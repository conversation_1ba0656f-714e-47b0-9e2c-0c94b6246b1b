import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ArrowLeft } from "@mui/icons-material";
import { Input, Radio, Button } from "antd";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import archiveIcon from "../../assets/images/archiveIcon.svg";
import { notificationMessage } from "../../helper/utilsFunc";
export default function SearchHeader(props) {
  const { t } = useTranslation('archive')
  const [checkValue, setCheckValue] = useState('file');
  const [searchWord, setSearchWord] = useState('');
  const searchWordPrevRef = React.useRef('');
  const checkValuePrevRef = React.useRef('file');
  React.useEffect(()=>{
    checkValuePrevRef.current = ''
  },[props.selectedNode])
  const onCheckType = (e) => {
    setCheckValue(e.target.value);
  };
  const handleChangeSearch =(e)=>{
    setSearchWord(e.target.value);
  }
  const handleSubmitSearch =()=>{
    if(props.selectedNode?.length>1 && searchWord){
    notificationMessage(t("chooseOneNoteFromTree", "", "bottomRight"))
    }
    else{
    if(checkValue!== checkValuePrevRef.current|| searchWord!==searchWordPrevRef.current){
      props.onSearchFilesOrFolders(checkValue, searchWord)
      searchWordPrevRef.current = searchWord;
      checkValuePrevRef.current = checkValue;
    }
    }
  }
  
  return (
    <div className="searchHeader">
      <p className="headTitle">
        {t('electronicArchive')}
        <img alt="icon" className="mx-2" src={archiveIcon} />
      </p>
      <Link to="/">
        <ArrowLeft/>
      </Link>{" "}
      
      <Button className="headBtnSearch" disabled={!(props?.archiveTreeElements?.length) && props.isGeoExplorer} onClick={handleSubmitSearch}>{t('search')}</Button>
      <div className="m-auto headCenter">
        <Radio.Group disabled={!(props?.archiveTreeElements?.length) && props.isGeoExplorer} onChange={onCheckType} value={checkValue}>
          <Radio value={'file'}>
        {t('file')}
          </Radio>
          <Radio value={'folder'}>{t('folder')}</Radio>
        </Radio.Group>
        <Input placeholder={t('enterSearchWord')} disabled={!(props?.archiveTreeElements?.length) && props.isGeoExplorer} onChange={handleChangeSearch} onPressEnter={handleSubmitSearch} value={searchWord} />
      </div>
    </div>
  );
}
