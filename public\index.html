<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/wildlifeexplorer/favicon.ico" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="مستكشف الراصد الفطري" content="مستكشف الراصد الفطري" />
    <link rel="apple-touch-icon" href="/wildlifeexplorer/logo.png" />
    <link rel="manifest" href="/wildlifeexplorer/manifest.json" />
    <link
      rel="stylesheet"
      href="https://use.fontawesome.com/releases/v5.1.0/css/all.css"
    />
    <link
      rel="stylesheet"
      href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css"
      integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk"
      crossorigin="anonymous"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
    />
    <link rel="stylesheet" href="css/App.css" id="cssStyleLink" />
    <title>مستكشف الراصد الفطري</title>
    <link href="/wildlifeexplorer/static/css/60.84ef4d6a.chunk.css" rel="stylesheet" />
    <link
      href="/wildlifeexplorer/static/css/main.b92b3af0.chunk.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <script type="text/javascript" src="/wildlifeexplorer/config.js"></script>
    <script type="text/javascript" src="/wildlifeexplorer/config-link.js"></script>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script
      src="https://unpkg.com/react/umd/react.production.min.js"
      crossorigin
    ></script>
    <script
      src="https://unpkg.com/react-dom/umd/react-dom.production.min.js"
      crossorigin
    ></script>
    <script
      src="https://unpkg.com/react-bootstrap@next/dist/react-bootstrap.min.js"
      crossorigin
    ></script>
   
    <script src="/wildlifeexplorer/static/js/60.86249480.chunk.js"></script>
    <script src="/wildlifeexplorer/static/js/main.e58b2ff5.chunk.js"></script>
  </body>
</html>
