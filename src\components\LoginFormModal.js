import { Button, Form, Input, Modal } from "antd";
import { useState } from "react";
import axios from "axios";
import { useTranslation } from "react-i18next";

export default function LoginFormModal({ isModalOpen, setIsModalOpen }) {
  const [username, setUserName] = useState("");
  const [password, setPassword] = useState("");
  const [loginError, showLoginError] = useState(false);

  const { t } = useTranslation("forms");

  const handleSubmit = (e) => {
    e.preventDefault();

    if (username && password) {
      axios
        .post(window.ApiUrl + "/auth", { username, password })
        .then(({ data }) => {
          showLoginError(false);
          localStorage.setItem("user", JSON.stringify(data));
          localStorage.setItem("token", data.token);
          window.location.reload();
        })
        .catch((error) => {
          showLoginError(true);
        });
    }
  };

  return (
    <Modal
      title={t("login")}
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      className="login-modal"
    >
      <Form onSubmit={handleSubmit}>
        <div
          style={{
            textAlign: localStorage.getItem("lang") === "ar" ? "right" : "left",
          }}
        >
          <label>{t("userName")}</label>
          <Form.Item
            name="userName"
            rules={[{ required: true, message: "يرجي ادخال اسم المستخدم" }]}
          >
            <Input
              type="userName"
              placeholder={t("userName")}
              value={username}
              onChange={(e) => setUserName(e.target.value)}
              className="px-3"
            />
          </Form.Item>
        </div>

        <div
          style={{
            textAlign: localStorage.getItem("lang") === "ar" ? "right" : "left",
          }}
        >
          <label>{t("password")}</label>
          <Form.Item
            name="password"
            rules={[{ required: true, message: "يرجي ادخال كلمة المرور" }]}
          >
            <Input
              type="password"
              placeholder={t("password")}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="px-3"
            />
          </Form.Item>
        </div>

        <Button
          type="primary"
          disabled={!username || !password}
          onClick={handleSubmit}
          style={{
            backgroundColor: "#b45333",
            display: "block",
            margin: "auto",
            color: "#fff",
          }}
        >
          {t("login")}
        </Button>

        {loginError && (
          <div
            className="error-message"
            style={{ color: "red", marginBlock: "7px", textAlign: "center" }}
          >
            يوجد خطأ في اسم المستخدم او كلمة المرور
          </div>
        )}
      </Form>
    </Modal>
  );
}
