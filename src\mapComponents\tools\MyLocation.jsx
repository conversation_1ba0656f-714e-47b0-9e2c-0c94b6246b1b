import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Resizable } from "re-resizable";
import { Fade } from "react-reveal";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import locationIcon from "../../assets/images/locateglobal.svg";
import { useEffect, useState } from "react";
import { addPictureSymbol, project } from "../../helper/common_func";
import Point from "@arcgis/core/geometry/Point";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import MapView from "@arcgis/core/views/MapView";
import Map from "@arcgis/core/Map";
import Draggable from "react-draggable";
import { useTranslation } from "react-i18next";

export default function MyLocation(props) {
  let map;
  let view;

  const { i18n } = useTranslation();

  useEffect(() => {
    map = new Map({
      basemap: "satellite",
    });

    view = new MapView({
      container: "mapOverviewDiv",
      map: map,
      ui: {
        components: ["attribution"],
      },
      extent: window.fullExtent,
    });

    view.ui._removeComponents(["attribution"]);
    map.view = view;
    view.on(
      ["click", "drag", "double-click", "mouse-wheel", "hold"],
      function (event) {
        event.stopPropagation();
      }
    );

    const graphicsLayer = new GraphicsLayer({
      id: "GraphicsLayer",
    });

    map.add(graphicsLayer);

    view.ui._removeComponents(["attribution"]);

    //addLocationOnMap();

    addExtentonMap();
  }, []);

  const addExtentonMap = () => {
    addPictureSymbol(
      props.map.view.extent.center,
      locationIcon,
      "GraphicsLayer",
      map,
      15,
      15
    );
  };

  const addLocationOnMap = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        var loc = new Point({
          longitude: position.coords.longitude,
          latitude: position.coords.latitude,
        });
        project([loc], 102100, (res) => {
          addPictureSymbol(res[0], locationIcon, "GraphicsLayer", map, 36, 36);
        });
      });
    }
  };

  const [x, setX] = useState(0);
  const [y, setY] = useState(0);

  const handleDrag = (e, position) => {
    setX(position.x);
    setY(position.y);
  };

  return (
    <>
      {/* <Fade left collapse> */}
      <Draggable
        position={{ x, y }}
        onDrag={handleDrag}
        bounds={{
          left:
            i18n.language === "ar"
              ? -(window.innerWidth - 400)
              : -window.innerWidth,
          top: -300,
          right: i18n.language === "ar" ? window.innerWidth - 400 : 0, // Subtract component width
          bottom: window.innerHeight - 600, // Subtract component height
        }}
      >
        <div
          className="toolsMenu inquiryTool layersMenu leftToolMenu"
          style={{
            overflow: "auto",
            height: "300px",
            maxHeight: "500px",
            position: "relative",
            top: "225px",
            minWidth: "400px",
            border: "4px solid #fff",
          }}
          // className="leftToolMenu"
          // defaultSize={{
          //   width: 400,
          //   height: "300",
          // }}
          // // minHeight={300}
          // maxWidth={800}
          // maxHeight={600}
          // bounds="window"
        >
          {/* <Fade left> */}
          <span
            style={{
              width: "100%",
              float: "left",
              textAlign: "left",
              marginLeft: "5px",
              marginTop: "-5px",
            }}
          >
            {" "}
            <FontAwesomeIcon
              icon={faTimes}
              style={{
                marginTop: "5px",
                marginRight: "5px",
                cursor: "pointer",
              }}
              onClick={(e) => {
                e.stopPropagation();
                props.closeToolsData();
              }}
            />
          </span>
          <>
            <div
              style={{
                height: "93%",
              }}
            >
              <div
                id="mapOverviewDiv"
                style={{
                  height: "100%",
                }}
              ></div>
            </div>
          </>
          {/* </Fade> */}
        </div>
      </Draggable>

      {/* </Fade> */}
    </>
  );
}
