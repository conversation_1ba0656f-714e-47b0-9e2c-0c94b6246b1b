import { Input, message, Modal, Select, Switch, Upload } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaMap, FaTrashAlt } from "react-icons/fa";
import { MdKeyboardArrowDown } from "react-icons/md";

import descImg from "../../assets/images/study-actor/desc.svg";
import calenderImg from "../../assets/images/study-actor/Calendar.svg";
import userImg from "../../assets/images/study-actor/User.svg";
import attachementsImg from "../../assets/images/study-actor/attachements.svg";
import pathsImg from "../../assets/images/study-actor/account_circle.svg";
import mapImg from "../../assets/images/study-actor/gis_map-route.svg";
import monitoringImage from "../../assets/images/study-actor/monitoring.svg";
import { RiArrowDropDownFill } from "react-icons/ri";

import dummy_map_img from "../../assets/images/dashboard/dashMap.png";
import {
  convertTimeStampToDate,
  formatDayMonth,
  getLayerId,
  getMapInfo,
  getTimeDifferenceFromTimeStamp,
  queryTask,
  showLoading,
  studyTotalTimeString,
  zoomToFeatures,
} from "../../helper/common_func";
import MapPreview from "./MapPreview";
import Graphic from "@arcgis/core/Graphic";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import axios from "axios";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";

export default function StudyDetails({
  setActiveScreen,
  screens,
  data,
  language,
  map,
  setPathDetailsData,
  setMonitorCaseDetailsData,
  userObject,
  is_mission_member,
  activeTabId,
  montioringLayersNames,
}) {
  console.log("activeTabId", activeTabId);
  console.log("data", data);
  const is_user_team_leader = data.assigned_to.some(
    (user) => user.user_id == userObject.id && user.is_manager
  );
  const [pathsList, setPathsList] = useState([]);
  const [pathsMonitoringCasesList, setPathsMonitoringCasesList] = useState([]);
  const [monitoringCasesList, setMonitoringCasesList] = useState([]);

  const drawPaths = async (pathsFeaturesList) => {
    await getMapInfo(window.mobileAppUrl).then(async (mapInfo) => {
      let pathId = getLayerId(mapInfo, "SIGHTING_TOURPATH");
      const sourceFeatureLayer = new FeatureLayer({
        url: `${window.mobileAppUrl}/${pathId} `,
      });
      const pathsLayer = map.findLayerById("PathsGraphicLayer");
      if (!pathsLayer || !(pathsLayer instanceof GraphicsLayer)) {
        console.error(
          `GraphicsLayer with id "PathsGraphicLayer" not found or is not a GraphicsLayer.`
        );
        return;
      }
      pathsLayer.removeAll();
      await sourceFeatureLayer.load();
      const renderer = sourceFeatureLayer.renderer;

      let pathsGraphics = [];

      pathsFeaturesList.forEach((path) => {
        const pathGraphic = path;
        pathGraphic.symbol = renderer.symbol;
        pathsGraphics.push(pathGraphic);
      });
      pathsLayer.addMany(pathsGraphics);

      zoomToFeatures(pathsGraphics, map);
    });
  };

  const queryTaskPromise = (settings) => {
    return new Promise((resolve, reject) => {
      queryTask({
        ...settings,
        callbackResult: (data) => resolve(data),
        callbackError: (error) => reject(error),
        notShowLoading: true,
      });
    });
  };

  const getPaths = async (mapInfo) => {
    let taskGUIDs = [];
    let pathId = getLayerId(mapInfo, "SIGHTING_TOURPATH");
    await queryTask({
      url: window.mobileAppUrl + "/" + pathId,
      where: `TASK_ID = ${data.id}`,
      returnGeometry: true,
      callbackResult: async ({ features }) => {
        if (features.length) {
          console.log("paths list features are", features);
          if (features.length > 0) {
            await drawPaths(features);
            setPathsList(features);
            features.forEach((feature) => {
              if (feature.attributes.TASK_GUID)
                taskGUIDs.push(feature.attributes.TASK_GUID);
            });
          }
        }
        await getMontioringCases(mapInfo, taskGUIDs);
      },
      callbackError(error) {
        message.warning(t("ErrorOccurd"));
      },
    });
  };

  const getMontioringCases = async (mapInfo, taskGUIDs) => {
    console.log("task guids are", taskGUIDs);

    let promiseQueries = montioringLayersNames.map((layerName) => {
      let layerId = getLayerId(mapInfo, layerName);
      return queryTaskPromise({
        url: `${window.mobileAppUrl}/${layerId}`,
        where: `TASK_ID = ${data.id}`,
        returnGeometry: true,
      });
    });

    await Promise.all(promiseQueries).then((results) => {
      console.log("promise queries result", results);
      results.forEach((result, index) => {
        if (result.features.length > 0) {
          if (taskGUIDs.length > 0) {
            result.features.forEach((feature) => {
              if (taskGUIDs.includes(feature.attributes.TASK_GUID)) {
                setPathsMonitoringCasesList((prev) => {
                  return [
                    ...prev,
                    {
                      task_guid: feature.attributes.TASK_GUID,
                      feature: feature,
                      ownedLayerName: montioringLayersNames[index],
                    },
                  ];
                });
              } else {
                setMonitoringCasesList((prev) => {
                  return [
                    ...prev,
                    {
                      feature: feature,
                      ownedLayerName: montioringLayersNames[index],
                    },
                  ];
                });
              }
            });
          } else {
            result.features.forEach((feature) => {
              setMonitoringCasesList((prev) => {
                return [
                  ...prev,
                  {
                    feature: feature,
                    ownedLayerName: montioringLayersNames[index],
                  },
                ];
              });
            });
          }
        }
      });

      showLoading(false);
    });
  };

  const drawFieldGraphics = (stringfiedGraphics, layerName) => {
    let jsonGraphicsArray;
    try {
      jsonGraphicsArray = JSON.parse(stringfiedGraphics);
    } catch (err) {
      console.error("Invalid graphics JSON string", err);
      return;
    }
    const layer = map.findLayerById(layerName);
    if (!layer || !(layer instanceof GraphicsLayer)) {
      console.error(
        `GraphicsLayer with id "${layerName}" not found or is not a GraphicsLayer.`
      );
      return;
    }

    layer.removeAll();

    jsonGraphicsArray.forEach((graphicData) => {
      console.log("graphicData", graphicData);
      const graphic = Graphic.fromJSON(graphicData);
      layer.add(graphic);
    });

    zoomToFeatures(jsonGraphicsArray, map);
  };

  useEffect(() => {
    console.log("is team leader", is_user_team_leader);
    const fetchData = async () => {
      showLoading(true);
      await getMapInfo(window.mobileAppUrl).then(async (mapInfo) => {
        await getPaths(mapInfo);
      });
    };

    fetchData();
    if (data.graphics) {
      drawFieldGraphics(data.graphics, "FieldMissionGraphicLayer");
    }
  }, []);

  const { t } = useTranslation("sidemenu");
  const [showPaths, setShowPaths] = useState(true);
  const [showMonitoringCases, setShowMonitoringCases] = useState(true);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const MissionCommentsDisplay = ({
    assignedToData,
    userObject,
    onSubmitNote,
  }) => {
    const [noteInput, setNoteInput] = useState("");
    console.log("assigned to data", assignedToData);
    console.log("userObject", userObject);

    const userAssignment = assignedToData.find(
      (item) => item.user_id === userObject.id
    );

    if (!userAssignment && is_mission_member) {
      return (
        <div style={{ color: "#fff", textAlign: "center", padding: "20px" }}>
          لا يوجد مهمة مخصصة لهذا المستخدم
        </div>
      );
    }

    let comments;

    if (userAssignment && is_mission_member) {
      comments = userAssignment.comments || [];
    } else {
      comments = [...assignedToData.map((user) => user.comments)];
      comments = comments[0];
    }

    console.log("comments", comments);

    const handleSubmitNote = () => {
      if (noteInput.trim() === "") return;

      const newNote = {
        comment: noteInput,
        mission_id: data.id,
        user_id: is_mission_member ? null : assignedToData[0].user_id,
      };

      if (onSubmitNote) {
        onSubmitNote(newNote);
      }

      setNoteInput("");
    };

    if (comments.length === 0) {
      return (
        <div style={{ color: "#fff", textAlign: "center", padding: "20px" }}>
          لا يوجد ملاحظات
          <div>
            <label className="selectLabelStyle">
              {t("studiesActor.enter_notes")}
            </label>
            <input
              placeholder={t("studiesActor.enter_notes")}
              value={noteInput}
              onChange={(e) => setNoteInput(e.target.value)}
              style={{
                width: "100%",
                padding: "10px",
                borderRadius: "5px",
                border: "1px solid #ccc",
                marginTop: "5px",
              }}
            />
          </div>
          <button
            className="SearchBtn"
            onClick={handleSubmitNote}
            disabled={noteInput.trim() === ""}
          >
            {t("studiesActor.send")}
          </button>
        </div>
      );
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
        }}
      >
        {/* Display all comments */}
        {comments.map((comment, index) => (
          <div
            key={index}
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}
          >
            <div
              style={{
                textAlign: "start",
                display: "flex",
                flexDirection: "column",
                gap: "10px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "5px",
                    color: "#fff",
                  }}
                >
                  <span
                    style={{
                      fontSize: "16px",
                      fontWeight: "400",
                    }}
                  >
                    {is_mission_member
                      ? comment.commnent_by === userObject.id
                        ? t("studiesActor.observer")
                        : t("studiesActor.reviewer")
                      : comment.commnent_by === userObject.id
                      ? t("studiesActor.reviewer")
                      : t("studiesActor.observer")}
                  </span>
                  <span
                    style={{
                      fontSize: "11px",
                      fontWeight: "300",
                    }}
                  >
                    {comment.created_at}
                  </span>
                </div>
              </div>

              <div
                style={{
                  color: "#fff",
                  fontSize: "16px",
                  fontWeight: "400",
                  backgroundColor: "#333c",
                  borderRadius: "10px",
                  padding: "10px",
                }}
              >
                {comment.comment}
              </div>
            </div>
          </div>
        ))}

        {/* Add new comment section */}
        <div>
          <label className="selectLabelStyle">
            {t("studiesActor.enter_notes")}
          </label>
          <input
            placeholder={t("studiesActor.enter_notes")}
            value={noteInput}
            onChange={(e) => setNoteInput(e.target.value)}
            style={{
              width: "100%",
              padding: "10px",
              borderRadius: "5px",
              border: "1px solid #ccc",
              marginTop: "5px",
            }}
          />
        </div>

        <button
          className="SearchBtn"
          onClick={handleSubmitNote}
          disabled={noteInput.trim() === ""}
        >
          {t("studiesActor.send")}
        </button>
      </div>
    );
  };

  const handleNoteSubmission = async (newNote) => {
    console.log("New note submitted:", newNote);
    const url = `${window.ApiUrl}FieldMission/review-feedback`;

    await axios
      .put(url, newNote, {
        headers: {
          "Content-Type": "application/json",
        },
      })
      .then((response) => {
        console.log("response", response);
        handleCancel();
        setActiveScreen(screens[0]);
      })
      .catch((err) => {
        console.log("err", err);
      });
  };

  const handleClickForUnderApproval = async () => {
    await axios
      .put(
        `${window.ApiUrl}FieldMission/review-accept?userId=${data.assigned_to[0].user_id}&missionId=${data.assigned_to[0].mission_id}`
      )
      .then((response) => {
        message.success(t("studiesActor.study_confirmed"));
        setActiveScreen(screens[0]);
      });
  };

  const handleClickForConfirmation = async () => {
    await axios
      .put(
        `${window.ApiUrl}FieldMission/gis-approve?userId=${data.assigned_to[0].user_id}&missionId=${data.assigned_to[0].mission_id}`
      )
      .then((response) => {
        message.success(t("studiesActor.study_accepted"));
        setActiveScreen(screens[0]);
      });
  };

  const handleClickForfreeze = async () => {
    const formData = {
      mission_id: data.assigned_to[0].mission_id,
      is_freezed: true,
    };
    await axios
      .put(`${window.ApiUrl}FieldMission/mission-toggle-freeze`, formData)
      .then((response) => {
        message.success(t("studiesActor.study_freezed"));
        setActiveScreen(screens[0]);
      });
  };

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "10px",
      }}
    >
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "8px 12px",
          display: "flex",
          flexDirection: "column",
          gap: "15px",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div style={{ fontWeight: "700", fontSize: "16px" }}>
            {language == "ar" ? data.name : data.name_en}
            {/* {t("studiesActor.trip_name_placeholder")} */}
          </div>
          <div
            style={{
              backgroundColor: "#EEEEEE66",
              padding: "5px 20px",
              borderRadius: "5px",
              flexShrink: 0,
            }}
          >
            {data.number}
          </div>
        </div>

        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "start",
            gap: "10px",
          }}
        >
          <img src={descImg} alt="" />
          <div>
            <div style={{ fontWeight: "700", fontSize: "16px" }}>
              {t("studiesActor.description_label")}
            </div>
            <div style={{ textAlign: "start" }}>
              {/* {t("studiesActor.description_text")} */}
              {language == "ar" ? data.description : data.description_en}
            </div>
          </div>
        </div>

        {(data.start_date || data.end_date) && (
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "start",
              gap: "10px",
            }}
          >
            <img src={calenderImg} alt="" />
            <div>
              <div style={{ fontWeight: "700", fontSize: "16px" }}>
                {t("studiesActor.duration")}
              </div>
              <div style={{ textAlign: "start" }}>
                {data.start_date && t("from")}{" "}
                {/* {formatDayMonth(data.start_date, language)}{" "} */}
                {data.start_date} {data.end_date && t("to")}{" "}
                {/* {formatDayMonth(data.end_date, language)} */}
                {data.end_date}
              </div>
            </div>
          </div>
        )}
        {data.manager_name && (
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "start",
              gap: "10px",
            }}
          >
            <img src={userImg} alt="" />
            <div>
              <div style={{ fontWeight: "700", fontSize: "16px" }}>
                {t("studiesActor.manager_label")}
              </div>
              <div style={{ textAlign: "start" }}>{data.manager_name}</div>
            </div>
          </div>
        )}
      </div>

      {data.assigned_to && data.assigned_to.length > 0 && (
        <div
          className="generalSearchCardWithoutHover"
          style={{
            padding: "16px",
            display: "flex",
            flexDirection: "row",
            alignItems: "start",
            gap: "10px",
          }}
        >
          <img src={userImg} alt="" />
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}
          >
            <div style={{ fontWeight: "700", fontSize: "16px" }}>
              {t("studiesActor.team")}
            </div>

            {data.manager_name && (
              <div>
                <div style={{ fontWeight: "700", fontSize: "16px" }}>
                  {t("studiesActor.manager_label")}
                </div>
                <div style={{ textAlign: "start" }}>{data.manager_name}</div>
              </div>
            )}

            <div>
              <div style={{ fontWeight: "700", fontSize: "16px" }}>
                {t("studiesActor.team_members")}
              </div>
              {data.assigned_to &&
                data.assigned_to.length > 0 &&
                data.assigned_to.map((item, index) => (
                  <div key={index} style={{ textAlign: "start" }}>
                    {!item.is_manager && item.user_fullname}
                  </div>
                ))}

              {/* <div style={{ textAlign: "start" }}>ياسر الدوسري</div> */}
            </div>
          </div>
        </div>
      )}

      {data.attachments && data.attachments.length > 0 && (
        <div
          className="generalSearchCardWithoutHover"
          style={{
            padding: "8px 12px",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "start",
              gap: "10px",
            }}
          >
            <img src={attachementsImg} alt="" />
            <div
              style={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                gap: "10px",
              }}
            >
              <div style={{ fontWeight: "700", fontSize: "16px" }}>
                {t("studiesActor.attachments")}
              </div>
              {data.attachments.map((attachement, index) => {
                return (
                  <div
                    key={index}
                    style={{
                      color: "#fff",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      gap: "10px",
                      padding: "5px 10px",
                      backgroundColor: "rgb(255 255 255 / 40%)",
                      borderRadius: "5px",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "10px",
                        flexShrink: "0",
                      }}
                      onClick={() => {
                        console.log("paths list are", pathsList);
                        console.log(
                          "paths monitoring cases list are",
                          pathsMonitoringCasesList
                        );
                        console.log(
                          "monitoring cases list are",
                          monitoringCasesList
                        );
                        window.open(
                          `${window.filesURL}${attachement.file_url}`,
                          "_blank"
                        );
                      }}
                    >
                      <FaMap />
                      <div
                        style={{
                          maxWidth: "200px",
                          wordWrap: "break-word",
                        }}
                      >
                        {attachement.file_name}
                      </div>
                    </div>
                    {/* <FaTrashAlt style={{ cursor: "pointer" }} /> */}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {data.maps && data.maps.length > 0 && (
        <div
          className="generalSearchCardWithoutHover"
          style={{
            padding: "8px 12px",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "start",
              gap: "10px",
            }}
          >
            <img src={attachementsImg} alt="" />
            <div
              style={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                gap: "10px",
              }}
            >
              <div style={{ fontWeight: "700", fontSize: "16px" }}>
                {t("studiesActor.maps")}
              </div>

              {data.maps.map((item, index) => (
                <div
                  key={index}
                  style={{
                    background: "#F4E5E1",
                    padding: "8px",
                    borderRadius: "8px",
                    borderBottom: "2px solid #B45333",
                  }}
                >
                  <img
                    src={
                      item.offline_map.thumbnail
                        ? item.offline_map.thumbnail
                        : dummy_map_img
                    }
                    alt=""
                    style={{
                      height: "50px",
                      borderRadius: "8px",
                      width: "100%",
                      objectFit: "cover",
                    }}
                  />
                  {/* <MapPreview featureServiceUrl={item.feature_service} /> */}
                  <div
                    style={{
                      color: "#85736E",
                      textAlign: "center",
                      fontWeight: "400",
                      fontSize: "12px",
                    }}
                  >
                    {language == "ar"
                      ? item.offline_map.title_ar
                      : item.offline_map.title_en}
                    {/* {t("studiesActor.sample_map_name")} */}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* start paths */}
      {
        <div className="box">
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              color: "#fff",
              marginBottom: "10px",
              cursor: "pointer",
            }}
            onClick={() => setShowPaths(!showPaths)}
          >
            <div
              style={{
                display: "flex",
                gap: "5px",
                alignItems: "center",
              }}
            >
              <img src={pathsImg} alt="" />
              <div style={{ fontWeight: "700", fontSize: "16px" }}>
                {t("studiesActor.my_paths")}
              </div>
            </div>

            <div
              style={{
                display: "flex",
                gap: "5px",
                alignItems: "center",
              }}
            >
              <MdKeyboardArrowDown
                size={20}
                style={{
                  // cursor: "pointer",
                  transform: `rotate(${!showPaths ? "180deg" : 0})`,
                }}
                // onClick={() => setShowPaths(!showPaths)}
              />
            </div>
          </div>

          {showPaths && (
            <div
              style={{
                display: "flex",
                gap: "10px",
                flexDirection: "column",
              }}
            >
              {pathsList.length > 0 ? (
                pathsList.map((item, index) => {
                  return (
                    <div
                      key={index}
                      className="generalSearchCard"
                      style={{
                        display: "flex",
                        alignItems: "statrt",
                        justifyContent: "space-between",
                        gap: "15px",
                        padding: "16px",
                      }}
                      onClick={() => {
                        console.log("item path clicked", item);
                        let pathMonitoringCasesData =
                          pathsMonitoringCasesList.filter(
                            (montioringCase) =>
                              montioringCase.task_guid ==
                              item.attributes.TASK_GUID
                          );
                        console.log(
                          "pathMonitoringCasesData",
                          pathMonitoringCasesData
                        );

                        let pathDetailsData = {
                          pathData: item,
                          monitoringCases: [...pathMonitoringCasesData],
                        };
                        console.log("pathDetailsData", pathDetailsData);
                        setPathDetailsData(pathDetailsData);
                        setActiveScreen(screens[2]);
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          gap: "10px",
                          alignItems: "start",
                        }}
                      >
                        <img src={mapImg} alt="" />
                        <div
                          style={{
                            flex: 1,
                            display: "flex",
                            flexDirection: "column",
                            gap: "10px",
                          }}
                        >
                          {item.attributes.PATH_NAME && (
                            <div>{item.attributes.PATH_NAME}</div>
                          )}
                          <div
                            style={{
                              display: "flex",
                              gap: "10px",
                              fontSize: "12px",
                              flexWrap: "wrap",
                            }}
                          >
                            {item.attributes.CREATED_DATE && (
                              <div>
                                {convertTimeStampToDate(
                                  item.attributes.CREATED_DATE,
                                  false
                                )}
                              </div>
                            )}
                            {item.attributes.START_TIME &&
                              item.attributes.END_TIME && (
                                <div>
                                  {studyTotalTimeString(
                                    item.attributes.END_TIME,
                                    item.attributes.START_TIME,
                                    language
                                  )}
                                </div>
                              )}
                            {item.attributes.DISTANCE_KM && (
                              <div>
                                {`(${item.attributes.DISTANCE_KM.toFixed(2)})`}
                                {language == "ar" ? " كم" : " KM"}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div>
                        <FaTrashAlt style={{ cursor: "pointer" }} />
                      </div>
                    </div>
                  );
                })
              ) : (
                <div
                  style={{
                    color: "#fff",
                    fontSize: "14px",
                    textAlign: "center",
                  }}
                >
                  {t("studiesActor.no_paths_list")}
                </div>
              )}
            </div>
          )}
        </div>
      }
      {/* end paths */}

      {/* start monitoring cases */}
      {
        <div className="box">
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              color: "#fff",
              marginBottom: "10px",
              cursor: "pointer",
            }}
            onClick={() => setShowMonitoringCases(!showMonitoringCases)}
          >
            <div
              style={{
                display: "flex",
                gap: "5px",
                alignItems: "center",
              }}
            >
              <img src={pathsImg} alt="" />
              <div style={{ fontWeight: "700", fontSize: "16px" }}>
                {t("studiesActor.monitoring_cases")}
              </div>
            </div>

            <div
              style={{
                display: "flex",
                gap: "5px",
                alignItems: "center",
              }}
            >
              <MdKeyboardArrowDown
                size={20}
                style={{
                  // cursor: "pointer",
                  transform: `rotate(${!showMonitoringCases ? "180deg" : 0})`,
                }}
                // onClick={() => setShowMonitoringCases(!showMonitoringCases)}
              />
            </div>
          </div>

          {showMonitoringCases && (
            <div
              style={{
                display: "flex",
                gap: "10px",
                flexDirection: "column",
              }}
            >
              {monitoringCasesList.length > 0 ? (
                monitoringCasesList.map((item, index) => {
                  return (
                    <div
                      key={index}
                      className="generalSearchCard"
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "15px",
                        padding: "16px",
                      }}
                      onClick={() => {
                        console.log("monitor case item clicked", item);
                        setMonitorCaseDetailsData(item);
                        setActiveScreen(screens[3]);
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          gap: "10px",
                          alignItems: "center",
                        }}
                      >
                        <img
                          src={
                            item.feature.attributes.PHOTOS
                              ? `${window.filesURL}${
                                  item.feature.attributes.PHOTOS.split(",")[0]
                                }`
                              : monitoringImage
                          }
                          alt=""
                          style={{
                            width: "50px",
                            height: "50px",
                            objectFit: "cover",
                          }}
                        />
                        <div
                          style={{
                            flex: 1,
                            display: "flex",
                            flexDirection: "column",
                            gap: "5px",
                          }}
                        >
                          {item.feature.attributes.INCIDENT_DATE && (
                            <div style={{ fontSize: "14px" }}>
                              {convertTimeStampToDate(
                                item.feature.attributes.INCIDENT_DATE,
                                false
                              )}
                            </div>
                          )}
                          {item.feature.attributes.SIGHTING_DATE && (
                            <div style={{ fontSize: "14px" }}>
                              {convertTimeStampToDate(
                                item.feature.attributes.SIGHTING_DATE,
                                false
                              )}
                            </div>
                          )}
                          {((language == "ar" &&
                            item.feature.attributes.AR_INCIDENT_TYPE) ||
                            (language == "en" &&
                              item.feature.attributes.EN_INCIDENT_TYPE) ||
                            (language == "ar" &&
                              item.feature.attributes.AR_SITE_TYPE) ||
                            (language == "en" &&
                              item.feature.attributes.EN_SITE_TYPE) ||
                            (language == "ar" &&
                              item.feature.attributes.AR_NCW_CATEGORY) ||
                            (language == "en" &&
                              item.feature.attributes.EN_NCW_CATEGORY)) && (
                            <div style={{ fontSize: "12px" }}>
                              {language == "ar"
                                ? item.feature.attributes.AR_INCIDENT_TYPE
                                  ? item.feature.attributes.AR_INCIDENT_TYPE
                                  : item.feature.attributes.AR_SITE_TYPE
                                  ? item.feature.attributes.AR_SITE_TYPE
                                  : item.feature.attributes.AR_NCW_CATEGORY
                                : item.feature.attributes.EN_INCIDENT_TYPE
                                ? item.feature.attributes.EN_INCIDENT_TYPE
                                : item.feature.attributes.EN_SITE_TYPE
                                ? item.feature.attributes.EN_SITE_TYPE
                                : item.feature.attributes.EN_NCW_CATEGORY}
                            </div>
                          )}
                          {item.feature.attributes.INCIDENT_DATE && (
                            <div style={{ fontSize: "11px" }}>
                              {convertTimeStampToDate(
                                item.feature.attributes.INCIDENT_DATE
                              )}
                            </div>
                          )}
                          {item.feature.attributes.SIGHTING_DATE && (
                            <div style={{ fontSize: "11px" }}>
                              {convertTimeStampToDate(
                                item.feature.attributes.SIGHTING_DATE
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div
                  style={{
                    color: "#fff",
                    fontSize: "14px",
                    textAlign: "center",
                  }}
                >
                  {t("studiesActor.no_monitoring_cases_list")}
                </div>
              )}
            </div>
          )}
        </div>
      }
      {/* end monitoring cases */}

      {/* start share */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
          display: "flex",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        {/* <Select
          showSearch
          placeholder={t("studiesActor.share_placeholder")}
          optionFilterProp="label"
          suffixIcon={<RiArrowDropDownFill size={30} />}
          mode="multiple"
          options={[
            {
              value: "jack",
              label: "Jack",
            },
            {
              value: "lucy",
              label: "Lucy",
            },
            {
              value: "tom",
              label: "Tom",
            },
          ]}
        /> */}

        <button className="SearchBtn">{t("studiesActor.share")}</button>
        {userObject.is_mission_reviewer && (
          <button className="SearchBtn" onClick={showModal}>
            {t("studiesActor.return")}
          </button>
        )}
        {userObject.is_mission_reviewer && (
          <button onClick={handleClickForUnderApproval} className="SearchBtn">
            {t("studiesActor.confirm")}
          </button>
        )}
        {userObject.is_mission_gis_approval && (
          <button onClick={handleClickForConfirmation} className="SearchBtn">
            {t("studiesActor.accept")}
          </button>
        )}
        {is_user_team_leader && (
          <button onClick={handleClickForfreeze} className="SearchBtn">
            {t("studiesActor.freeze")}
          </button>
        )}
        {is_mission_member && activeTabId == 1 && (
          <button className="SearchBtn" onClick={showModal}>
            {t("studiesActor.send_for_review")}
          </button>
        )}

        {/* <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <label htmlFor="switch-1">عرض هذا المسار فقط على الخريطة</label>
          <Switch defaultChecked id="switch-1" style={{ width: "45px" }} />
        </div>

        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <label htmlFor="switch-2">عرض المسار على الخريطة</label>
          <Switch defaultChecked id="switch-2" />
        </div> */}
      </div>
      {/* end share */}

      {/* <div>
        <label className="selectLabelStyle">
          {t("studiesActor.send_to_label")}
        </label>
        <Input placeholder={t("studiesActor.send_to_placeholder")} readOnly />
      </div> */}

      {/* <button className="SearchBtn">{t("studiesActor.send_for_review")}</button> */}

      {/* <Modal
        className="study-actor-modal"
        title={t("studiesActor.return")}
        closable={{ "aria-label": "Custom Close Button" }}
        open={isModalOpen}
        // open={true}
        onOk={handleOk}
        onCancel={handleCancel}
        width={"500px"}
        height={"500px"}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "10px",
          }}
        >
          <div>
            <label className="selectLabelStyle">
              {t("studiesActor.enter_notes")}
            </label>
            <Input placeholder={t("studiesActor.enter_notes")} />
          </div>

          <button className="SearchBtn">{t("studiesActor.send")}</button>
        </div>
      </Modal> */}

      <Modal
        className="study-actor-modal"
        title={t("studiesActor.notes")}
        closable={{ "aria-label": "Custom Close Button" }}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={"500px"}
        height={"500px"}
      >
        <>
          <MissionCommentsDisplay
            assignedToData={data.assigned_to}
            userObject={userObject}
            onSubmitNote={async (newNote) =>
              await handleNoteSubmission(newNote)
            }
          />
        </>
      </Modal>
    </div>
  );
}
