import React, { useState, useEffect } from "react";
import { Input, Select } from "antd";
import Checkbox from "antd/lib/checkbox/Checkbox";
import { saveAs } from "file-saver";
import { withTranslation } from "react-i18next";
import { RiArrowDropDownFill } from "react-icons/ri";

import Extent from "@arcgis/core/geometry/Extent";
import Point from "@arcgis/core/geometry/Point";
import SpatialReference from "@arcgis/core/geometry/SpatialReference";
import Graphic from "@arcgis/core/Graphic";
import LegendLayer from "@arcgis/core/rest/support/LegendLayer";
import PrintTemplate from "@arcgis/core/rest/support/PrintTemplate";
import PictureMarkerSymbol from "@arcgis/core/symbols/PictureMarkerSymbol";
import PrintTask from "@arcgis/core/tasks/PrintTask";
import PrintParameters from "@arcgis/core/tasks/support/PrintParameters";
import logoImage from "../../assets/images/logo.svg";
import northArrowImage from "../../assets/images/northarrow.png";
import { showLoading } from "../../helper/common_func";
import axios from "axios";

const PrintComponent = ({ map, t }) => {
  const [extenstionType, setExtenstionType] = useState(null);
  const [printTitle, setPrintTitle] = useState("");
  const [layoutSize, setLayoutSize] = useState(null);
  const [layoutOrientation, setLayoutOrientation] = useState(null);
  const [validateForm, setValidateForm] = useState(false);

  const layoutSizes = [
    { key: "A3", name: "A3" },
    { key: "A4", name: "A4" },
  ];

  const layoutOrientations = [
    { key: "Landscape", name: "Landscape" },
    { key: "Portrait", name: "Portrait" },
  ];

  useEffect(() => {
    return () => {
      const customEvent = new CustomEvent("showPrintBox", {
        detail: { show: false },
      });
      document.dispatchEvent(customEvent);
    };
  }, []);

  const selectChange = (name) => (e) => {
    if (name === "layoutSize") {
      setLayoutSize(e);
    } else if (name === "layoutOrientation") {
      setLayoutOrientation(e);
    }
  };

  const exportMap = (res) => {
    showLoading(false);
    let toPrint;
    if (res.url.indexOf(".pdf") > -1) {
      toPrint = window.open(res.url);
    } else {
      toPrint = window.open("", "", "width=2500,height=2000");
      if (toPrint) {
        const img = new Image();
        img.src = res.url;
        img.onload = () => {
          toPrint.print();
          toPrint.close();
        };
        toPrint.document.body.appendChild(img);
        toPrint.document.close();
        toPrint.focus();
      }
    }
  };

  const saveMap = async (res) => {
    const blob = await fetch(res.url).then((r) => r.blob());
    showLoading(false);
    saveAs(blob, printTitle);
  };

  const printMap = async (callbackResult) => {
    setValidateForm(true);

    if (layoutSize && layoutOrientation) {
      let user = JSON.parse(localStorage.getItem("user"));
      let userName = user?.name;

      const printTask = new PrintTask({
        url: window.customPrintUrl + "?token=" + window.esriToken,
      });

      const customTemplate = new PrintTemplate({
        format: "PDF",
        layout: layoutSize + "_" + layoutOrientation,
        layoutOptions: {
          titleText: userName || " ",
        },
      });

      const printParams = new PrintParameters({
        view: map.view,
        template: customTemplate,
      });

      showLoading(true);

      let excludeLayers = await axios.get(
        `${window.ApiUrl}api/ExcludeLayers/GetAll`
      );

      showLoading(false);

      showLoading(true);

      window.__neglectLegendLayers =
        excludeLayers.data?.map((x) => x.esri_layer_id) || [];

      printTask.execute(printParams).then(
        (e) => {
          callbackResult(e);
        },
        (error) => {
          showLoading(false);
        }
      );
    }
    return;
  };

  return (
    <div className="printStyle">
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "10px",
          overflow: "auto",
          padding: "5px",
        }}
      >
        <p style={{ margin: "0" }} className="pTextAlign">
          {t("layoutSize")}
        </p>
        <Select
          virtual={false}
          suffixIcon={<RiArrowDropDownFill size={30} />}
          className="searchInput englishFont"
          showSearch
          allowClear
          style={{
            borderColor: !layoutSize && validateForm ? "red" : "black",
            borderRadius: "5px",
            border: !layoutSize && validateForm ? "1px solid #ff1313" : "none",
          }}
          onChange={selectChange("layoutSize")}
          value={layoutSize}
          placeholder={t("layoutSize")}
          getPopupContainer={(trigger) => trigger.parentNode}
          optionFilterProp="v"
          filterOption={(input, option) => option.v.indexOf(input) >= 0}
        >
          {layoutSizes.map((m) => (
            <Select.Option v={m.name} key={m.key} value={m.key}>
              {m.name}
            </Select.Option>
          ))}
        </Select>
        {validateForm && !layoutSize && (
          <div style={{ textAlign: "center" }}>
            <label className="requiredPrint">-{t("layoutSize")}-</label>
          </div>
        )}

        <>
          <p style={{ margin: "0" }} className="pTextAlign">
            {t("layoutOrientation")}
          </p>
          <Select
            virtual={false}
            suffixIcon={<RiArrowDropDownFill size={30} />}
            className="searchInput englishFont"
            showSearch
            allowClear
            style={{
              borderColor: !layoutOrientation && validateForm ? "red" : "black",
              borderRadius: "5px",
              border:
                !layoutOrientation && validateForm
                  ? "1px solid #ff1313"
                  : "none",
            }}
            onChange={selectChange("layoutOrientation")}
            value={layoutOrientation}
            placeholder={t("layoutOrientation")}
            getPopupContainer={(trigger) => trigger.parentNode}
            optionFilterProp="v"
            filterOption={(input, option) => option.v.indexOf(input) >= 0}
          >
            {layoutOrientations.map((m) => (
              <Select.Option v={m.name} key={m.key} value={m.key}>
                {m.name}
              </Select.Option>
            ))}
          </Select>
          {validateForm && !layoutOrientation && (
            <div style={{ textAlign: "center" }}>
              <label className="requiredPrint">
                - {t("layoutOrientation")} -
              </label>
            </div>
          )}
        </>
      </div>

      <div style={{ display: "flex", gap: "15px", marginTop: "20px" }}>
        <button
          className="SearchBtn"
          size="large"
          type="button"
          onClick={() => printMap(saveMap)}
        >
          {t("save")}
        </button>

        <button
          className="SearchBtn"
          size="large"
          type="button"
          onClick={() => printMap(exportMap)}
        >
          {t("print")}
        </button>
      </div>
    </div>
  );
};

export default withTranslation("print")(PrintComponent);
