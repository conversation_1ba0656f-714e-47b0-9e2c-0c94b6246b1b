import React, { useState, useEffect } from "react";
//import Style
//import Packages
import { Navbar, Container } from "react-bootstrap";
import Extent from "@arcgis/core/geometry/Extent";
import logo from "../../../assets/images/logo.svg";
import visionLogo from "../../../assets/images/vision.png";

import { useTranslation } from "react-i18next";
import {
  clearGraphics,
  getLayerId,
  highlightFeature,
  queryTask,
} from "../../../helper/common_func";
import { notificationMessage } from "../../../helper/utilsFunc";
import HijriCalenderComp from "./CalenderComp";
import SelectComp from "./SelectComp";
import FloatInput from "./FloatLabel";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowAltCircleDown,
  faArrowCircleUp,
} from "@fortawesome/free-solid-svg-icons";

export default function DashHeader(props) {
  const { t, i18n } = useTranslation("dashboard");

  const [timeContexts] = useState([
    {
      name: t("yearly"),
      type: "yearly",
    },
    {
      name: t("monthly"),
      type: "monthly",
    },
    {
      name: t("daily"),
      type: "daily",
    },
  ]);
  const WHOLE_BOUND_TYPES = [
    {
      name: t("mainMunicipalities"),
      value: "MUNICIPALITY_NAME",
    },
    {
      name: t("secMunicipalities"),
      value: "SUB_MUNICIPALITY_NAME",
    },
    {
      name: t("plans"),
      value: "PLAN_NO",
    },
    {
      name: t("districts"),
      value: "DISTRICT_NAME",
    },
  ];
  const [boundaryTypes, setBoundaryTypes] = useState([]);
  useEffect(() => {
    let { queryData, layersNames } = props;
    if (queryData?.selectedLayer && layersNames?.length) {
      let currentLayer = layersNames.find(
        (lay) => lay.name === queryData.selectedLayer
      );
      let layerFields = currentLayer.fields;
      let boundsTypesCopy = [...WHOLE_BOUND_TYPES];
      let commonFields = boundsTypesCopy.filter((boundField) => {
        if (
          boundField.value === "SUB_MUNICIPALITY_NAME" &&
          props?.queryData?.selectedLayer === "incidents940"
        )
          return undefined;
        else if (layerFields.map((i) => i.fieldName).includes(boundField.value))
          return boundField;
        else return undefined;
      });
      setBoundaryTypes(commonFields);
      // window.__map.view.goTo(new Extent(window.dashboardExtent))
    }
  }, [props.queryData?.selectedLayer, props.layersNames]);
  const handleSelect = (name) => (value, e) => {
    let { queryData, setQueryData, map } = props;
    if (name === "layers") {
      // setQueryData({
      //   ...queryData,
      //   selectedLayer: e?.value,
      // });
      let homeToExtent = document.querySelector(".esri-home");
      homeToExtent && homeToExtent.click();
      clearGraphics(["ThematicGraphicLayer", "highlightGraphicLayer"], map);
      props.handleLayerChange(e?.value);
    } else if (name === "adminBoundary") {
      window?.__map?.view?.goTo({
        center: [48.19391181173523, 27.162440069022164],
        zoom: 8,
      });
      // let homeToExtent = document.querySelector('.esri-home');
      // homeToExtent && homeToExtent.click();

      //CLEAR HEAT MAP
      clearGraphics(["ThematicGraphicLayer", "highlightGraphicLayer"], map);
      props.handleAdminBoundChange(e?.value);
    } else if (name === "subBoundary") {
      clearGraphics(["highlightGraphicLayer"], props.map);
      setQueryData({
        ...queryData,
        selectedBoundaryType: {
          ...queryData.selectedBoundaryType,
          selectedBoundary: e?.value,
        },
      });
      if (e?.value && queryData.selectedBoundaryType.value)
        highlightSubAdmin(e.value, queryData.selectedBoundaryType.value);
    } else if (name === "preSubBound") {
      clearGraphics(["highlightGraphicLayer"], props.map);
      setQueryData({
        ...queryData,
        selectedBoundaryType: {
          ...queryData.selectedBoundaryType,
          preNeededBound: {
            ...queryData?.selectedBoundaryType?.preNeededBound,
            selectedPreNeededBoundary: e?.value,
          },
        },
      });

      if (e?.value && queryData.selectedBoundaryType?.preNeededBound?.value)
        highlightSubAdmin(
          e.value,
          queryData.selectedBoundaryType.preNeededBound?.value
        );
      props.handleAdminBoundChange(
        e?.value,
        e?.value
          ? `${queryData.selectedBoundaryType?.preNeededBound?.value}=${e?.value}`
          : true
      );
    } else if (name === "timeContext") {
      // if(e?.value) props.handleClearCalender();
      if (!e?.value) props.handleClearCalender(true);
      else
        setQueryData({
          ...queryData,
          selectedTimeContext: {
            type: e?.value,
            dateData: [],
          },
        });
    } else {
      return null;
    }
  };
  const highlightSubAdmin = (subAdmin, adminType) => {
    let boundLayer;
    switch (adminType) {
      case "MUNICIPALITY_NAME":
        boundLayer = "Municipality_Boundary";
        break;
      case "SUB_MUNICIPALITY_NAME":
        boundLayer = "Sub_Municipality_Boundary";

        break;
      case "PLAN_NO":
        boundLayer = "Plan_Data";

        break;

      default:
        boundLayer = "District_Boundary";
        break;
    }
    let layerID = getLayerId(props.map.__mapInfo, boundLayer);
    let queryParams = {
      url: window.mapUrl + "/" + layerID,
      notShowLoading: false,
      returnGeometry: true,
      outFields: ["OBJECTID"],
      where:
        adminType === "PLAN_NO"
          ? `${adminType} = '${subAdmin}'`
          : `${adminType} = ${subAdmin}`,
    };
    queryTask({
      ...queryParams,
      callbackResult: ({ features }) => {
        if (features.length) {
          highlightFeature(features[0], props.map, {
            layerName: "highlightGraphicLayer",
            isHiglightSymbol: true,
            highlighColor: "rgba(255, 255, 0, 0.4)",
            strokeColor: [240, 255, 89, 1],
            highlightWidth: 4,
            noclear: false,
            isZoom: true,
          });
        }
      },
      callbackError: (err) => {
        notificationMessage("حدث خطأ برجاء المحاولة مرة أخرى", 4);
      },
    });
  };
  return (
    <>
      <div className="dashLogosNav no-print">
        <FontAwesomeIcon
          id={props.dropNavOpened == "headerHidden" ? "" : "closeArrow"}
          className="openCloseArrow"
          onClick={
            props.dropNavOpened == "headerHidden"
              ? props.openNavDrop
              : props.closeNavDrop
          }
          icon={
            props.dropNavOpened == "headerHidden"
              ? faArrowAltCircleDown
              : faArrowCircleUp
          }
        />
        <h5
          style={{
            position: "absolute",
            left: "50%",
            top: "50%",
            transform: " translate(-50%, -50%)",
          }}>
          لوحة البيانات والإحصائيات
        </h5>
        <img
          className="ml-2"
          alt="logo"
          src={logo}
          style={{
            width: "76px",
            position: "absolute",
            right: "0",
            top: "50%",
            transform: " translate(-50%, -50%)",
          }}
        />
        <img
          className="ml-2"
          alt="logo"
          src={visionLogo}
          style={{
            width: "100px",
            position: "absolute",
            top: "50%",
            transform: " translate(-50%, -50%)",
            left: "4vw",
            height: "80px",
          }}
        />
      </div>
      <Navbar
        className="Dashboard-Header Header navbar"
        id={props.dropNavOpened}>
        <Container fluid>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            {props?.queryData?.selectedTimeContext?.type && (
              <ul className="dashHeaderDates select-wrap">
                <li>
                  <HijriCalenderComp
                    dataPerPeriod={props?.dataPerPeriod}
                    type={props?.queryData?.selectedTimeContext?.type}
                    setQueryData={props.setQueryData}
                    queryData={props.queryData}
                    handleClearCalender={props.handleClearCalender}
                  />
                </li>
              </ul>
            )}
            {/** timeContext dropdown list */}
            <div className="dashHeaderSelectDiv select-wrap">
              <FloatInput
                placeholder={t("timeContext")}
                value={props?.queryData?.selectedTimeContext?.type}>
                <SelectComp
                  listName="timeContext"
                  onChange={handleSelect("timeContext")}
                  value={props?.queryData?.selectedTimeContext?.type}
                  placeholder={t(`chooseTimeContext`)}
                  list={timeContexts || []}
                  allowClear={true}
                />{" "}
                <span className="dashLabel">{t("timeContext")}</span>{" "}
              </FloatInput>
            </div>
            <div
              // className="m-auto"
              style={{
                display: "flex",
                marginLeft: "auto",
              }}>
              {/**subBoundary dropdown list */}

              {props?.queryData?.selectedBoundaryType?.boundariesArr.length ? (
                <div className="dashHeaderSelectDiv select-wrap" style={{maxWidth:"300px"}}>
                  <FloatInput
                    placeholder={t(
                      `sub_${props?.queryData?.selectedBoundaryType.value}`
                    )}
                    value={
                      props?.queryData?.selectedBoundaryType?.selectedBoundary
                    }>
                    <SelectComp
                      listName="subBoundary"
                      onChange={handleSelect("subBoundary")}
                      value={
                        props?.queryData?.selectedBoundaryType?.selectedBoundary
                      }
                      placeholder={t(
                        `sub_${props?.queryData?.selectedBoundaryType.value}`
                      )}
                      list={
                        props?.queryData?.selectedBoundaryType?.boundariesArr ||
                        []
                      }
                      allowClear={true}
                    />
                    <span className="dashLabel">
                      {t(`sub_${props?.queryData?.selectedBoundaryType.value}`)}
                    </span>
                  </FloatInput>
                </div>
              ) : null}
              {/**preNeeded boundary */}
              {props?.queryData?.selectedBoundaryType?.value ===
              "DISTRICT_NAME" ? (
                <div className="dashHeaderSelectDiv select-wrap">
                  <FloatInput
                    placeholder={t("mainMunicipalities")}
                    value={
                      props?.queryData?.selectedBoundaryType?.preNeededBound
                        ?.selectedPreNeededBoundary
                    }>
                    <SelectComp
                      listName="preSubBound"
                      onChange={handleSelect("preSubBound")}
                      value={
                        props?.queryData?.selectedBoundaryType?.preNeededBound
                          ?.selectedPreNeededBoundary
                      }
                      placeholder={t("mainMunicipalities")}
                      list={
                        props?.queryData?.selectedBoundaryType?.preNeededBound
                          ?.preNeededBoundariesArr || []
                      }
                      allowClear={true}
                    />{" "}
                    <span className="dashLabel">{t("mainMunicipalities")}</span>{" "}
                  </FloatInput>
                </div>
              ) : null}
              {/**************** */}
              {/**adminBoundary dropdown list */}

              {boundaryTypes?.length ? (
                <div className="dashHeaderSelectDiv select-wrap">
                  <FloatInput
                    placeholder={t("adminBoundary")}
                    value={props?.queryData?.selectedBoundaryType?.value}>
                    <SelectComp
                      listName="adminBoundary"
                      onChange={handleSelect("adminBoundary")}
                      value={props?.queryData?.selectedBoundaryType?.value}
                      placeholder={t("adminBoundary")}
                      list={boundaryTypes || []}
                      allowClear={true}
                    />
                    <span className="dashLabel">{t("adminBoundary")}</span>{" "}
                  </FloatInput>
                </div>
              ) : null}
              {/**layers dropdown list */}

              {props.layersNames?.length ? (
                <div className="dashHeaderSelectDiv select-wrap">
                  <FloatInput
                    placeholder={t("layers")}
                    value={props?.queryData?.selectedLayer}>
                    <SelectComp
                      isIncident={props.isIncident}
                      listName="layers"
                      onChange={handleSelect("layers")}
                      value={props?.queryData?.selectedLayer}
                      placeholder={t("chooseLayer")}
                      list={props.layersNames || []}
                      languageStatus={i18n.language}
                    />{" "}
                    <span className="dashLabel">{t("layers")}</span>
                  </FloatInput>
                </div>
              ) : null}
            </div>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </>
  );
}
