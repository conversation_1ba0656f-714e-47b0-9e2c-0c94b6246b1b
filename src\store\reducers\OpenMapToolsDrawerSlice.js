import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  open: false,
};

const OpenMapToolsDrawerSlice = createSlice({
  name: "map_tools_drawer",
  initialState,
  reducers: {
    openMapToolsDrawer: (state, action) => {
      state.open = true;
    },
    closeMapToolsDrawer: (state, action) => {
      state.open = false;
    },
    toggleMapToolsDrawer: (state, action) => {
      state.open = !state.open;
    },
  },
});

export const { openMapToolsDrawer, closeMapToolsDrawer, toggleMapToolsDrawer } =
  OpenMapToolsDrawerSlice.actions;

export default OpenMapToolsDrawerSlice.reducer;
