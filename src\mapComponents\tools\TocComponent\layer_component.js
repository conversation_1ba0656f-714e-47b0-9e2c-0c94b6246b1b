import { faCaretSquareLeft } from "@fortawesome/free-regular-svg-icons";
import {
  faCaretDown,
  faCaretLeft,
  faCaretRight,
  faCaretSquareDown,
  faCartPlus,
  faPlus,
  faPlusSquare,
  faSearchPlus,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import i18n from "../../../i18n";
// import { layersSetting } from "../../../helper/layers";
import { useTranslation } from "react-i18next";

// context to save all input refs
const LayerInputContext = createContext();

// Provider component to wrap the entire layer components
export const LayerInputProvider = ({ children }) => {
  const inputRefs = useRef({});

  const registerInput = (id, ref) => {
    inputRefs.current[id] = ref;
  };

  const getInput = (id) => inputRefs.current[id];

  return (
    <LayerInputContext.Provider value={{ registerInput, getInput }}>
      {children}
    </LayerInputContext.Provider>
  );
};

// Hook to use the layer input context
const useLayerInputs = () => useContext(LayerInputContext);

export const LayerComponent = ({
  level = 0,
  childLayers,
  layer,
  legends,
  AllLayers,
  map,
  mainData,
  languageState,
}) => {
  const { registerInput, getInput } = useLayerInputs();
  const inputRef = useRef(null);

  const { t } = useTranslation("layers");
  let childs = childLayers.filter((child) => child.parentLayerId == layer.id);
  const tocLayerNames = Object.keys(mainData.tocLayers);
  //   // for testing

  // const tocLayerNames = [
  //   "PROTECTED_AREA_BOUNDARY",
  //   "PATHYMETRIC_MAPPING",
  //   "MANGROVE",
  //   "WET_LAND",
  //   "ECO_REGION",
  //   "SEA_GRASS",
  // ];

  const [isExpanded, setIsExpanded] = useState(false);
  const [legendData, setLegendData] = useState(undefined);
  const [isVisible, setIsVisible] = useState();
  const [filteredChilds, setFilteredChilds] = useState();

  useEffect(() => {
    filterChilds();
    let existedLegend = legends.find((legend) => legend.layerId == layer.id);
    if (existedLegend) {
      setLegendData(existedLegend);
    }
    var existedLayer = map
      .findLayerById("baseMap")
      .allSublayers._items.find((lay) => lay.id == layer.id);
    if (existedLayer) {
      layer.visible = existedLayer.visible;
      setIsVisible(existedLayer.visible);
    }
  }, []);

  useEffect(() => {
    if (inputRef.current) {
      registerInput(layer.id, inputRef.current);
    }
  }, [layer.id, registerInput]);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const changeLayer = () => {
    let parentExists = false;
    let parentLayer;
    let reqLayer = layer;
    let reqLayerInputRef;
    if (layer.parentLayerId !== -1) {
      parentExists = true;
    }

    if (!parentExists) {
      layer.visible = !layer.visible;
      var existedLayer = map
        .findLayerById("baseMap")
        .allSublayers._items.find((lay) => lay.id == layer.id);
      existedLayer.visible = layer.visible;
    }

    while (parentExists) {
      // to update layer visibility data
      reqLayer.visible = !reqLayer.visible;
      // to toggle layer visibility checkbox
      reqLayerInputRef = getInput(reqLayer.id);
      reqLayerInputRef.checked = reqLayer.visible;
      // to toggle layer visibility on map
      var existedLayer = map
        .findLayerById("baseMap")
        .allSublayers._items.find((lay) => lay.id == reqLayer.id);
      existedLayer.visible = reqLayer.visible;
      // to get parent layer
      parentLayer = AllLayers.find((lay) => lay.id == reqLayer.parentLayerId);

      // get other layer Ids in same level
      let otherChildIds = parentLayer.subLayerIds.filter(
        (id) => id != reqLayer.id
      );
      let otherChildIsVisible = false;
      otherChildIds.forEach((childId) => {
        let child = AllLayers.find((lay) => lay.id == childId);
        if (child.visible) otherChildIsVisible = true;
      });

      // check if other layers and parent are visible to behave layer as if it doesn't has a parent
      if (parentLayer.visible && otherChildIsVisible) {
        parentExists = false;
      } else {
        if (parentLayer.parentLayerId !== -1) {
          reqLayer = parentLayer;
        } else {
          if (reqLayer.visible != parentLayer.visible) {
            parentLayer.visible = !parentLayer.visible;
            reqLayerInputRef = getInput(parentLayer.id);
            reqLayerInputRef.checked = parentLayer.visible;
            var existedLayer = map
              .findLayerById("baseMap")
              .allSublayers._items.find((lay) => lay.id == parentLayer.id);
            existedLayer.visible = parentLayer.visible;
          }
          parentExists = false;
        }
      }
    }
    setIsVisible(reqLayer.visible);
  };

  const zoomToLayer = () => {
    if (layer && layer.minScale > 0 && layer.disable) {
      var dpi = 96; // Set to resolution of your screen
      var scale = layer.minScale;
      //this.props.map.view.scale = scale;
      var mapunitInMeters = 111319.5; // size of one degree at Equator. Change if you are using a projection with a different unit than degrees
      var newRes = scale / (dpi * 39.37 * mapunitInMeters);
      var newExtent = map.view.extent.expand(newRes * 200);
      map.view.goTo(newExtent);

      // let tempLayer = this.props.map
      //   .findLayerById("baseMap")
      //   .allSublayers._items.find((x) => x.id == layer.layerId);

      // if (tempLayer) {
      //   tempLayer.visible = true;
      //   let { layers } = this.state;
      //   layers.$legends[key].visible = true;
      //   this.setState({ layers });
      // }
    }
  };

  const filterChilds = () => {
    let filteredChilds = [];
    let childLayers = childs.filter((child) =>
      tocLayerNames.includes(child.name)
    );
    if (childLayers.length > 0) {
      childLayers.forEach((child) => {
        if (child.parentLayerId == layer.id) {
          filteredChilds.push(child);
        }
      });
    }
    if (childLayers.length == 0 && layer.subLayerIds) {
      childs.forEach((child) => {
        if (
          child.subLayerIds &&
          child.parentLayerId == layer.id &&
          !tocLayerNames.includes(child.name)
        ) {
          filteredChilds.push(child);
        }
      });
    }

    setFilteredChilds(filteredChilds);
  };

  return (
    <section
      className={` ${layer.disable}`}
      style={{
        paddingInlineStart: `${(level + 1) * 15}px`,
      }}
    >
      <div className="toc-gallery" key={layer.id}>
        <div onClick={toggleExpand} style={{ cursor: "pointer" }}>
          {isExpanded ? (
            <FontAwesomeIcon icon={faCaretDown} />
          ) : (
            <FontAwesomeIcon
              icon={i18n.language === "ar" ? faCaretLeft : faCaretRight}
            />
          )}
        </div>
        <input
          ref={inputRef}
          type="checkbox"
          style={{ accentColor: "#9D4223" }}
          checked={isVisible}
          onChange={changeLayer}
        />
        <label
          style={{
            fontSize: "13px",
            fontWeight: "normal",
          }}
        >
          {mainData.tocLayers[layer.name]
            ? languageState === "ar"
              ? mainData.tocLayers[layer.name].arabicName
              : mainData.tocLayers[layer.name].englishName
            : t(layer.name)}
        </label>
        {legendData && (
          <div style={{ cursor: "pointer" }} onClick={zoomToLayer}>
            <FontAwesomeIcon
              icon={faSearchPlus}
              style={{
                fontSize: "15px",
                color: "#9d4223",
                marginInlineStart: "auto",
                display: "block",
              }}
            />
          </div>
        )}
      </div>
      {isExpanded && filteredChilds ? (
        <div>
          {filteredChilds.map((child, key) => (
            <LayerComponent
              level={level + 1}
              layer={child}
              legends={legends}
              childLayers={childLayers}
              zoomToLayer={zoomToLayer}
              // changeLayer={changeLayer}
              map={map}
              mainData={mainData}
              AllLayers={AllLayers}
              languageState={languageState}
            />
          ))}
        </div>
      ) : null}
      {isExpanded && legendData
        ? legendData.legend.map((legend, key) => {
            return (
              <div key={key}>
                <img
                  src={"data:image/jpeg;base64," + legend.imageData}
                  alt=""
                  className="layer-img"
                />
                <div
                  style={{
                    fontSize: "13px",
                    marginBottom: "10px",
                  }}
                >
                  {legend.label}
                </div>
              </div>
            );
          })
        : null}
    </section>
  );
};
