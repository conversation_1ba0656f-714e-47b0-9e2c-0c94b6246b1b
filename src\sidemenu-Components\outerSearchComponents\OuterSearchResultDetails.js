import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "react-bootstrap";
import { Tab, Tabs, Tab<PERSON>ist, TabPanel } from "react-tabs";
import "react-tabs/style/react-tabs.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import axios from "axios";
import { faArchive, faSearchPlus } from "@fortawesome/free-solid-svg-icons";
import IconButton from "@mui/material/IconButton";
import { Tooltip } from "@mui/material";
import moment from "moment-hijri";
import { AiOutlineBars } from "react-icons/ai";
import PlanDataModal from "../../tables/Modals/PlanLandsData/PlanDataModal";
import Loader from "../../containers/Loader";
import {
  getFeatureDomainName,
  getLayerId,
  navigateToGoogle,
  convertToArabic,
  queryTask,
  showLoading,
  zoomToFeatureByObjectId,
  zoomToFeatureDefault,
  zoomToFeatureBySpatialID,
  zoomToFeatureByFilter,
  checkIfHiddenField,
  getFieldsMediaUrls,
  convertTimeStampToDate,
  highlightAreaWithDimming,
} from "../../helper/common_func";

import GalleryArchive from "../../assets/images/gallery archive.svg";
import { useTranslation } from "react-i18next";
import generateRandomColor, {
  getFileTypeFromFormat,
  isNumber,
  notificationMessage,
} from "../../helper/utilsFunc";
import { toArabic } from "arabic-digits";
import { PARCEL_LANDS_LAYER_NAME } from "../../helper/constants";
import { message, List, Typography } from "antd";
import {
  alphbetNumbers,
  getNumbersInStringsByValue,
  megawrahNumbersInString,
  municipilitiesForRoyal,
  randomPlanNumbers,
  subdivisionTypes,
} from "../../helper/layers";
import ArchiveModal from "../../tables/Modals/ArchiveModal/ArchiveModal";
import PdfViewer from "../../components/PdfViewer/PdfViewer";
import { IoIosArrowDown, IoMdInformationCircleOutline } from "react-icons/io";
import { IoLocation } from "react-icons/io5";
import {
  FaGripVertical,
  FaImages,
  FaChartBar,
  FaFolderOpen,
} from "react-icons/fa";
import { LuStretchHorizontal } from "react-icons/lu";
import GalleryModal from "../../tables/Modals/GalleryModal";

import MultiSelectBarChart from "./MultiSelectBarChart";

import pdf_file_logo from "../../assets/images/sidemenu/pdf_file_logo.svg";
import exel_file_logo from "../../assets/images/sidemenu/exel_file_logo.svg";
import word_file_logo from "../../assets/images/sidemenu/word_file_logo.svg";
import link_logo from "../../assets/images/sidemenu/link_logo.svg";
import rar_file_icon from "../../assets/images/sidemenu/rar-file-icon.svg";
import zip_file_icon from "../../assets/images/sidemenu/zip-file-icon.svg";
import any_file_icon from "../../assets/images/sidemenu/any-file-icon.svg";
import cad_file_icon from "../../assets/images/sidemenu/cad-file-icon.svg";

import important_areas_icon from "../../assets/images/sidemenu/important_areas.svg";
import eco_system_icon from "../../assets/images/sidemenu/eco_system.svg";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer.js";
import {
  PieChart,
  Pie,
  Cell,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
} from "recharts";
import { Tooltip as RechartsTooltip } from "recharts";

const { Text } = Typography;

function OuterSearchResultDetails(props) {
  const [dependcyLayer, setDependcyLayer] = useState(null);
  const [protectedAreaAttachments, setProtectedAreaAttachments] = useState();
  const [planDataModal, setPlanDataModal] = React.useState();
  const [archiveDataModal, setArchiveDataModal] = React.useState();
  const [loading, setLoading] = React.useState(false);
  const { t, i18n } = useTranslation("map", "common", "layers");
  const [imagesAndVideosStyle, setImagesAndVideosStyle] = useState("col");
  const [isImageOrVideoTab, setIsImageOrVideoTab] = useState(false);
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [galleryData, setGalleryData] = useState(undefined);
  const [openGalleryModal, setOpenGalleryModal] = useState(false);
  const [chartData, setChartData] = useState(null);

  const sharedIconsLayers = ["PROTECTED_AREA_BOUNDARY", "REGION"];

  console.log("outer search result detials props", props);

  // Event handler to change the selected tab index
  const handleTabChange = (newValue) => {
    console.log("tab changed", newValue);
    // clean up protectedAreaAttachments if existing
    if (protectedAreaAttachments?.length) setProtectedAreaAttachments();
    setSelectedTabIndex(newValue);
  };

  const zoomToFeatureOrDefault = (data, map) => {
    if (!data.geometry) {
      zoomToFeatureByObjectId(data, map, false, (feature) => {
        data.geometry = feature.geometry;
      });
    } else {
      if (
        data?.geometry?.rings?.length ||
        data?.geometry?.paths?.length ||
        data?.geometry?.x
      )
        zoomToFeatureDefault(data, map);
    }
  };

  const isPrivateRoyalSalesLands = [
    "PARCEL_PRIVACY",
    "LGR_ROYAL",
    "SALES_LANDS",
  ].includes(props.outerSearchResult?.layerName);

  const [iconsData] = useState(() => {
    let reqIcons = [
      {
        id: 1,
        icon: faSearchPlus,
        tooltip: "zoomIn",
        data: "Zoom Data",
      },
      ...(props?.mainData?.layers[
        isPrivateRoyalSalesLands
          ? PARCEL_LANDS_LAYER_NAME
          : props.data?.layerName
      ]?.dependecies?.filter((dep) => {
        let landParcelLayer = props.data?.layerName === "Landbase_Parcel";
        let isOwnerTypeExist =
          dep.showingField === "OWNER_TYPE" &&
          props.data?.OWNER_TYPE_Code === dep?.codeValue;
        if (landParcelLayer && isOwnerTypeExist) return dep;
        else if (
          (dep.showingField !== "OWNER_TYPE" &&
            (!dep.showingField || !isPrivateRoyalSalesLands)) ||
          (isPrivateRoyalSalesLands &&
            (dep.name === props.data?.layerName || dep.isLandsRelevant))
        )
          return dep;
        else return undefined;
      }) || []),
    ];

    return reqIcons;
  });

  const [selectedTab, setSelectedTab] = useState(0);

  let loggedIn = localStorage.getItem("user");

  useEffect(() => {
    let visibleLayersIDs = props.map.__mapInfo.mapVisibleLayerIDs;
    visibleLayersIDs.forEach((layerId) => {
      var existedLayer = props.map
        .findLayerById("baseMap")
        .allSublayers._items.find((lay) => lay.id == layerId);
      existedLayer.visible = true;
    });
    props.map.findLayerById("SelectGraphicLayer").removeAll();

    if (loggedIn) {
      getStatisticsForChart();
    }

    var imagesURL = getFieldsMediaUrls(
      props.data?.layerName,
      props.mainData.layers,
      props.data
    );

    if (imagesURL.length > 0) {
      setGalleryData(imagesURL);
    }
    if (props.data && !props.data.isNotZoom) {
      // console.log("detail data", props.data);
      // if (!props.data.geometry) {
      //   zoomToFeatureByObjectId(props.data, props.map, false, (feature) => {
      //     props.data.geometry = feature.geometry;
      //   });
      // } else {
      //   if (
      //     props?.data?.geometry?.rings?.length ||
      //     props?.data?.geometry?.paths?.length ||
      //     props?.data?.geometry?.x
      //   )
      //     zoomToFeatureDefault(props.data, props.map);
      // }

      zoomToFeatureOrDefault(props.data, props.map);
    }

    if (props.data.geometry.type == "polygon") {
      highlightAreaWithDimming([props.data], props.map);
    }

    return () => {
      // clearGraphicLayer("ZoomGraphicLayer", props.map);
      // props.setLandBaseParcelData();
      props.resultDetailsDataRef.current.landBaseParcelData = null;
    };
  }, []);

  useEffect(() => {
    let resultSearchGraphicLayer = props.map.findLayerById(
      "generalSearchResultGraphicLayer"
    );
    if (
      (props.data.layerName == "REGION" && selectedTabIndex === 3) ||
      (props.data.layerName == "PROTECTED_AREA_BOUNDARY" &&
        selectedTabIndex === 4)
    ) {
      resultSearchGraphicLayer.visible = false;
    } else {
      resultSearchGraphicLayer.visible = true;
    }

    return () => {
      resultSearchGraphicLayer.visible = true;
    };
  }, [selectedTabIndex]);

  const handleGoogleNavigation = () => {
    if (props.data.geometry) {
      if (props.data.geometry.centroid) {
        navigateToGoogle(
          props.data.geometry.centroid.latitude,
          props.data.geometry.centroid.longitude
        );
      } else {
        navigateToGoogle(
          props.data.geometry.latitude,
          props.data.geometry.longitude
        );
      }
    }
  };

  const getStatisticsForChart = () => {
    let fieldName;
    let where;
    if (props.data.layerName == "PROTECTED_AREA_BOUNDARY") {
      fieldName =
        props.languageState == "ar"
          ? props.data.AR_PROTECTED_AREA_NAME
          : props.data.EN_PROTECTED_AREA_NAME;
      where =
        props.languageState == "ar"
          ? `AR_PROTECTED_AREA_NAME='${fieldName}'`
          : `EN_PROTECTED_AREA_NAME='${fieldName.replaceAll(/'/gi, "''")}'`;
    } else if (props.data.layerName == "REGION") {
      fieldName =
        props.languageState == "ar"
          ? props.data.AR_REGION_NAME
          : props.data.EN_REGION_NAME;
      where =
        props.languageState == "ar"
          ? `AR_REGION='${fieldName}'`
          : `EN_REGION='${fieldName.replaceAll(/'/gi, "''")}'`;
    }
    let layerId = getLayerId(props.map.__mapInfo, "TERRESTRIAL_SPECIE");
    let outfield =
      props.languageState == "ar" ? "AR_NCW_CATEGORY" : "EN_NCW_CATEGORY";
    queryTask({
      url: window.mapUrl + "/" + layerId,
      where: where,
      outFields: [outfield],
      groupByFields: [outfield],
      returnGeometry: true,
      statistics: [{ type: "count", field: outfield }],
      callbackResult: ({ features }) => {
        let layerLegened = props.map.__mapInfo.info.$legends.find(
          (x) => x.layerName == "TERRESTRIAL_SPECIE"
        ).legend;

        features.forEach((f) => {
          let selectLenged = layerLegened.find(
            (l) => l.values[0] == f.attributes[outfield]
          );

          f.attributes.legend = selectLenged?.imageData;
        });

        console.log("query result", features);
        let chartData = features.map((feat) => feat.attributes);
        console.log("chart data", chartData);
        setChartData(chartData);
      },
      callbackError(error) {},
    });
  };

  const [showAllInfoTableData, setShowAllInfoTableData] = useState(false);

  const tableInfoData = (attribute) => {
    return convertToArabic(
      attribute.indexOf("AREA_KM") > -1 &&
        isNumber(
          (isPrivateRoyalSalesLands
            ? props.resultDetailsDataRef.current?.landBaseParcelData?.attributes
            : props.data)[attribute]
        )
        ? (
            +(
              isPrivateRoyalSalesLands
                ? props.resultDetailsDataRef.current?.landBaseParcelData
                    ?.attributes
                : props.data
            )[attribute]
          )
        : (isPrivateRoyalSalesLands
            ? props.resultDetailsDataRef.current?.landBaseParcelData?.attributes
            : props.data)[attribute]
    );
  };

  const getProtectedAreaAttachments = () => {
    // 1 - start loader
    showLoading(true);
    const protectedAreaLayerName = "PROTECTED_AREA_BOUNDARY_ATT";
    const layerdId = getLayerId(props.map.__mapInfo, protectedAreaLayerName);
    const queryFieldName = i18n.language.toUpperCase() + "_PROTECTED_AREA_NAME";
    const whereClause = `${queryFieldName}='${props.data[queryFieldName]}'`;
    const attachmentsOutFields = [
      "ATTACHMENT_FILE_PATH",
      "ATTACHMENT_CREATE_DATE",
      "AR_ATTACHMENT_DESCRIPTION",
      "EN_ATTACHMENT_DESCRIPTION",
    ];
    queryTask({
      url: window.mapUrl + "/" + layerdId,
      outFields: attachmentsOutFields,
      where: whereClause,
      callbackResult: ({ features }) => {
        if (features.length) {
          const attachments = features.map((feat) => {
            const isLink =
              feat.attributes.ATTACHMENT_FILE_PATH.startsWith("http");
            if (isLink) {
              return {
                id: feat.attributes.ATTACHMENT_CREATE_DATE,
                icon: link_logo,
                text: feat.attributes[
                  i18n.language.toUpperCase() + "_ATTACHMENT_DESCRIPTION"
                ],
                link: feat.attributes.ATTACHMENT_FILE_PATH,
              };
            }
            const fileFormat = feat.attributes.ATTACHMENT_FILE_PATH.split(".")
              .pop()
              .toLowerCase();
            const fileType = getFileTypeFromFormat(fileFormat);
            if (fileType === "excel") {
              return {
                id: feat.attributes.ATTACHMENT_CREATE_DATE,
                icon: exel_file_logo,
                text: feat.attributes[
                  i18n.language.toUpperCase() + "_ATTACHMENT_DESCRIPTION"
                ],
                link: `${window.filesURL}/${feat.attributes.ATTACHMENT_FILE_PATH}`,
              };
            } else if (fileType === "pdf") {
              return {
                id: feat.attributes.ATTACHMENT_CREATE_DATE,
                icon: pdf_file_logo,
                text: feat.attributes[
                  i18n.language.toUpperCase() + "_ATTACHMENT_DESCRIPTION"
                ],
                link: `${window.filesURL}/${feat.attributes.ATTACHMENT_FILE_PATH}`,
              };
            } else if (fileType === "word") {
              return {
                id: feat.attributes.ATTACHMENT_CREATE_DATE,
                icon: word_file_logo,
                text: feat.attributes[
                  i18n.language.toUpperCase() + "_ATTACHMENT_DESCRIPTION"
                ],
                link: `${window.filesURL}/${feat.attributes.ATTACHMENT_FILE_PATH}`,
              };
            } else if (fileType === "cad") {
              return {
                id: feat.attributes.ATTACHMENT_CREATE_DATE,
                icon: cad_file_icon,
                text: feat.attributes[
                  i18n.language.toUpperCase() + "_ATTACHMENT_DESCRIPTION"
                ],
                link: `${window.filesURL}/${feat.attributes.ATTACHMENT_FILE_PATH}`,
              };
            } else if (fileType === "image") {
              return {
                id: feat.attributes.ATTACHMENT_CREATE_DATE,
                thumbnail: `${window.filesURL}/${feat.attributes.ATTACHMENT_FILE_PATH}`,
                text: feat.attributes[
                  i18n.language.toUpperCase() + "_ATTACHMENT_DESCRIPTION"
                ],
                link: `${window.filesURL}/${feat.attributes.ATTACHMENT_FILE_PATH}`,
              };
            } else if (fileType === "zip") {
              return {
                id: feat.attributes.ATTACHMENT_CREATE_DATE,
                icon: zip_file_icon,
                text: feat.attributes[
                  i18n.language.toUpperCase() + "_ATTACHMENT_DESCRIPTION"
                ],
                link: `${window.filesURL}/${feat.attributes.ATTACHMENT_FILE_PATH}`,
              };
            } else if (fileType === "rar") {
              return {
                id: feat.attributes.ATTACHMENT_CREATE_DATE,
                icon: rar_file_icon,
                text: feat.attributes[
                  i18n.language.toUpperCase() + "_ATTACHMENT_DESCRIPTION"
                ],
                link: `${window.filesURL}/${feat.attributes.ATTACHMENT_FILE_PATH}`,
              };
            } else if (fileType === "other") {
              return {
                id: feat.attributes.ATTACHMENT_CREATE_DATE,
                icon: any_file_icon,
                text: feat.attributes[
                  i18n.language.toUpperCase() + "_ATTACHMENT_DESCRIPTION"
                ],
                link: `${window.filesURL}/${feat.attributes.ATTACHMENT_FILE_PATH}`,
              };
            }
          });
          setProtectedAreaAttachments(attachments);
          showLoading(false);
        } else {
          setProtectedAreaAttachments([]);
          showLoading(false);
        }
      },
      callbackError: (err) => {
        console.log(err);
        showLoading(false);
      },
    });
  };

  const [reservesNamesValues, setReservesNamesValues] = useState([]);
  const getReserves = () => {
    const layerdId = getLayerId(props.map.__mapInfo, "PROTECTED_AREA_BOUNDARY");
    let where =
      props.languageState == "ar"
        ? `AR_REGION = '${props.data.AR_REGION_NAME}'`
        : `EN_REGION = '${props.data.EN_REGION_NAME.replaceAll(/'/gi, "''")}'`;
    let outfield =
      props.languageState == "ar"
        ? "AR_PROTECTED_AREA_NAME"
        : "EN_PROTECTED_AREA_NAME";
    queryTask({
      url: window.mapUrl + "/" + layerdId,
      outFields: [outfield],
      where: where,
      returnDistinctValues: true,
      callbackResult: (result) => {
        const reserveNames = result.features.map(
          (feature) => feature.attributes[outfield]
        );
        console.log("reserves", reserveNames);
        setReservesNamesValues(reserveNames);
      },
      callbackError: (err) => {
        console.log(err);
        showLoading(false);
      },
    });
  };

  const ECO_SYSTEM_REGION = {
    layerName: "ECO_SYSTEM_REGION",
    filterField:
      localStorage.getItem("lang") == "en"
        ? "EN_REGION_NAME"
        : "AR_REGION_NAME",
    outField:
      localStorage.getItem("lang") == "en"
        ? "EN_ECOSYSTEM_NAME"
        : "AR_ECOSYSTEM_NAME",
  };
  const ECO_SYSTEM_PROTECTED = {
    layerName: "ECO_SYSTEM_PROTECTED",
    filterField:
      localStorage.getItem("lang") == "en"
        ? "EN_PROTECTED_AREA_NAME"
        : "AR_PROTECTED_AREA_NAME",
    outField:
      localStorage.getItem("lang") == "en"
        ? "EN_ECOSYSTEM_NAME"
        : "AR_ECOSYSTEM_NAME",
  };
  const [ecoSystemDataList, setEcoSystemDataList] = useState([]);
  const getEcoSystemData = () => {
    const existedFeatureLayers = [
      "important_area_WET_LAND",
      "important_area_ECO_HOTSPOT",
    ];
    existedFeatureLayers.forEach((layer) => {
      const existedLayer = props.map.findLayerById(layer);
      if (existedLayer) props.map.remove(existedLayer);
    });

    let featureLayer;
    const existedFeatureLayer = props.map.findLayerById("eco_system");
    let dataObject =
      props.data.layerName == "REGION"
        ? ECO_SYSTEM_REGION
        : ECO_SYSTEM_PROTECTED;
    const filterValue = props.data[dataObject.filterField];
    const layerId = getLayerId(props.map.__mapInfo, dataObject.layerName);
    const featureLayerUrl = `${window.mapUrl}/${layerId}`;
    const whereFilter = `${dataObject.filterField} = '${filterValue}'`;

    if (existedFeatureLayer && existedFeatureLayer.layerId == layerId) {
      existedFeatureLayer.definitionExpression = whereFilter;
      featureLayer = existedFeatureLayer;
    } else {
      if (existedFeatureLayer) {
        props.map.remove(existedFeatureLayer);
      }
      featureLayer = new FeatureLayer({
        id: "eco_system",
        url: featureLayerUrl,
        definitionExpression: whereFilter,
        visible: true,
      });
      props.map.add(featureLayer);
    }

    queryTask({
      url: featureLayerUrl,
      where: whereFilter,
      outFields: [
        dataObject.outField,
        "INTERSECTION_AREA_M",
        "INTERSECTION_PERCENTAGE",
      ],
      returnGeometry: false,
      groupByFields: [dataObject.outField],
      callbackResult: (result) => {
        let ecoSystemData = result.features.map((feat) => {
          const color = generateRandomColor();
          return {
            ecoSystemName: feat.attributes[dataObject.outField],
            area: feat.attributes.INTERSECTION_AREA_M.toFixed(2),
            percentage: parseFloat(
              (feat.attributes.INTERSECTION_PERCENTAGE * 100).toFixed(2)
            ),
            color,
          };
        });
        setEcoSystemDataList(ecoSystemData);
      },
    });
  };

  const IMPORTANT_AREAS_REGION = {
    fieldOFSelectedValue:
      localStorage.getItem("lang") == "en"
        ? "EN_REGION_NAME"
        : "AR_REGION_NAME",
    layers: [
      {
        layerName: "ECO_HOTSPOT",
        filterField:
          localStorage.getItem("lang") == "en" ? "EN_REGION" : "AR_REGION",
        featureColor: "#7c913c",
      },
      {
        layerName: "WET_LAND",
        filterField:
          localStorage.getItem("lang") == "en" ? "EN_REGION" : "AR_REGION",
        featureColor: "#0070e180",
      },
    ],
  };

  const IMPORTANT_AREAS_PROTECTED = {
    fieldOFSelectedValue:
      localStorage.getItem("lang") == "en"
        ? "EN_PROTECTED_AREA_NAME"
        : "AR_PROTECTED_AREA_NAME",
    layers: [
      {
        layerName: "ECO_HOTSPOT",
        filterField:
          localStorage.getItem("lang") == "en"
            ? "EN_PROTECTED_AREA_NAME"
            : "AR_PROTECTED_AREA_NAME",
        featureColor: "#7c913c",
      },
      {
        layerName: "WET_LAND",
        filterField:
          localStorage.getItem("lang") == "en"
            ? "EN_PROTECTED_AREA_NAME"
            : "AR_PROTECTED_AREA_NAME",
        featureColor: "#0070e180",
      },
    ],
  };

  const [importantAreasDataList, setImportantAreasDataList] = useState([]);

  function queryTaskPromise(settings) {
    return new Promise((resolve, reject) => {
      queryTask({
        ...settings,
        callbackResult: (data) => resolve(data),
        callbackError: (error) => reject(error),
        notShowLoading: true,
      });
    });
  }
  const getImportantAreasData = () => {
    let ecoSystemLayer = props.map.findLayerById("eco_system");
    if (ecoSystemLayer) props.map.remove(ecoSystemLayer);
    showLoading(true);
    let featureLayersColors = [];
    let dataObject =
      props.data.layerName == "REGION"
        ? IMPORTANT_AREAS_REGION
        : IMPORTANT_AREAS_PROTECTED;
    const filterValue = props.data[dataObject.fieldOFSelectedValue];
    let promiseQueries = [];
    let featureLayer;
    dataObject.layers.forEach((layer) => {
      const existedFeatureLayer = props.map.findLayerById(
        `important_area_${layer.layerName}`
      );
      const layerId = getLayerId(props.map.__mapInfo, layer.layerName);
      const featureLayerUrl = `${window.mapUrl}/${layerId}`;
      const whereFilter = `${layer.filterField} = '${filterValue}'`;
      if (existedFeatureLayer && existedFeatureLayer.layerId == layerId) {
        existedFeatureLayer.definitionExpression = whereFilter;
        featureLayer = existedFeatureLayer;
      } else {
        if (existedFeatureLayer) {
          props.map.remove(existedFeatureLayer);
        }
        featureLayer = new FeatureLayer({
          id: `important_area_${layer.layerName}`,
          url: featureLayerUrl,
          definitionExpression: whereFilter,
          visible: true,
        });
        props.map.add(featureLayer);
      }
      promiseQueries.push(
        queryTaskPromise({
          url: featureLayerUrl,
          where: whereFilter,
          returnCountOnly: true,
        })
      );

      if (layer.featureColor) {
        featureLayersColors.push(layer.featureColor);
      } else {
        featureLayersColors.push(null);
      }
    });

    Promise.all(promiseQueries).then((resultData) => {
      let importantAreasData = [];
      for (let index = 0; index < dataObject.layers.length; index++) {
        const color = featureLayersColors[index]
          ? featureLayersColors[index]
          : generateRandomColor();
        importantAreasData.push({
          name: dataObject.layers[index].layerName,
          count: resultData[index],
          color,
          translatedName: t(dataObject.layers[index].layerName),
        });
      }
      setImportantAreasDataList(importantAreasData);
      showLoading(false);
    });
  };

  return (
    <div className="generalResultDetails cardDetailsHelp">
      {props.data && (
        <Tabs
          defaultFocus={true}
          selectedIndex={selectedTab}
          // onSelect={(x) => makeClickAction(x)}
          value={selectedTabIndex}
          onSelect={(x) => handleTabChange(x)}
          //onChange={handleTabChange}
        >
          <TabList
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <div>
              <Tab
                onClick={() => {
                  setIsImageOrVideoTab(false);
                }}
              >
                <Tooltip title={t("map:results")} placement="top">
                  <IconButton className="tooltipButton">
                    <IoMdInformationCircleOutline />
                  </IconButton>
                </Tooltip>
              </Tab>

              {props.data.layerName == "PROTECTED_AREA_BOUNDARY" && (
                <Tab
                  onClick={() => {
                    setIsImageOrVideoTab(false);
                    setOpenGalleryModal(true);
                    console.log("gallery data", galleryData);
                    if (!galleryData) {
                      message.warning(t("NoDataWarning", { ns: "common" }));
                    }
                  }}
                >
                  <Tooltip
                    title={t("map:mapToolsServices.showImages")}
                    placement="top"
                  >
                    <IconButton className="tooltipButton">
                      <FaImages />
                    </IconButton>
                  </Tooltip>
                </Tab>
              )}
              {iconsData.map((ic) => {
                if (
                  ic.depName === "LandStatistics" &&
                  randomPlanNumbers.includes(props.data?.PLAN_NO)
                )
                  return undefined;
                if (
                  ic.showingField === undefined ||
                  ic.showingField !== "OWNER_TYPE" ||
                  !isPrivateRoyalSalesLands ||
                  (isPrivateRoyalSalesLands &&
                    ic.name === props.data?.layerName)
                )
                  return (
                    <Tab
                      onClick={() => {
                        setIsImageOrVideoTab(false);
                        zoomToFeatureOrDefault(props.data, props.map);
                      }}
                    >
                      <Tooltip
                        title={t(`layers:${ic.tooltip}`)}
                        placement="top"
                      >
                        <IconButton className="tooltipButton">
                          {ic.icon ? (
                            <FontAwesomeIcon
                              icon={ic.icon}
                              style={{
                                cursor: "pointer",
                              }}
                            />
                          ) : (
                            <img
                              id="outerSVG"
                              className={
                                ic.className === "contzwa2edClass"
                                  ? "updaeContractImgClass"
                                  : ""
                              }
                              alt="Icon"
                              src={ic.imgIconSrc}
                              style={{
                                cursor: "pointer",
                              }}
                            />
                          )}
                        </IconButton>
                      </Tooltip>
                    </Tab>
                  );
              })}

              <Tab onClick={() => setIsImageOrVideoTab(false)}>
                <Tooltip
                  title={t("map:mapToolsServices.googleMaps")}
                  placement="top"
                >
                  <IconButton
                    className="tooltipButton"
                    onClick={handleGoogleNavigation}
                  >
                    <IoLocation />
                  </IconButton>
                </Tooltip>
              </Tab>

              {loggedIn &&
                (props.data.layerName == "PROTECTED_AREA_BOUNDARY" ||
                  props.data.layerName == "REGION") && (
                  <Tab
                    onClick={() => {
                      setIsImageOrVideoTab(false);
                    }}
                  >
                    <Tooltip
                      title={t("map:mapToolsServices.barChart")}
                      placement="top"
                    >
                      <IconButton className="tooltipButton">
                        <FaChartBar />
                      </IconButton>
                    </Tooltip>
                  </Tab>
                )}

              {props.data.layerName == "PROTECTED_AREA_BOUNDARY" &&
                props?.mainData?.logged &&
                props?.mainData?.user && (
                  <Tab
                    key={"files"}
                    onClick={() => getProtectedAreaAttachments()}
                  >
                    <Tooltip
                      title={t("map:mapToolsServices.files")}
                      placement="top"
                    >
                      <IconButton className="tooltipButton">
                        <FaFolderOpen />
                      </IconButton>
                    </Tooltip>
                  </Tab>
                )}

              {props.data.layerName == "REGION" && (
                <Tab
                  onClick={() => {
                    setIsImageOrVideoTab(false);
                    getReserves();
                  }}
                >
                  <Tooltip title={t("map:regions")} placement="top">
                    <IconButton className="tooltipButton">
                      <AiOutlineBars />
                    </IconButton>
                  </Tooltip>
                </Tab>
              )}

              {/* start new tabs */}
              {sharedIconsLayers.includes(props.data.layerName) && (
                <>
                  <Tab
                    onClick={() => {
                      getImportantAreasData();
                    }}
                  >
                    <Tooltip title={t("map:important_areas")} placement="top">
                      <IconButton className="tooltipButton">
                        <img
                          src={important_areas_icon}
                          alt=""
                          style={{ width: "17px" }}
                          className="search_tab_img"
                        />
                      </IconButton>
                    </Tooltip>
                  </Tab>

                  <Tab
                    onClick={() => {
                      getEcoSystemData();
                    }}
                  >
                    <Tooltip title={t("map:Ecosystems")} placement="top">
                      <IconButton className="tooltipButton">
                        <img
                          src={eco_system_icon}
                          alt=""
                          style={{ width: "17px" }}
                          className="search_tab_img"
                        />
                      </IconButton>
                    </Tooltip>
                  </Tab>
                </>
              )}
              {/* end new tabs */}
            </div>

            {isImageOrVideoTab && (
              <div>
                <IconButton className="tooltipButton">
                  <LuStretchHorizontal
                    onClick={() => setImagesAndVideosStyle("col")}
                  />
                </IconButton>
                <IconButton className="tooltipButton">
                  <FaGripVertical
                    onClick={() => setImagesAndVideosStyle("row")}
                  />
                </IconButton>
              </div>
            )}
          </TabList>

          {/* المواقع الهامة والأراضي الرطبة */}
          {((props.data.layerName == "PROTECTED_AREA_BOUNDARY" &&
            selectedTabIndex === 6) ||
            (props.data.layerName == "REGION" && selectedTabIndex === 5)) && (
            <TabPanel value={selectedTabIndex} index={selectedTabIndex}>
              <div style={{ padding: "15px" }}>
                <div
                  style={{
                    color: "#fff",
                    fontWeight: "400",
                    fontSize: "16px",
                    textAlign: "start",
                  }}
                >
                  {t("important_areas")}
                </div>

                <div
                // style={{
                //   height: "200px",
                //   backgroundColor: "#ddd",
                //   borderRadius: "10px",
                //   marginBlock: "15px",
                // }}
                >
                  {/* chart here */}
                  {importantAreasDataList.length > 0 && (
                    <div>
                      <BarChart
                        // style={{ cursor: "pointer", display: "inline-block" }}
                        width={400}
                        height={250}
                        data={importantAreasDataList}
                        // margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        barSize={50}
                        // style={{ maxWidth: "100%", paddingInlineStart: "30px" }}
                        style={
                          i18n.language === "ar"
                            ? {
                                maxWidth: "100%",
                                paddingInlineStart: "30px",
                                transform: "scale(1.3)",
                              }
                            : {
                                maxWidth: "100%",
                                paddingInlineEnd: "30px",
                                transform: "scale(1.3)",
                              }
                        }
                      >
                        <XAxis
                          dataKey="translatedName"
                          interval={0}
                          angle={-30}
                          height={80}
                          tick={{
                            fill: "white",
                            fontSize: 12,
                            dy: 10,
                            dx: 0,
                            x: 0,
                            y: 0,
                            textAnchor: "middle",
                            transform: "rotate(0)",
                          }}
                        />
                        <YAxis tick={{ fill: "white", fontSize: 12 }} />
                        {/* <Tooltip content={<CustomTooltip />} /> */}
                        <Bar
                          dataKey="count"
                          fill="#8884d8"
                          fillOpacity={0.8}
                          // cursor="pointer"
                        >
                          {importantAreasDataList.map((item, index) => (
                            <Cell
                              // cursor="pointer"
                              fill={item.color}
                              key={t(`cell-${index}`)}
                            />
                          ))}
                        </Bar>
                      </BarChart>
                    </div>
                  )}
                </div>

                <table
                  style={{
                    color: "#fff",
                    fontWeight: "400",
                    fontSize: "16px",
                    width: "100%",
                  }}
                >
                  {importantAreasDataList.length > 0 &&
                    importantAreasDataList.map((item) => {
                      return (
                        <tr style={{ borderBottom: "1px solid #fff" }}>
                          <td style={{ paddingBlock: "15px" }}>
                            {item.translatedName}
                          </td>
                          <td style={{ paddingBlock: "15px" }}>{item.count}</td>
                        </tr>
                      );
                    })}
                </table>
              </div>
            </TabPanel>
          )}

          {/* النظم البيئية */}
          {((props.data.layerName == "PROTECTED_AREA_BOUNDARY" &&
            selectedTabIndex === 7) ||
            (props.data.layerName == "REGION" && selectedTabIndex === 6)) && (
            <TabPanel value={selectedTabIndex} index={selectedTabIndex}>
              <div style={{ padding: "15px" }}>
                <div
                  style={{
                    color: "#fff",
                    fontWeight: "400",
                    fontSize: "16px",
                    textAlign: "start",
                  }}
                >
                  {t("Ecosystems")}
                </div>

                <div
                // style={{
                //   height: "200px",
                //   width: "200px",
                //   backgroundColor: "#ddd",
                //   borderRadius: "10px",
                //   marginBlock: "15px",
                // }}
                >
                  {ecoSystemDataList.length > 0 && (
                    <PieChart
                      width={400}
                      height={200}
                      style={{
                        maxWidth: "100%",
                        transform: "scale(1.3)",
                        transformOrigin: "center",
                        display: "block",
                        margin: "0 auto",
                      }}
                    >
                      <Pie
                        data={ecoSystemDataList}
                        dataKey="percentage"
                        nameKey="ecoSystemName"
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                      >
                        {ecoSystemDataList.map((item, index) => {
                          return (
                            <Cell key={`cell-${index}`} fill={item.color} />
                          );
                        })}
                      </Pie>
                      <RechartsTooltip
                        contentStyle={{
                          fontSize: "8px",
                          textWrap: "auto",
                          maxWidth: "80px",
                          borderRadius: "5px",
                        }}
                        wrapperStyle={{
                          left: -100,
                          // top: 50,
                        }}
                      />
                    </PieChart>
                  )}
                </div>

                <table
                  style={{
                    color: "#fff",
                    fontWeight: "400",
                    fontSize: "16px",
                    width: "100%",
                  }}
                >
                  {ecoSystemDataList.length > 0 &&
                    ecoSystemDataList.map((item) => {
                      return (
                        <tr style={{ borderBottom: "1px solid #fff" }}>
                          <td
                            style={{ paddingBlock: "15px", maxWidth: "100px" }}
                          >
                            {item.ecoSystemName}
                          </td>
                          <td
                            style={{ paddingBlock: "15px", maxWidth: "100px" }}
                          >
                            {item.area}
                            {props.languageState == "ar" ? " كم" : " km"}
                          </td>
                          <td
                            style={{ paddingBlock: "15px", maxWidth: "100px" }}
                          >
                            {item.percentage}%
                          </td>
                        </tr>
                      );
                    })}
                </table>
              </div>
            </TabPanel>
          )}

          {selectedTabIndex === 5 &&
            props?.mainData?.logged &&
            props?.mainData?.user && (
              <TabPanel value={selectedTabIndex} index={selectedTabIndex}>
                {protectedAreaAttachments?.filter((att) => att)?.length ? (
                  protectedAreaAttachments
                    ?.filter((att) => att)
                    ?.map(({ icon, text, id, link, thumbnail }) => {
                      return (
                        <div
                          key={id}
                          className="generalSearchCard"
                          onClick={() => {
                            window.open(link, "_blank");
                          }}
                          style={{
                            textAlign: "right",
                            display: "flex",
                            gap: "5px",
                            alignItems: "center",
                            wordBreak: "break-word",
                          }}
                        >
                          {icon || thumbnail ? (
                            <img
                              src={icon || thumbnail}
                              style={
                                !thumbnail
                                  ? {}
                                  : {
                                      width: "50px",
                                      objectFit: "cover",
                                      aspectRatio: "1",
                                      padding: "2px",
                                    }
                              }
                              alt={text}
                            />
                          ) : (
                            <FaFolderOpen />
                          )}
                          {text}
                        </div>
                      );
                    })
                ) : (
                  <>
                    {!protectedAreaAttachments ? (
                      <>
                        {" "}
                        <Loader />{" "}
                      </>
                    ) : (
                      <p style={{ color: "white" }} className="noDataStyle">
                        {" "}
                        {t("common:notFilesData")}
                      </p>
                    )}
                  </>
                )}
              </TabPanel>
            )}

          {((selectedTabIndex < 4 &&
            props.data.layerName == "PROTECTED_AREA_BOUNDARY") ||
            (selectedTabIndex < 4 && props.data.layerName == "SPECIE") ||
            (selectedTabIndex < 3 && props.data.layerName == "REGION")) && (
            <TabPanel value={selectedTabIndex} index={selectedTabIndex}>
              <Table
                striped
                responsive
                hover
                className="mt-2 outerSearchDetailTrStyle"
              >
                {(isPrivateRoyalSalesLands &&
                  props?.resultDetailsDataRef.current?.landBaseParcelData) ||
                (!isPrivateRoyalSalesLands && props?.data?.layerName) ? (
                  props.mainData.layers[
                    isPrivateRoyalSalesLands
                      ? PARCEL_LANDS_LAYER_NAME
                      : props.data?.layerName
                  ].outFields
                    .filter(
                      (x) => x !== "OBJECTID" && x.indexOf("SPATIAL_ID") < 0
                    )
                    .map((attribute, index) => {
                      return checkIfHiddenField(
                        props.mainData.layers[
                          isPrivateRoyalSalesLands
                            ? PARCEL_LANDS_LAYER_NAME
                            : props.data?.layerName
                        ],
                        attribute
                      ) ? (
                        <></>
                      ) : (
                        <tr key={index}>
                          <td className="infoTableTd">
                            {props.mainData.layers[
                              isPrivateRoyalSalesLands
                                ? PARCEL_LANDS_LAYER_NAME
                                : props.data?.layerName
                            ]?.notInConfig
                              ? props.languageState === "ar"
                                ? props.mainData.layers[
                                    isPrivateRoyalSalesLands
                                      ? PARCEL_LANDS_LAYER_NAME
                                      : props.data?.layerName
                                  ].aliasOutFields[index]
                                : props.mainData.layers[
                                    isPrivateRoyalSalesLands
                                      ? PARCEL_LANDS_LAYER_NAME
                                      : props.data?.layerName
                                  ].aliasOutFields[index]
                              : props.languageState === "ar" &&
                                props.mainData.layers[
                                  isPrivateRoyalSalesLands
                                    ? PARCEL_LANDS_LAYER_NAME
                                    : props.data?.layerName
                                ].aliasOutFields[index].match(
                                  "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
                                )
                              ? props.mainData.layers[
                                  isPrivateRoyalSalesLands
                                    ? PARCEL_LANDS_LAYER_NAME
                                    : props.data?.layerName
                                ].aliasOutFields[index]
                              : t(
                                  `layers:${
                                    props.mainData.layers[
                                      isPrivateRoyalSalesLands
                                        ? PARCEL_LANDS_LAYER_NAME
                                        : props.data?.layerName
                                    ].aliasOutFields[index]
                                  }`
                                )}
                          </td>
                          <td
                            className="infoTableData"
                            style={{ textAlign: "start" }}
                          >
                            {attribute === "CREATED_DATE" ? (
                              //toArabic(
                              //moment(
                              convertTimeStampToDate(
                                (isPrivateRoyalSalesLands
                                  ? props.resultDetailsDataRef.current
                                      ?.landBaseParcelData?.attributes
                                  : props.data)["CREATED_DATE"]
                              )
                            ) : //).format("iYYYY/iMM/iDD")
                            //)
                            tableInfoData(attribute) ? (
                              <div>
                                {tableInfoData(attribute).length > 40 &&
                                showAllInfoTableData ? (
                                  <div>
                                    {tableInfoData(attribute)}
                                    {tableInfoData(attribute).length > 40 && (
                                      <span
                                        style={{
                                          cursor: "pointer",
                                        }}
                                        onClick={() =>
                                          setShowAllInfoTableData(false)
                                        }
                                      >
                                        <IoIosArrowDown
                                          color="#b45333"
                                          style={{
                                            transform: "rotate(180deg)",
                                          }}
                                        />
                                      </span>
                                    )}
                                  </div>
                                ) : (
                                  <div>
                                    {attribute.toLowerCase().endsWith("_area")
                                      ? tableInfoData(attribute).toFixed(2)
                                      : tableInfoData(attribute).toString().slice(0, 40)}
                                    {tableInfoData(attribute).length > 40 && (
                                      <span
                                        style={{
                                          cursor: "pointer",
                                          color: "#b45333",
                                        }}
                                        onClick={() =>
                                          setShowAllInfoTableData(true)
                                        }
                                      >
                                        <IoIosArrowDown color="#b45333" />
                                      </span>
                                    )}
                                  </div>
                                )}
                              </div>
                            ) : (
                              <div>{t("common:notAvailable")}</div>
                            )}
                          </td>
                        </tr>
                      );
                    })
                ) : isPrivateRoyalSalesLands &&
                  !props.resultDetailsDataRef.current?.landBaseParcelData &&
                  !loading ? (
                  <p className="noDataStyle"> {t("common:notAvailableData")}</p>
                ) : (
                  isPrivateRoyalSalesLands &&
                  !props.resultDetailsDataRef.current?.landBaseParcelData &&
                  loading && <Spinner />
                )}
              </Table>
            </TabPanel>
          )}

          {((props.data.layerName == "REGION" && selectedTabIndex === 3) ||
            (props.data.layerName == "PROTECTED_AREA_BOUNDARY" &&
              selectedTabIndex === 4)) && (
            <>
              {loggedIn && (
                <div>
                  <MultiSelectBarChart
                    chartData={chartData}
                    languageState={props.languageState}
                    map={props.map}
                    reserveName={
                      props.languageState == "ar"
                        ? props.data.layerName == "PROTECTED_AREA_BOUNDARY"
                          ? props.data.AR_PROTECTED_AREA_NAME
                          : props.data.AR_REGION_NAME
                        : props.data.layerName == "PROTECTED_AREA_BOUNDARY"
                        ? props.data.EN_PROTECTED_AREA_NAME
                        : props.data.EN_REGION_NAME
                    }
                    fieldName={
                      props.languageState == "ar"
                        ? props.data.layerName == "PROTECTED_AREA_BOUNDARY"
                          ? "AR_PROTECTED_AREA_NAME"
                          : "AR_REGION"
                        : props.data.layerName == "PROTECTED_AREA_BOUNDARY"
                        ? "EN_PROTECTED_AREA_NAME"
                        : "EN_REGION"
                    }
                  />
                </div>
              )}
            </>
          )}

          {props.data.layerName == "REGION" &&
            selectedTabIndex === 4 &&
            reservesNamesValues.length > 0 && (
              <>
                <List
                  bordered
                  dataSource={reservesNamesValues}
                  renderItem={(item) => (
                    <List.Item
                      // style={{ backgroundColor: "#1f1f1f" }}
                      // className="flex items-center gap-2 !justify-start"
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "start",
                        gap: "5px",
                      }}
                    >
                      <div
                        style={{
                          width: "8px",
                          height: "8px",
                          // marginTop: "8px",
                          backgroundColor: "#b45333",
                          borderRadius: "50%",
                        }}
                      />
                      <Text strong style={{ color: "white" }}>
                        {item}
                      </Text>
                    </List.Item>
                  )}
                />
              </>
            )}

          {openGalleryModal && (
            <div
              style={{
                display: "grid",
                gap: "8px",
                gridTemplateColumns:
                  imagesAndVideosStyle === "col"
                    ? "repeat(1, 1fr)"
                    : "repeat(2, 1fr)",
              }}
            >
              {galleryData && (
                <GalleryModal
                  gallery_data={galleryData}
                  openGalleryModal={openGalleryModal}
                  setOpenGalleryModal={setOpenGalleryModal}
                />
              )}
            </div>
          )}
        </Tabs>
      )}
    </div>
  );
}
export default OuterSearchResultDetails;
