import React, { useState, useEffect, useRef } from "react";
import { Modal, Row, Col, message } from "antd";
import { Table } from "react-bootstrap";
import Sunburst from "sunburst-chart";
import generateRandomColor from "../../helper/utilsFunc";
import { useTranslation } from "react-i18next";

import { json, useNavigate } from "react-router-dom";
import PieChartComponent from "./PieChartComponent";
import { getLayerId, queryTask } from "../../helper/common_func";

const MetaDataStatistics = (props) => {
  console.log("MetaDataStatistics props", props);
  const navigate = useNavigate();
  const handleOk = async () => {};
  const { t } = useTranslation("print");
  const [chartData, setChartData] = useState();
  const [chartColors, setChartColors] = useState();
  const [statColorsTable, setStatColorsTable] = useState([]);
  const [hoverData, setHoverData] = useState(null);

  const sunBurstChartRef = useRef(null);
  useEffect(() => {
    let settedData = [];
    let dataForChart;
    let dataColors;

    if (props.showMetaStat) {
      if (props.showMetaStat.isFromDomain) {
        settedData =
          props.showMetaStat.appliedStatisticsField.domain.codedValues.map(
            (c) => ({
              id: c.code,
              report: c.name,
              color: generateRandomColor(),
            })
          );

        dataForChart = props.showMetaStat.data.map((slice) => ({
          name: slice[props.showMetaStat.appliedStatisticsField.name],
          value: slice.countResult,
        }));

        dataColors = dataForChart
          .map((data) => {
            const match = settedData.find((obj) => obj.report === data.name);
            return match ? match.color : null;
          })
          .filter((color) => color !== null);
      } else {
        settedData = props.showMetaStat.data.map((c) => ({
          report: c[props.showMetaStat.appliedStatisticsField.name],
          color: generateRandomColor(),
          count: c.countResult,
        }));

        dataForChart = settedData.map((data) => ({
          name: data.report,
          value: data.count,
        }));
        dataColors = settedData.map((data) => data.color);
      }
      console.log(settedData);
      console.log(dataForChart);
      console.log(dataColors);
      setStatColorsTable(settedData);
      setChartData(dataForChart);
      setChartColors(dataColors);

      if (props.showMetaStat.layerData.layerName == "SPECIE") {
        let sunBurstData = [];
        let layerId = getLayerId(
          props.map.__mapInfo,
          props.showMetaStat.layerData.layerName
        );

        let queryPromises = dataForChart.map((item, index) => {
          return new Promise((resolve) => {
            let whereForChart =
              props.showMetaStat.where != "1=1"
                ? `${props.showMetaStat.appliedStatisticsField.name}='${item.name}' AND ${props.showMetaStat.where}`
                : `${props.showMetaStat.appliedStatisticsField.name}='${item.name}'`;
            queryTask({
              url: window.mapUrl + "/" + layerId,
              where: whereForChart,
              groupByFields: ["SPECIE_ALIAS"],
              statistics: [
                { type: "count", field: "SPECIE_ALIAS", name: "SPECIE_COUNT" },
              ],
              callbackResult: ({ features }) => {
                let childs = features
                  .filter((item) => item.attributes.SPECIE_ALIAS)
                  .map((feat) => ({
                    name: feat.attributes.SPECIE_ALIAS,
                    value: feat.attributes.SPECIE_COUNT,
                    color: generateRandomColor(),
                    size: feat.attributes.SPECIE_COUNT,
                  }));

                let childrenObject = {
                  name: item.name,
                  value: item.value,
                  color: dataColors[index],
                  children: childs,
                  size: item.value,
                };

                sunBurstData.push(childrenObject);
                resolve();
              },
              callbackError: (error) => {
                console.error("Error in queryTask:", error);
                resolve();
              },
            });
          });
        });

        Promise.all(queryPromises).then(() => {
          let data = {
            name: t("totalNumber"),
            color: "#65DD91",
            children: sunBurstData,
          };
          let chartData = sunBurstData.map((item) => {
            return {
              name: item.name,
              value: item.value,
            };
          });

          let childrenData = sunBurstData.flatMap((obj) =>
            obj.children.map((item) => {
              return {
                name: item.name,
                value: item.value,
              };
            })
          );

          let chartColors = sunBurstData.map((item) => item.color);
          let childColors = sunBurstData.flatMap((obj) =>
            obj.children.map((item) => item.color)
          );

          chartData.push(...childrenData);
          chartColors.push(...childColors);
          Sunburst()
            .data(data)
            .label("name")
            .size("size")
            .color("color")
            .showLabels(false)
            .width(400)
            .radiusScaleExponent(0.8)
            .onHover((e) => {
              if (e) {
                if (e.name != t("totalNumber")) {
                  setHoverData({
                    landUse: e.name ? e.name : null,
                    count: e.value ? e.value : null,
                  });
                } else {
                  setHoverData({
                    landUse: e.name ? e.name : null,
                    count: props.showMetaStat.totalCount,
                  });
                }
              }
            })
            .tooltipContent((d, node) => `<i>${node.value}</i>`)(
            sunBurstChartRef.current
          );
        });
      }
    } else {
      if (hoverData) setHoverData(null);
      if (statColorsTable.length) setStatColorsTable([]);
    }

    return () => {
      setHoverData(null);
      setStatColorsTable([]);
      props.openMetaStat(false);
    };
  }, [props.showMetaStat]);

  const handleOnHoverTable = (data) => {
    if (props.showMetaStat.layerData.layerName != "REGION") {
      setHoverData({
        landUse: data.name,
        count: data.value,
      });
    } else {
      let whereField =
        localStorage.getItem("lang") == "en" ? "EN_REGION" : "AR_REGION";
      let groupByField =
        localStorage.getItem("lang") == "en"
          ? "EN_NCW_CATEGORY"
          : "AR_NCW_CATEGORY";
      let where = `${whereField}='${data.name}'`;
      let layerID = getLayerId(props.map.__mapInfo, "SPECIE");
      let queryParams = {
        url: window.mapUrl + "/" + layerID,
        notShowLoading: true,
        returnGeometry: false,
        outFields: [groupByField],
        groupByFields: [groupByField],
        statistics: [
          {
            field: groupByField,
            name: "category_count",
            type: "count",
          },
        ],
        where: where,
      };
      queryTask({
        ...queryParams,
        callbackResult: ({ features }) => {
          let categoriesList = features.map((item) => {
            return {
              name: item.attributes[groupByField],
              count: item.attributes.category_count,
            };
          });
          setHoverData({
            landUse: data.name,
            count: data.value,
            categoriesList,
          });
        },
        callbackError: (err) => {
          console.log(err);
        },
      });
    }
  };

  const handleExportPDFStatistics = () => {
    localStorage.setItem("ChartData", JSON.stringify(chartData));
    localStorage.setItem("ChartColors", JSON.stringify(chartColors));

    localStorage.setItem(
      "layerData",
      JSON.stringify(props.showMetaStat.layerData)
    );
    localStorage.setItem(
      "statisticsFieldName",
      JSON.stringify(props.showMetaStat.appliedStatisticsField.name)
    );
    localStorage.setItem(
      "totalCount",
      props.showMetaStat?.data?.reduce(
        (acc, item) => acc + (item.countResult || 0),
        0
      ) || props.showMetaStat.totalCount
    );

    let layerId = getLayerId(
      props.map.__mapInfo,
      props.showMetaStat.layerData.layerName
    );

    localStorage.setItem("layerId", layerId);

    window.open(process.env.PUBLIC_URL + "/PrintPdfAttrTbl", "_blank");
  };
  return (
    <Modal
      className="metaStatModal"
      visible={props.showMetaStat ? true : false}
      onOk={handleOk}
      onCancel={props.openMetaStat}
      width={"50%"}
    >
      <h5 className="statTitle mb-5">
        {props.showMetaStat.layerData.layerName == "REGION"
          ? "التنوع الاحيائي بالمناطق"
          : props.showMetaStat.layerData.layerMetadata.arname}
      </h5>
      {props.showMetaStat.layerData.layerName == "REGION" && (
        <Row align="top">
          <Col
            md={{ span: 24 }}
            lg={{ span: 8 }}
            style={{ height: "600px", overflow: "auto" }}
          >
            <Table className="table  metastatTable" style={{ width: "100%" }}>
              <thead>
                <th>{t("region")}</th>
                <th>{t("totalNumber")}</th>
                <th> {t("drawKey")} </th>
              </thead>

              <tbody>
                {statColorsTable.map((s, index) => (
                  <tr onMouseEnter={() => handleOnHoverTable(chartData[index])}>
                    <td>{s.report}</td>
                    <td>{s.count}</td>
                    <td>
                      <p
                        className="colorBall"
                        style={{ background: s.color }}
                      ></p>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Col>
          <Col md={{ span: 24 }} lg={{ span: 8 }}>
            {chartData && (
              <PieChartComponent
                chartData={chartData}
                chartColors={chartColors}
                handleOnHoverTable={handleOnHoverTable}
                layerName={props.showMetaStat.layerData.layerName}
                map={props.map}
              />
            )}
          </Col>{" "}
          <Col lg={{ span: 2 }}></Col>
          <Col md={{ span: 24 }} lg={{ span: 6 }}>
            {hoverData ? (
              <Table className="table metastatTable2">
                {hoverData.landUse ? (
                  <tr>
                    <th> {t(t("region"))}</th>
                    <td>{hoverData.landUse}</td>
                  </tr>
                ) : null}
                <tr>
                  <th> {t("totalNumber")}</th>
                  <td>{hoverData.count}</td>
                </tr>
                {hoverData.categoriesList.map((item) => (
                  <tr>
                    <th> {item.name}</th>
                    <td>{item.count}</td>
                  </tr>
                ))}
              </Table>
            ) : null}
          </Col>
        </Row>
      )}
      {props.showMetaStat.layerData.layerName == "PROTECTED_AREA_BOUNDARY" && (
        <Row align="top">
          <Col
            md={{ span: 24 }}
            lg={{ span: 8 }}
            style={{ height: "600px", overflow: "auto" }}
          >
            <Table className="table  metastatTable" style={{ width: "100%" }}>
              <thead>
                <th>{t("statement")}</th>
                <th>{t("percent")}</th>
                <th> {t("drawKey")} </th>
              </thead>

              <tbody>
                {statColorsTable.map((s, index) => (
                  <tr onMouseEnter={() => handleOnHoverTable(chartData[index])}>
                    <td>{s.report}</td>
                    <td>
                      {parseFloat(
                        (chartData[index].value /
                          props.showMetaStat.totalCount) *
                          100
                      ).toFixed(2)}
                      {props.showMetaStat.layerData.layerName != "REGION"
                        ? "%"
                        : ""}
                    </td>

                    <td>
                      <p
                        className="colorBall"
                        style={{ background: s.color }}
                      ></p>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Col>
          <Col md={{ span: 24 }} lg={{ span: 8 }}>
            {/* <div className="sunburst-chart" ref={chartRef}></div> */}
            {chartData && (
              <PieChartComponent
                chartData={chartData}
                chartColors={chartColors}
                handleOnHoverTable={handleOnHoverTable}
              />
            )}
          </Col>{" "}
          <Col lg={{ span: 2 }}></Col>
          <Col md={{ span: 24 }} lg={{ span: 6 }}>
            {hoverData ? (
              <Table className="table metastatTable2">
                {hoverData.landUse ? (
                  <tr>
                    <th>
                      {" "}
                      {t(props.showMetaStat.appliedStatisticsField.name)}
                    </th>
                    <td>{hoverData.landUse}</td>
                  </tr>
                ) : null}
                <tr>
                  <th> {t("totalNumber")}</th>
                  <td>{hoverData.count}</td>
                </tr>
                <tr></tr>
                <tr>
                  <th>{t("ratio")}</th>
                  <td>
                    {parseFloat(
                      (
                        (hoverData.count / props.showMetaStat.totalCount) *
                        100
                      ).toFixed(2)
                    )}{" "}
                    %{" "}
                  </td>
                </tr>
              </Table>
            ) : null}
          </Col>
        </Row>
      )}
      {props.showMetaStat.layerData.layerName == "SPECIE" && (
        <>
          <Row align="top">
            <Col
              md={{ span: 24 }}
              lg={{ span: 8 }}
              style={{ height: "600px", overflow: "auto" }}
            >
              <Table className="table  metastatTable" style={{ width: "100%" }}>
                <thead>
                  <th>{t("statement")}</th>
                  <th> {t("drawKey")} </th>
                </thead>

                <tbody>
                  {statColorsTable.map((s, index) => (
                    <tr
                      onMouseEnter={() => handleOnHoverTable(chartData[index])}
                    >
                      <td>{s.report}</td>
                      <td>
                        <p
                          className="colorBall"
                          style={{ background: s.color }}
                        ></p>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Col>
            <Col md={{ span: 24 }} lg={{ span: 8 }}>
              <div ref={sunBurstChartRef}></div>
            </Col>{" "}
            <Col lg={{ span: 2 }}></Col>
            <Col md={{ span: 24 }} lg={{ span: 6 }}>
              {hoverData ? (
                <Table className="table metastatTable2">
                  {hoverData.landUse ? (
                    <tr>
                      <th>
                        {" "}
                        {t(props.showMetaStat.appliedStatisticsField.name)}
                      </th>
                      <td>{hoverData.landUse}</td>
                    </tr>
                  ) : null}
                  <tr>
                    <th> {t("totalNumber")}</th>
                    <td>{hoverData.count}</td>
                  </tr>
                  <tr>
                    {/* <th>{t("spaceM2")}</th>{" "}
                <td>
                  {hoverData.area} {t("km2")}
                </td> */}
                  </tr>
                </Table>
              ) : null}
            </Col>
          </Row>
        </>
      )}
      <div className="metaStatBtns">
        {" "}
        <button
          onClick={props.openMetaStat}
          className="SearchBtn mt-3 w-25"
          size="large"
          htmlType="submit"
        >
          {t("close")}
        </button>
        <button
          onClick={handleExportPDFStatistics}
          className="SearchBtn mt-3 w-25"
          size="large"
        >
          {t("ExtractFile")} PDF
        </button>
      </div>
    </Modal>
  );
};

export default MetaDataStatistics;
