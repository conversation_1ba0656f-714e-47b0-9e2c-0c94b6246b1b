import { Button, message, Modal, Input, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { Md<PERSON>eyboardArrowDown } from "react-icons/md";
import { SketchPicker } from "react-color";
import tools_logo from "../../assets/images/interactive-map/tools_logo.svg";
import tool_1 from "../../assets/images/interactive-map/tool_1.svg";
import tool_2 from "../../assets/images/interactive-map/tool_2.svg";
import tool_3 from "../../assets/images/interactive-map/tool_3.svg";
import tool_4 from "../../assets/images/interactive-map/tool_4.svg";
import tool_5 from "../../assets/images/interactive-map/tool_5.svg";
import tool_7 from "../../assets/images/interactive-map/tool_7.svg";
import tool_8 from "../../assets/images/interactive-map/tool_8.svg";
import tool_9 from "../../assets/images/interactive-map/tool_9.svg";
import tool_10 from "../../assets/images/interactive-map/tool_10.svg";
import tool_11 from "../../assets/images/interactive-map/tool_11.svg";
import tool_12 from "../../assets/images/interactive-map/tool_12.svg";
import draw_text from "../../assets/images/interactive-map/drawText.svg";
import add_file_icon from "../../assets/images/interactive-map/add_file.svg";

import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import Sketch from "@arcgis/core/widgets/Sketch";
import Color from "@arcgis/core/Color";
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine";
import { drawText } from "../../helper/common_func";
import axios from "axios";

function ToolsBox(props, ref) {
  useImperativeHandle(ref, () => ({
    cancelDraw,
    handleSelectTool,
  }));

  const { t } = useTranslation("sidemenu");

  const tools = [
    {
      name: "straight-line",
      src: tool_1,
      label: t("straight_line", { ns: "common" }),
    },
    { name: "free-hand", src: tool_2, label: t("free_hand", { ns: "common" }) },
    {
      name: "line-with-arrows",
      src: tool_3,
      label: t("line_with_arrows", { ns: "common" }),
    },
    { name: "rectangle", src: tool_4, label: t("rectangle", { ns: "common" }) },
    { name: "circle", src: tool_5, label: t("circle", { ns: "common" }) },
    {
      name: "half-spinner",
      src: tool_7,
      label: t("half_spinner", { ns: "common" }),
    },
    {
      name: "line-right-arrow",
      src: tool_8,
      label: t("line_right_arrow", { ns: "common" }),
    },
    {
      name: "line-left-arrow",
      src: tool_9,
      label: t("line_left_arrow", { ns: "common" }),
    },
    {
      name: "location_marker",
      src: tool_10,
      label: t("location", { ns: "common" }),
    },
    {
      name: "add-image",
      src: tool_11,
      label: t("imageUploadSettings", { ns: "common" }),
    },
    {
      name: "add-file",
      src: add_file_icon,
      label: t("fileUploadSettings", { ns: "common" }),
    },
    {
      name: "pointText",
      src: draw_text,
      label: t("point_text", { ns: "common" }),
    },
  ];
  const componentRef = useRef({});
  const { current: sketch } = componentRef;
  const activeSketchName = useRef({});
  const activeDelete = useRef(false);
  const [showTools, setShowTools] = useState(true);
  const [selectedColor, setSelectedColor] = useState("#F5A623");
  const [showColorPicker, setShowColorPicker] = useState(false);

  const textPointRef = useRef(null);
  const [textModalVisible, setTextModalVisible] = useState(false);

  useEffect(() => {
    sketch.current = new Sketch({
      layer: props.map.findLayerById("InteractiveMapGraphicLayer"),
      view: props.map.view,
      defaultUpdateOptions: {
        tool: "",
      },
    });

    sketch.current.on("create", (event) => {
      if (event.state == "complete" && activeDelete.current) {
        let intersectedGeometries = [];

        let graphicLayer = props.map.findLayerById(
          "InteractiveMapGraphicLayer"
        );
        if (graphicLayer.graphics.items.length > 0) {
          for (
            let index = 0;
            index < graphicLayer.graphics.items.length;
            index++
          ) {
            let graphic = graphicLayer.graphics.items[index];
            if (event.graphic) {
              if (
                geometryEngine.intersects(
                  event.graphic.geometry,
                  graphic.geometry
                ) &&
                graphic._layerName
              ) {
                window.__isInteractiveMapSaved = false;
                intersectedGeometries.push(graphic);
                if (graphic.has_animation) {
                  intersectedGeometries.push(
                    graphicLayer.graphics.items[index + 1]
                  );
                  index++;
                }
              }
            }
          }
          if (intersectedGeometries.length === 1) {
            props.history.current.undoStack.push({
              action: "delete",
              graphic: intersectedGeometries[0],
            });
          } else {
            props.history.current.undoStack.push(intersectedGeometries);
          }
        }
        graphicLayer.remove(event.graphic);
        graphicLayer.removeMany(intersectedGeometries);
      } else if (event.state == "complete" && !activeDelete.current) {
        window.__isInteractiveMapSaved = false;
        if (activeSketchName.current == "pointText") {
          props.map
            .findLayerById("InteractiveMapGraphicLayer")
            .remove(event.graphic);
          if (textPointRef.current.input.value) {
            let textGraphic = drawText(
              event.graphic,
              textPointRef.current.input.value,
              props.map,
              "InteractiveMapGraphicLayer",
              15,
              3,
              3,
              event.graphic.symbol.color
            );

            textGraphic._layerName = "Tools_InteractiveGraphicLayer";
            props.history.current.undoStack.push({
              action: "add",
              graphic: textGraphic,
            });
          }
        } else if (event.graphic) {
          event.graphic._layerName = "Tools_InteractiveGraphicLayer";
          props.history.current.undoStack.push({
            action: "add",
            graphic: event.graphic,
          });
        }
      }
    });

    return () => {
      cancelDraw();
      sketch.current.destroy();
    };
  }, []);

  useEffect(() => {
    if (props.generalSelectedItem) {
      handleSelectTool(props.generalSelectedItem);
    }
  }, [selectedColor]);

  const drawPolyLine = () => {
    activeSketchName.current = "polyline";
    sketch.current.viewModel.polylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      style: "solid",
    };
    sketch.current.create("polyline");
  };

  const drawLineWithTwoArrows = () => {
    activeSketchName.current = "polylineWithTwoArrows";
    const arrowPolylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      marker: {
        type: "simple-line",
        color: selectedColor,
        width: 2,
      },
      style: "solid",
      cap: "butt",
      marker: {
        style: "arrow",
        placement: "begin-end",
      },
    };

    sketch.current.viewModel.polylineSymbol = arrowPolylineSymbol;
    sketch.current.create("polyline");
  };

  const drawLineWithRightArrow = () => {
    activeSketchName.current = "polylineWithRighArrow";
    const arrowPolylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      marker: {
        type: "simple-line",
        color: selectedColor,
        width: 2,
      },
      style: "solid",
      cap: "butt",
      marker: {
        style: "arrow",
        placement: "end",
      },
    };

    sketch.current.viewModel.polylineSymbol = arrowPolylineSymbol;
    sketch.current.create("polyline");
  };

  const drawLineWithLeftArrow = () => {
    activeSketchName.current = "polylineWithLeftArrow";
    const arrowPolylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      marker: {
        type: "simple-line",
        color: selectedColor,
        width: 2,
      },
      style: "solid",
      cap: "butt",
      marker: {
        style: "arrow",
        placement: "begin",
      },
    };

    sketch.current.viewModel.polylineSymbol = arrowPolylineSymbol;
    sketch.current.create("polyline");
  };

  const drawRectangle = () => {
    activeSketchName.current = "rectangle";
    let color = Color.fromHex(selectedColor);
    color.a = 0.15;
    sketch.current.viewModel.polygonSymbol = {
      type: "simple-fill",
      color: color,
      outline: {
        color: selectedColor,
        width: 2,
      },
    };
    sketch.current.create("rectangle");
  };

  const drawCircle = () => {
    activeSketchName.current = "circle";
    let color = Color.fromHex(selectedColor);
    color.a = 0.1;
    sketch.current.viewModel.polygonSymbol = {
      type: "simple-fill",
      color: color,
      outline: {
        color: selectedColor,
        width: 2,
      },
    };
    sketch.current.create("circle");
  };

  const drawPolyLineFreeHand = () => {
    activeSketchName.current = "freeHand";
    sketch.current.viewModel.polylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      style: "solid",
    };
    sketch.current.create("polyline", { mode: "freehand" });
  };

  const cancelDraw = () => {
    sketch.current.cancel();
  };

  const [warningMessage, setWarningMessage] = useState(false);

  const drawTextHandle = () => {
    if (
      textPointRef.current.input.value === "" ||
      textPointRef.current.input.value.trim().length == 0
    ) {
      setWarningMessage(true);
    } else {
      setWarningMessage(false);
      sketch.current.viewModel.pointSymbol = {
        type: "simple-marker",
        style: "circle",
        color: selectedColor,
        size: "6px",
        outline: {
          color: selectedColor,
          width: 2,
        },
      };

      sketch.current.create("point");
      activeSketchName.current = "pointText";
      setTextModalVisible(false);
    }
  };

  const handleDeleteIcon = () => {
    activeDelete.current = true;
    cancelDraw();
    sketch.current.viewModel.polygonSymbol = {
      type: "simple-fill",
      color: [0, 0, 0, 0.1],
      outline: {
        color: [0, 0, 0],
        width: 2,
      },
    };

    sketch.current.create("rectangle");
  };

  const handleSelectTool = (tool) => {
    if (
      props.generalSelectedItem &&
      props.generalSelectedItem.index === tool.index &&
      props.generalSelectedBox === "tools"
    ) {
      props.setGeneralSelectedItem(null);
      props.setGeneralSelectedBox("");
      props.updateGraphicState(undefined);

      return;
    }

    if (tool.name != "delete") {
      activeDelete.current = false;
      cancelDraw();
    }
    switch (tool.name) {
      case "straight-line":
        drawPolyLine();
        break;

      case "free-hand":
        drawPolyLineFreeHand();
        break;
      case "line-with-arrows":
        drawLineWithTwoArrows();
        break;
      case "circle":
        drawCircle();
        break;
      case "rectangle":
        drawRectangle();
        break;

      case "line-right-arrow":
        drawLineWithRightArrow();
        break;

      case "line-left-arrow":
        drawLineWithLeftArrow();
        break;

      case "delete":
        handleDeleteIcon();
        break;

      case "add-image":
        handleImageUpload();
        break;

      case "add-file":
        handleFileUpload();
        break;

      case "pointText":
        if (!showColorPicker) {
          showTextInput();
        }
        break;

      default:
        cancelDraw();
        break;
    }
    props.setGeneralSelectedItem(tool);
    props.setGeneralSelectedBox("tools");
    if (tool.name == "half-spinner" || tool.name == "location_marker") {
      props.updateGraphicState({
        graphicsLayerName: "Tools_InteractiveGraphicLayer",
        symbolName: tool.name,
      });
    } else {
      props.updateGraphicState(undefined);
    }
  };

  const handleRemoveAll = () => {
    props.setGeneralSelectedItem(null);
    props.setGeneralSelectedBox("");
    let graphicLayer = props.map.layers.items.find(
      (layer) => layer.id == "InteractiveMapGraphicLayer"
    );

    let graphicsToRemove = graphicLayer.graphics.items.filter(
      (graphic) => graphic._layerName == "Tools_InteractiveGraphicLayer"
    );
    if (graphicsToRemove.length > 0) {
      props.history.current.undoStack.push(graphicsToRemove);
      graphicLayer.graphics.removeMany(graphicsToRemove);
    }
    props.updateGraphicState(undefined);
  };

  const showTextInput = () => {
    setTextModalVisible(true);
  };

  //// image upload ////

  const [uploadedImageUrl, setUploadedImageUrl] = useState();
  const [showUploadImageModal, setShowUploadImageModal] = useState(false);

  const handleImageUpload = () => {
    if (!uploadedImageUrl) {
      document.getElementById("imageUpload").click();
    } else {
      setShowUploadImageModal(true);
    }
  };

  const handleImageFileChange = async (event) => {
    const file = event.target.files[0];
    const maxSize = 2 * 1024 * 1024;
    const allowedExtensions = ["image/png", "image/jpeg", "image/gif"];
    if (file) {
      if (file.size > maxSize) {
        message.warning(t("invalidFileSize", { ns: "common" }));
        return;
      } else if (!allowedExtensions.includes(file.type)) {
        message.warning(t("invalidFileType", { ns: "common" }));
        return;
      } else {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        await postImageFile(file);
      }
    }
  };

  const postImageFile = async (file) => {
    const formData = new FormData();
    formData.append(`file[${0}]`, file);

    await axios
      .post(window.ApiUrl + "uploadMultifiles", formData)
      .then((response) => {
        setUploadedImageUrl(
          (prevState) => `${window.filesURL}/${response.data[0].data}`
        );
        setShowUploadImageModal(false);
        drawUploadedFile(`${window.filesURL}/${response.data[0].data}`);
      })
      .catch((error) => {
        message.warning(t("ErrorRequest", { ns: "common" }));
      });
  };

  const drawUploadedFile = (imageUrl, fileURL) => {
    props.updateGraphicState({
      graphicsLayerName: "Tools_InteractiveGraphicLayer",
      symbolName: imageUrl,
      fileURL,
    });
  };

  //// file upload ////

  const [uploadedFile, setUploadedFile] = useState();
  const [fileUrl, setFileUrl] = useState();
  const [showUploadFileModal, setShowUploadFileModal] = useState(false);

  const handleFileUpload = () => {
    if (!uploadedFile) {
      document.getElementById("fileUpload").click();
    } else {
      setShowUploadFileModal(true);
    }
  };

  const handleFileChange = async (event) => {
    const file = event.target.files[0];
    const allowedExtensions = [
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/pdf",
    ];
    if (file) {
      if (!allowedExtensions.includes(file.type)) {
        message.warning(t("invalidFileType", { ns: "common" }));
        return;
      } else {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        await postFile(file);
      }
    }
  };

  const postFile = async (file) => {
    const formData = new FormData();
    formData.append(`file[${0}]`, file);

    await axios
      .post(window.ApiUrl + "uploadMultifiles", formData)
      .then((response) => {
        setShowUploadFileModal(false);
        let fileUrl = `${window.filesURL}/${response.data[0].data}`;
        setFileUrl(fileUrl);
        switch (file.type) {
          case "application/pdf":
            setUploadedFile("pdf");
            drawUploadedFile("pdf", fileUrl);
            break;
          case "application/vnd.ms-excel":
            setUploadedFile("excel");
            drawUploadedFile("excel", fileUrl);
            break;
          case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
            setUploadedFile("excel");
            drawUploadedFile("excel", fileUrl);
            break;
          case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            setUploadedFile("word");
            drawUploadedFile("word", fileUrl);
            break;
          case "application/msword":
            setUploadedFile("word");
            drawUploadedFile("word", fileUrl);
            break;
        }
      })
      .catch((error) => {
        message.warning(t("ErrorRequest", { ns: "common" }));
      });
  };

  return (
    <>
      <div className="box">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            color: "#fff",
            cursor: "pointer",
          }}
          onClick={() => setShowTools(!showTools)}
        >
          <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
            <img src={tools_logo} alt="tools logo" />
            <span>{t("drawingShape")}</span>
          </div>

          <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
            <Button
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveAll();
              }}
            >
              {t("clearAll")}
            </Button>
            <MdKeyboardArrowDown
              size={20}
              style={{
                // cursor: "pointer",
                transform: `rotate(${!showTools ? "180deg" : 0})`,
              }}
              // onClick={() => setShowTools(!showTools)}
            />
          </div>
        </div>

        {showTools && (
          <div
            className="images"
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(6, 1fr)",
              gap: "10px",
              marginTop: "10px",
            }}
          >
            <input
              type="file"
              id="imageUpload"
              style={{ display: "none" }}
              onChange={handleImageFileChange}
              accept=".gif, .png, .jpg, .jpeg"
            />
            <input
              type="file"
              id="fileUpload"
              style={{ display: "none" }}
              onChange={handleFileChange}
              accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,
                .xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,
                application/pdf"
            />
            {tools.map((tool, indx) => (
              <Tooltip title={tool.label} placement="bottom">
                <div
                  key={indx}
                  name={tool.name}
                  className="image"
                  style={{
                    background: props.generalSelectedItem
                      ? props.generalSelectedItem.index === indx &&
                        props.generalSelectedBox === "tools"
                        ? "#B55433"
                        : "transparent"
                      : "transparent",
                  }}
                  onClick={() => {
                    handleSelectTool({
                      name: tool.name,
                      src: tool.src,
                      index: indx,
                    });
                  }}
                >
                  <img
                    src={tool.src}
                    alt="tool"
                    style={{
                      filter: props.generalSelectedItem
                        ? props.generalSelectedItem.index === indx &&
                          props.generalSelectedBox === "tools"
                          ? "brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(2%) hue-rotate(264deg) brightness(105%) contrast(101%)"
                          : ""
                        : "",
                    }}
                  />
                </div>
              </Tooltip>
            ))}

            {/* start color picker */}
            <div
              className="image"
              style={{
                background: selectedColor,
              }}
              onClick={() => {
                setShowColorPicker(!showColorPicker);
              }}
            >
              <img
                src={tool_12}
                alt="select color"
                style={{
                  filter:
                    selectedColor === "#ffffff"
                      ? "brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(7500%) hue-rotate(16deg) brightness(94%) contrast(106%)"
                      : "",
                }}
              />
            </div>

            {showColorPicker && (
              <div
                style={{
                  position: "absolute",
                  direction: "ltr",
                }}
              >
                <SketchPicker
                  color={selectedColor}
                  onChange={(color) => {
                    setSelectedColor(color.hex);
                    setTextModalVisible(false);
                    setShowColorPicker(false);
                  }}
                />
              </div>
            )}
            {/* end color picker */}
          </div>
        )}
      </div>
      <>
        <Modal
          title={t("enterText", { ns: "common" })}
          centered
          visible={textModalVisible}
          onCancel={() => {
            setTextModalVisible(false);
            setWarningMessage(false);
          }}
          okText={t("edit")}
          cancelText={t("cancel")}
        >
          <Input
            name="pointText"
            maxLength={70}
            ref={textPointRef}
            placeholder={t("enterText2", { ns: "common" })}
            onChange={(e) => {
              setWarningMessage(
                e.target.value === "" || e.target.value.trim().length == 0
              );
            }}
          />
          {warningMessage && (
            <span
              style={{
                display: "inline-flex",
                alignItems: "center",
                padding: "4px 8px",
                borderRadius: "4px",
                fontSize: "16px",
                color: "#ff3333",
              }}
            >
              <span style={{ marginRight: "4px", fontSize: "18px" }}>⚠️</span>
              <span style={{ fontWeight: "bold" }}>
                {t("enterText", { ns: "common" })}
              </span>
            </span>
          )}
          <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                drawTextHandle();
              }}
            >
              {t("confirm", { ns: "common" })}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                setTextModalVisible(false);
                setWarningMessage(false);
              }}
            >
              {t("close", { ns: "common" })}
            </Button>
          </div>
        </Modal>

        <Modal
          title={t("useSameImageUrl", { ns: "common" })}
          centered
          visible={showUploadImageModal}
          onCancel={() => setShowUploadImageModal(false)}
          okText={t("yes")}
          cancelText={t("no")}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                document.getElementById("imageUpload").click();
              }}
            >
              {t("yes", { ns: "common" })}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                setShowUploadImageModal(false);
                drawUploadedFile(uploadedImageUrl);
              }}
            >
              {t("no", { ns: "common" })}
            </Button>
          </div>
        </Modal>
        <Modal
          title={t("useSameFileUrl", { ns: "common" })}
          centered
          visible={showUploadFileModal}
          onCancel={() => setShowUploadFileModal(false)}
          okText={t("yes")}
          cancelText={t("no")}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                document.getElementById("fileUpload").click();
              }}
            >
              {t("yes", { ns: "common" })}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                setShowUploadFileModal(false);
                drawUploadedFile(uploadedFile, fileUrl);
              }}
            >
              {t("no", { ns: "common" })}
            </Button>
          </div>
        </Modal>
      </>
    </>
  );
}
export default forwardRef(ToolsBox);
