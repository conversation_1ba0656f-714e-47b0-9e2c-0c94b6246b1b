import React, { useState, useEffect } from "react";
import leftIcon from "../assets/images/leftMenuIcon.svg";
import Fade from "react-reveal/Fade";
import { Tooltip } from "@mui/material";
import Draw from "@arcgis/core/views/draw/Draw";
import compareLayers from "../assets/images/tools/tool1.svg";
import help from "../assets/images/tools/help.svg";
import inquiry from "../assets/images/tools/tool2.svg";
import identify_img from "../assets/images/mapTools/identify.svg";
import basemap_img from "../assets/images/mapTools/basemap.svg";
import googleMaps from "../assets/images/tools/tool3.svg";
import print_img from "../assets/images/sidemenu/print.svg";
import smallMap from "../assets/images/tools/tool4.svg";
import small_map_img from "../assets/images/mapTools/small_map.svg";
import my_location from "../assets/images/mapTools/my_location.svg";
import general_site_map from "../assets/images/mapTools/general_site_map.svg";
import full_map_img from "../assets/images/mapTools/full_map.svg";
import ServicesSearch from "./Services/ServicesSearch";
import AllTools from "./tools/AllTools";
import Swipe from "@arcgis/core/widgets/Swipe";
import Extent from "@arcgis/core/geometry/Extent";
import Graphic from "@arcgis/core/Graphic";
import Zoom from "@arcgis/core/widgets/Zoom";
import {
  addPictureSymbol,
  clearGraphicLayer,
  highlightFeature,
  project,
  showLoading,
} from "../helper/common_func";
import { saveAs } from "file-saver";
import locationIcon from "../assets/images/near_me-dark.svg";
import zoomin_img from "../assets/images/mapTools/zoomin.svg";
import zoomout_img from "../assets/images/mapTools/zoomout.svg";
import next_img from "../assets/images/mapTools/next.svg";
import previous_img from "../assets/images/mapTools/previous.svg";
import move_img from "../assets/images/mapTools/move.svg";
import clear_img from "../assets/images/mapTools/clear.svg";
import compare_layers_img from "../assets/images/mapTools/compare_layers.svg";
import full_screen_img from "../assets/images/mapTools/full_screen.svg";
import layers_img from "../assets/images/mapTools/layers.svg";

import PrintTemplate from "@arcgis/core/rest/support/PrintTemplate";
import PrintParameters from "@arcgis/core/tasks/support/PrintParameters";
import { CustomTileLayer } from "../helper/common_func";
import { useTranslation } from "react-i18next";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import Point from "@arcgis/core/geometry/Point";
import { useNavigate } from "react-router-dom";
import eventBus from "../helper/EventBus";
import PrintTask from "@arcgis/core/tasks/PrintTask";
import axios from "axios";

export default function MapToolsAndServicesIcons(props) {
  const { t } = useTranslation("map");
  const [openServSearch, setServSearch] = useState(false);
  const [activeService, setActiveService] = useState(0);
  const [activeServiceItem, setActiveServiceItem] = useState(null);
  const [openToolData, setToolData] = useState(false);
  const [activeTool, setActiveTool] = useState(0);
  const navigate = useNavigate();
  const openServiceSearch = (e) => {
    setServSearch(true);
    setActiveService(e.id);
    setActiveServiceItem(e);
    setToolData(false);
    setActiveTool("");
  };

  const openToolsData = (e) => {
    if (e.target.name == "myLocation") {
      disableActiveTool();
      setActiveTool(e.target.name);

      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition((position) => {
          var loc = new Point({
            longitude: position.coords.longitude,
            latitude: position.coords.latitude,
          });
          project([loc], 102100, (res) => {
            addPictureSymbol(
              res[0],
              locationIcon,
              "locationGraphicLayer",
              props.map,
              36,
              36
            );
            highlightFeature(res[0], props.map, {
              layerName: "ZoomGraphicLayer",
              isZoom: true,
              isZoomOnly: true,
              zoomDuration: 1000,
            });
          });
        });
      }
    } else {
      if (e.target.name == "inquiry") {
      }

      window.DisableActiveTool();
      setServSearch(false);
      setActiveService(0);
      setToolData(true);
      e !== undefined && e.target !== undefined
        ? setActiveTool(e.target.name)
        : setActiveTool("");

      // if (e.target.name == "compareLayers") {
      //   var swipe = props.map.view.ui._components.find(
      //     (x) =>
      //       x.widget._container &&
      //       x.widget._container.className.indexOf("swipe") > -1
      //   );

      //   if (swipe) {
      //     swipe.widget.destroy();
      //     props.map.view.ui.remove(swipe);
      //     setActiveTool("");
      //   } else {
      //     var swipeLayer = props.map.findLayerById("baseMap");
      //     let swipe = new Swipe({
      //       view: props.map.view,
      //       leadingLayers: [swipeLayer],
      //       direction: "horizontal", // swipe widget will move from top to bottom of view
      //       position: 50, // position set to middle of the view (50%)
      //     });
      //     props.map.view.ui.add(swipe);
      //   }
      // } else if (e.target.name == "traffic") {
      //   if (!props.map.findLayerById("trafficLayerId")) {
      //     let layer = new CustomTileLayer({
      //       urlTemplate: window.trafficUrl,
      //       id: "trafficLayerId",
      //     });

      //     props.map.layers.add(layer);
      //   } else {
      //     props.map.remove(props.map.findLayerById("trafficLayerId"));
      //   }
      // }
    }
  };

  useEffect(() => {
    eventBus.on("openIdentify", ({ message }) => {
      if (message.show) {
        openIdentifyAutomatic();
      }
    });
  }, []);

  const openIdentifyAutomatic = () => {
    window.DisableActiveTool();
    setServSearch(false);
    setActiveService(0);
    setToolData(true);
    setActiveTool("inquiry");
  };

  const [openToolservice, setToolService] = useState(true);

  function zoomOut(evt) {
    var vertices = evt.vertices;
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
    props.map.view.graphics.removeAll();
    var action = window.__draw.create("rectangle");
    //view.focus();
    action.on("vertex-add", drawRect);
    action.on("draw-complete", zoomOut);
    action.on("cursor-update", drawRect);
    if (evt.vertices.length === 1) {
      props.map.view.goTo({ scale: props.map.view.scale * 2 });
      return;
    }
    var sx = vertices[0][0],
      sy = vertices[0][1];
    var ex = vertices[1][0],
      ey = vertices[1][1];
    var rect = {
      x: Math.min(sx, ex),
      y: Math.max(sy, ey),
      width: Math.abs(sx - ex),
      height: Math.abs(sy - ey),
      spatialReference: props.map.view.spatialReference,
    };
    if (rect.width !== 0 || rect.height !== 0) {
      var scrPnt1 = props.map.view.toScreen(rect);
      var scrPnt2 = props.map.view.toScreen({
        x: rect.x + rect.width,
        y: rect.y,
        spatialReference: rect.spatialReference,
      });
      var mWidth = props.map.view.extent.width;
      var delta =
        ((mWidth * props.map.view.width) / Math.abs(scrPnt2.x - scrPnt1.x) -
          mWidth) /
        0.4;
      var vExtent = props.map.view.extent;
      props.map.view.goTo(
        new Extent({
          xmin: vExtent.xmin - delta,
          ymin: vExtent.ymin - delta,
          xmax: vExtent.xmax + delta,
          ymax: vExtent.ymax + delta,
          spatialReference: vExtent.spatialReference,
        })
      );
    }
  }

  const removeAllGraphicsOnMap = () => {
    disableActiveTool();
    props.map.view.graphics.removeAll();
    props.clearGeneralSearchData();
    //for reset layersMenu
    if (props.map.layers.find((x) => x.id.indexOf("fl_") > -1)) {
      props.map
        .findLayerById("baseMap")
        .allSublayers.items.find(
          (x) => x.title == "PROTECTED_AREA_BOUNDARY"
        ).visible = true;

      props.map.findLayerById("baseMap").visible = true;
    }
    let hideLayersMenu = props.map.layers.items.filter(
      (x) => x.id.indexOf("fl_") > -1 || x.id.indexOf("compareLayerList_") > -1
    );
    hideLayersMenu.forEach((layer) => {
      layer.destroy();
    });
    //////////////////////////////////

    props.map.layers.items.forEach((layer) => {
      clearGraphicLayer(layer.id, props.map);
    });

    navigate("/");

    setTimeout(() => {
      sessionStorage.clear();
    }, 500);

    window.__legend_checked_list = {};
    window.__legend_checked_toggle_list = {};
  };

  const openToolsMenu = () => {
    if (openToolservice) {
      setToolService(false);
      setToolData(false);
      setActiveTool("");
      setActiveService(0);
      setServSearch(false);
    } else {
      setToolService(true);
      setToolData(false);
      setActiveTool("");
      setActiveService(0);
      setServSearch(false);
    }
  };

  const closeToolsData = (e) => {
    setToolData(false);
    setActiveTool("");
  };

  const closeServiceSearch = () => {
    setServSearch(false);
    setActiveService(0);
  };

  const enableViewPanning = () => {
    if (window.__evtViewDragHandler) {
      window.__evtViewDragHandler.remove();
      window.__evtViewDragHandler = null;
    }
    if (window.__evtViewKeyDownHandler) {
      window.__evtViewKeyDownHandler.remove();
      window.__evtViewKeyDownHandler = null;
    }
  };

  const disableViewPanning = () => {
    if (window.__evtViewDragHandler) {
      window.__evtViewDragHandler.remove();
      window.__evtViewDragHandler = null;
    }
    if (window.__evtViewKeyDownHandler) {
      window.__evtViewKeyDownHandler.remove();
      window.__evtViewKeyDownHandler = null;
    }
    window.__evtViewDragHandler = props.map.view.on("drag", (event) => {
      // prevents panning with the mouse drag event
      if (activeTool != "dis") event.stopPropagation();
    });

    window.__evtViewKeyDownHandler = props.map.view.on("key-down", (event) => {
      // prevents panning with the arrow keys
      var keyPressed = event.key;
      if (keyPressed.slice(0, 5) === "Arrow") {
        if (activeTool != "dis") event.stopPropagation();
      }
    });
  };

  const displayZoomOutCursor = () => {
    props.map.view.container.style.cursor = "zoom-out";
  };
  const displayZoomInCursor = () => {
    props.map.view.container.style.cursor = "zoom-in";
  };
  // const displayCrosshairCursor = () => {
  //   props.map.view.container.style.cursor = "crosshair";
  // };
  // const displayPointerCursor = () => {
  //   props.map.view &&
  //     props.map.view.container &&
  //     props.map.view.container.style &&
  //     "pointer" !== props.map.view.container.style.cursor &&
  //     (props.map.view.container.style.cursor = "pointer");
  // };
  const displayDefaultCursor = () => {
    props.map.view &&
      props.map.view.container &&
      props.map.view.container.style &&
      "default" !== props.map.view.container.style.cursor &&
      (props.map.view.container.style.cursor = "default");
  };

  const removeCurrentSelTool = () => {
    props.map.view.popup.close();
  };

  const getExtentfromVertices = (vertices) => {
    var sx = vertices[0][0],
      sy = vertices[0][1];
    var ex = vertices[1][0],
      ey = vertices[1][1];
    var rect = {
      x: Math.min(sx, ex),
      y: Math.max(sy, ey),
      width: Math.abs(sx - ex),
      height: Math.abs(sy - ey),
      spatialReference: props.map.view.spatialReference,
    };
    if (rect.width > -1 || rect.height > -1) {
      return new Extent({
        xmin: parseFloat(rect.x),
        ymin: parseFloat(rect.y) - parseFloat(rect.height),
        xmax: parseFloat(rect.x) + parseFloat(rect.width),
        ymax: parseFloat(rect.y),
        spatialReference: rect.spatialReference,
      });
    } else {
      return null;
    }
  };

  const drawRect = (event) => {
    var vertices = event.vertices;
    //remove existing graphic
    props.map.view.graphics.removeAll();
    if (vertices.length < 2) {
      return;
    }

    // create a new extent
    var extent = getExtentfromVertices(vertices);

    var graphic = new Graphic({
      geometry: extent,
      symbol: {
        type: "simple-fill", // autocasts as SimpleFillSymbol
        color: [157, 66, 35, 0.1],
        style: "solid",
        outline: {
          // autocasts as SimpleLineSymbol
          color: [180, 83, 51],
          width: 1.5,
        },
      },
    });

    props.map.view.graphics.add(graphic);
  };

  function zoomOut(evt) {
    var vertices = evt.vertices;
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
    props.map.view.graphics.removeAll();
    var action = window.__draw.create("rectangle");
    //view.focus();
    action.on("vertex-add", drawRect);
    action.on("draw-complete", zoomOut);
    action.on("cursor-update", drawRect);
    if (evt.vertices.length === 1) {
      props.map.view.goTo({ scale: props.map.view.scale * 2 });
      return;
    }
    var sx = vertices[0][0],
      sy = vertices[0][1];
    var ex = vertices[1][0],
      ey = vertices[1][1];
    var rect = {
      x: Math.min(sx, ex),
      y: Math.max(sy, ey),
      width: Math.abs(sx - ex),
      height: Math.abs(sy - ey),
      spatialReference: props.map.view.spatialReference,
    };
    if (rect.width !== 0 || rect.height !== 0) {
      var scrPnt1 = props.map.view.toScreen(rect);
      var scrPnt2 = props.map.view.toScreen({
        x: rect.x + rect.width,
        y: rect.y,
        spatialReference: rect.spatialReference,
      });
      var mWidth = props.map.view.extent.width;
      var delta =
        ((mWidth * props.map.view.width) / Math.abs(scrPnt2.x - scrPnt1.x) -
          mWidth) /
        20;
      var vExtent = props.map.view.extent;
      props.map.view.goTo(
        new Extent({
          xmin: vExtent.xmin - delta,
          ymin: vExtent.ymin - delta,
          xmax: vExtent.xmax + delta,
          ymax: vExtent.ymax + delta,
          spatialReference: vExtent.spatialReference,
        })
      );
    }
  }

  const zoomIn = (evt) => {
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
    props.map.view.graphics.removeAll();
    var action = window.__draw.create("rectangle");
    //props.map.view.focus();
    action.on("vertex-add", drawRect);
    action.on("draw-complete", zoomIn);
    action.on("cursor-update", drawRect);
    if (evt.vertices.length === 1) {
      props.map.view.goTo({ scale: props.map.view.scale * 0.5 });
      return;
    }
    var extent = getExtentfromVertices(evt.vertices);

    if (extent.width !== 0 || extent.height !== 0) {
      props.map.view.goTo(extent);
    }
  };

  const activeZoomIn = (e) => {
    if (true) {
      // setActiveTool("zoomIn");
      // removeCurrentSelTool();
      // disableViewPanning();
      // props.map.view.graphics.removeAll();
      // if (!window.__draw) {
      //   window.__draw = new Draw({
      //     view: props.map.view,
      //   });
      // }
      var action = window.__draw.create("rectangle");
      displayZoomInCursor();
      //props.map.view.focus();
      action.on("vertex-add", drawRect);
      action.on("draw-complete", zoomIn);
      action.on("cursor-update", drawRect);
    } else {
      var zoom = new Zoom({
        view: props.map.view,
        visible: false,
      });

      zoom.zoomIn();
    }
  };

  const activeZoomOut = () => {
    if (true) {
      // setActiveTool("zoomOut");
      // removeCurrentSelTool();
      // disableViewPanning();
      // props.map.view.graphics.removeAll();
      // if (!window.__draw) {
      //   window.__draw = new Draw({
      //     view: props.map.view,
      //   });
      // }
      var action = window.__draw.create("rectangle");
      displayZoomOutCursor();
      //view.focus();
      action.on("vertex-add", drawRect);
      action.on("draw-complete", zoomOut);
      action.on("cursor-update", drawRect);
    } else {
      var zoom = new Zoom({
        view: props.map.view,
        visible: false,
      });

      zoom.zoomOut();
    }
  };

  const goToPreviousExtent = () => {
    disableActiveTool();
    // setActiveTool("prev");
    if (window.__extentHistory[window.__extentHistoryIndx].preExtent) {
      window.__prevExtent = true;
      if (window.__extentHistoryIndx > 0) {
        props.map.view.goTo(
          window.__extentHistory[window.__extentHistoryIndx].preExtent
        );
        window.__extentHistoryIndx--;
      }
    }
  };

  const goToNextExtent = () => {
    disableActiveTool();
    // setActiveTool("next");
    window.__nextExtent = true;
    if (window.__extentHistory.length > window.__extentHistoryIndx + 1) {
      window.__extentHistoryIndx++;
      props.map.view.goTo(
        window.__extentHistory[window.__extentHistoryIndx].currentExtent
      );
    }
  };

  const goToFullExtent = () => {
    disableActiveTool();
    setActiveTool("fullExt");
    props.map.view.goTo(window.__fullExtent);
    if (props.setIndicatorFullExtent) {
      props.setIndicatorFullExtent();
    }
  };

  const disableActiveTool = () => {
    // setActiveTool("dis");
    removeCurrentSelTool();

    enableViewPanning();
    displayDefaultCursor();
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
  };

  const printMap = async() => {
    removeCurrentSelTool();

    let user = JSON.parse(localStorage.getItem("user"));
    let userName = user?.name;

    const printTask = new PrintTask({
      url: window.customPrintUrl + "?token=" + window.esriToken,
    });

    const customTemplate = new PrintTemplate({
      format: "pdf",
      layout: "A4_CustomLayout1",
      layoutOptions: {
        titleText: userName || " ",
      },
    });

    const printParams = new PrintParameters({
      view: props.map.view,
      template: customTemplate,
    });

    showLoading(true);
    
    let excludeLayers = await axios.get(`${window.ApiUrl}api/ExcludeLayers/GetAll`);

    showLoading(true);
    
    window.__neglectLegendLayers = excludeLayers.data?.map((x)=>x.esri_layer_id) || [];

    printTask.execute(printParams).then(
      (e) => {
        exportMap(e);
      },
      (error) => {
        showLoading(false);
      }
    );

    setTimeout(() => {
      props.map.findLayerById("printGraphicLayer").removeAll();
    }, 800);
  };

  const exportMap = (res) => {
    showLoading(false);
    let toPrint;
    if (res.url.indexOf(".pdf") > -1) {
      toPrint = window.open(res.url);
    } else {
      toPrint = window.open("", "", "width=2500,height=2000");
      if (toPrint) {
        const img = new Image();
        img.src = res.url;
        img.onload = () => {
          toPrint.print();
          toPrint.close();
        };
        toPrint.document.body.appendChild(img);
        toPrint.document.close();
        toPrint.focus();
      }
    }
  };

  const saveMap = async (res) => {
    const blob = await fetch(res.url).then((r) => r.blob());
    showLoading(false);
    saveAs(blob, "map");
  };

  window.DisableActiveTool = () => {
    disableActiveTool();
  };

  const extentChangeHandler = (evt) => {
    if (window.__prevExtent || window.__nextExtent) {
      window.__currentExtent = evt;
    } else {
      window.__preExtent = window.__currentExtent;
      window.__currentExtent = evt;
      window.__extentHistory = window.__extentHistory || [];
      window.__extentHistory.push({
        preExtent: window.__preExtent,
        currentExtent: window.__currentExtent,
      });
      window.__extentHistoryIndx = window.__extentHistory.length - 1;
    }
    window.__prevExtent = window.__nextExtent = false;
    //console.log('extent--------',_extentHistory);
    //extentHistoryChange();
  };

  /***
   * left tools
   */
  const [tools] = useState([
    {
      id: 1,
      icon: identify_img,
      name: "inquiry",
      tooltip: "mapToolsServices.inquiry",
      onClick: openToolsData,
    },
    {
      id: 2,
      icon: basemap_img,
      name: "Basemap",
      tooltip: "mapToolsServices.Basemap",
      onClick: openToolsData,
    },
    {
      id: 3,
      icon: print_img,
      name: "print",
      tooltip: "mapToolsServices.print",
      onClick: openToolsData,
    },
    {
      id: 3,
      icon: googleMaps,
      name: "googleMaps",
      tooltip: "mapToolsServices.googleMaps",
      className: "googleMapToolClass",
      onClick: openToolsData,
    },
    {
      id: 4,
      icon: small_map_img,
      name: "smallMap",
      tooltip: "mapToolsServices.smallMap",
      onClick: openToolsData,
    },
    {
      id: 5,
      icon: my_location,
      name: "myLocation",
      tooltip: "mapToolsServices.myLocation",
      onClick: openToolsData,
    },
    {
      id: 6,
      icon: general_site_map,
      name: "generalSiteMap",
      tooltip: "mapToolsServices.generalSiteMap",
      onClick: openToolsData,
    },
    {
      id: 7,
      icon: full_map_img,
      name: "fullMap",
      tooltip: "mapTools.fullMap",
      onClick: goToFullExtent,
    },

    {
      id: 8,
      tooltip: "mapTools.zoomIn",
      name: "zoomIn",
      icon: zoomin_img,
      onClick: activeZoomIn,
    },
    {
      id: 9,
      tooltip: "mapTools.zoomOut",
      name: "zoomOut",
      icon: zoomout_img,
      onClick: activeZoomOut,
    },
    {
      id: 10,
      tooltip: "mapTools.next",
      name: "next",
      icon: next_img,
      onClick: goToNextExtent,
    },
    {
      id: 11,
      tooltip: "mapTools.prev",
      name: "prev",
      icon: previous_img,
      onClick: goToPreviousExtent,
    },
    {
      id: 12,
      tooltip: "mapTools.move",
      name: "move",
      icon: move_img,
      onClick: disableActiveTool,
    },
    {
      id: 13,
      tooltip: "mapTools.removeAll",
      name: "removeAll",
      icon: clear_img,
      onClick: removeAllGraphicsOnMap,
    },

    // {
    //   id: 5,
    //   icon: layersMenu,
    //   name: "layersMenu",
    //   tooltip: "mapToolsServices.layersMenu",
    // },
    // {
    //   id: 8,
    //   title: "mapToolsServices.compareLayers",
    //   name: "compareLayers",
    //   image: compare_layers_img,
    //   onClick: openToolsData,
    // },

    // {
    //   id: 5,
    //   icon: layersMenu,
    //   name: "layersMenu",
    //   tooltip: "mapToolsServices.layersMenu",
    // },

    // {
    //   id: 7,
    //   icon: traffic,
    //   name: "traffic",
    //   tooltip: "mapToolsServices.traffic",
    // },
  ]);

  const topTools = [
    {
      id: 1,
      tooltip: "mapToolsServices.compareLayers",
      name: "compareLayers",
      onClick: openToolsData,
      icon: compare_layers_img,
    },
    {
      id: 2,
      tooltip: "mapTools.fullScreen",
      name: "fullScreen",
      onClick: () => {
        if (!document.fullscreenElement) {
          props.handle.enter();
        } else {
          document.exitFullscreen();
        }
      },
      icon: full_screen_img,
    },
    {
      id: 3,
      tooltip: "mapToolsServices.mapKey",
      name: "layersMenu",
      onClick: openToolsData,
      icon: layers_img,
    },
  ];

  // const [moreservices] = useState([
  //   {
  //     id: 4,
  //     icon: setting,
  //     tooltip: "mapToolsServices.maintenance",
  //     where: " SRVC_SUBTYPE = '10015' ",
  //   },
  //   {
  //     id: 5,
  //     icon: hospital,
  //     tooltip: "mapToolsServices.hospitals",
  //     where: " SRVC_TYPE = '700' ",
  //   },
  //   {
  //     id: 6,
  //     icon: foodcart,
  //     tooltip: "mapToolsServices.catering",
  //     where: " SRVC_SUBTYPE = '10005' ",
  //   },
  // ]);
  // const [openMoreSer, setOpenMoreSer] = useState(false);
  // const openMoreServices = () => {
  //   setOpenMoreSer(true);
  // };
  // const closeMoreServices = () => {
  //   setOpenMoreSer(false);
  // };
  const openHelp = (e) => {
    e.preventDefault();
    setServSearch(false);
    setActiveService(0);
    setToolData(true);
    e !== undefined && e.target !== undefined
      ? setActiveTool(e.target.name)
      : setActiveTool("");
    localStorage.removeItem("showHelp");
    localStorage.removeItem("showMetaHelp");
    localStorage.removeItem("showOpenSideHelp");
    localStorage.removeItem("showCardsResultHelp");
    localStorage.removeItem("showCardDetailsHelp");
    setTimeout(() => {
      props.setHelpShow(false);
      props.setHelpShow(true);
    }, 1);
    props.setHelpShow(false);
  };

  useEffect(() => {
    watchUtils.whenTrue(props.map.view, "ready", () => {
      window.__fullExtent = props.map.view.extent.clone();
      window.__draw = new Draw({
        view: props.map.view,
      });
      watchUtils.whenOnce(props.map.view, "extent", () => {
        watchUtils.when(props.map.view, "stationary", (evt) => {
          if (evt) {
            extentChangeHandler(props.map.view.extent);
          }
        });
      });
    });
  }, []);

  return (
    <div>
      <Tooltip
        title={
          openToolservice ? t("mapTools.CloseTools") : t("mapTools.OpenTools")
        }
        placement="top"
      >
        <div
          className="leftIconMenu openCloseToolServHelp"
          onClick={openToolsMenu}
        >
          <img src={leftIcon} alt="" />
        </div>
      </Tooltip>

      <Fade left delay={500}>
        <div
          className={
            openToolservice
              ? "openedservicesMenu servicesHelp"
              : "closedservicesMenu servicesHelp"
          }
        >
          <ul style={{ display: "flex", alignItems: "center" }}>
            {/* {openMoreSer ? (
              <Tooltip title={t("mapToolsServices.lessServ")} placement="top">
                <li onClick={closeMoreServices} className="moreLessIcon">
                  <img
                    src={less}
                    style={{ transform: "rotate(180deg" }}
                    alt="lessServices"
                  />
                </li>
              </Tooltip>
            ) : (
              <Tooltip title={t("mapToolsServices.moreServ")} placement="top">
                <li onClick={openMoreServices} className="moreLessIcon">
                  <img src={more} alt="moreServices" />
                </li>
              </Tooltip>
            )}
            {openMoreSer
              ? moreservices.map((s, index) => (
                  <Tooltip title={t(s.tooltip)} placement="top" key={index}>
                    <li
                      id={s.id}
                      onClick={() => openServiceSearch(s)}
                      className={
                        Number(activeService) === Number(s.id)
                          ? "activeService"
                          : "serviceLi"
                      }
                    >
                      <img src={s.icon} alt="servicesIcon" id={s.id} />
                    </li>
                  </Tooltip>
                ))
              : null} */}{" "}
            {topTools.map(({ id, name, icon, onClick, tooltip }) => {
              return (
                <>
                  <Tooltip key={id} title={t(tooltip)} placement="top">
                    <li
                      name={name}
                      id={name}
                      // onClick={openToolsData}
                      onClick={(e) => {
                        onClick(e);
                      }}
                      className="serviceLi"
                    >
                      <img
                        src={icon}
                        name={name}
                        className="openedservicesMenuImg"
                        alt={name}
                      />
                    </li>
                  </Tooltip>

                  {openToolData && String(activeTool) === String(name) ? (
                    <AllTools
                      languageState={props.languageState}
                      mainData={props.mainData}
                      setPopupInfo={props.setPopupInfo}
                      popupInfo={props.popupInfo}
                      activeTool={name}
                      map={props.map}
                      closeToolsData={closeToolsData}
                      openToolsData={openToolsData}
                      openToolData={openToolData}
                    />
                  ) : null}
                </>
              );
            })}
            {/* <Tooltip title={t("mapTools.fullScreen")} placement="top">
              <li
                className="fullscreenServHelp serviceLi"
                onClick={props.handle.enter}
              >
                <img
                  src={fullScreenIcon}
                  className="openedservicesMenuImg"
                  alt="fullScreenIcon"
                />
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapToolsServices.home")} placement="top">
              <li className=" serviceLi">
                <a
                  href={`${window.hostURL}/home/<USER>
                  target="_blank"
                  rel="noreferrer"
                >
                  <img
                    src={homeIcon}
                    alt="homeIcon"
                    className="openedservicesMenuImg"
                  />
                </a>
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapToolsServices.help")} placement="top">
              <li className="serviceLi " onClick={openHelp} name="openhelp">
                <img
                  src={help}
                  alt="servicesIcon"
                  name="openhelp"
                  className="openedservicesMenuImg"
                />
              </li>
            </Tooltip> */}
          </ul>
        </div>
      </Fade>

      <Fade top delay={500}>
        <div
          className={
            openToolservice
              ? "openedToolsMenu toolsHelp"
              : "closedToolsMenu toolsHelp"
          }
        >
          <ul
            style={{
              overflow: "auto",
              height: "calc(100vh - 66px)",
              scrollbarWidth: "none",
            }}
          >
            {tools.map((tool, index) => (
              <>
                {tool.name !== "openHelp" ? (
                  <Tooltip
                    title={t(tool.tooltip)}
                    placement="right"
                    key={index + "tright"}
                  >
                    <li
                      // onClick={openToolsData}
                      onClick={(e) => {
                        tool.onClick(e);
                      }}
                      name={tool.name}
                      id={tool.name}
                      className={
                        "openedToolsMenuLi " +
                        (String(activeTool) === String(tool.name)
                          ? "activeService "
                          : "")
                      }
                    >
                      {tool.icon ? (
                        <img
                          className="openedservicesMenuImg"
                          src={tool.icon}
                          alt="toolsIcon"
                          id={tool.id}
                          name={tool.name}
                        // style={{
                        //   position:
                        //     tool.className === "googleMapToolClass"
                        //       ? "absolute"
                        //       : "",
                        //   top:
                        //     tool.className === "googleMapToolClass"
                        //       ? "10px"
                        //       : "",
                        //   left:
                        //     tool.className === "googleMapToolClass"
                        //       ? "10px"
                        //       : "",
                        // }}
                        />
                      ) : null}
                    </li>
                  </Tooltip>
                ) : (
                  <Tooltip
                    key={index + "t"}
                    title={t(tool.tooltip)}
                    placement="right"
                  >
                    <li
                      onClick={openHelp}
                      // id={tool.id}
                      name={tool.name}
                      id={tool.name}
                      className={
                        "openedToolsMenuLi " +
                        (String(activeTool) === String(tool.name)
                          ? "activeService "
                          : "")
                      }
                    >
                      <img
                        className="openedservicesMenuImg"
                        src={tool.icon}
                        alt="toolsIcon"
                        id={tool.id}
                        name={tool.name}
                      />
                    </li>
                  </Tooltip>
                )}

                {openToolData && String(activeTool) === String(tool.name) ? (
                  <AllTools
                    languageState={props.languageState}
                    mainData={props.mainData}
                    setPopupInfo={props.setPopupInfo}
                    popupInfo={props.popupInfo}
                    activeTool={tool.name}
                    map={props.map}
                    closeToolsData={closeToolsData}
                    openToolsData={openToolsData}
                    openToolData={openToolData}
                  />
                ) : null}
              </>
            ))}
          </ul>
        </div>
      </Fade>
      {openServSearch ? (
        <ServicesSearch
          mainData={props.mainData}
          outerResultMenuShown={props.outerResultMenuShown}
          outerOpenResultMenu={props.outerOpenResultMenu}
          handleDrawerOpen={props.handleDrawerOpen}
          setFilteredResult={props.setFilteredResult}
          activeService={activeServiceItem}
          map={props.map}
          closeServiceSearch={closeServiceSearch}
        />
      ) : null}
    </div>
  );
}
