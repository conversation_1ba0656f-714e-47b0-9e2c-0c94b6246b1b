import { <PERSON>aMap, FaTrashAlt } from "react-icons/fa";
import { MdKeyboardArrowDown } from "react-icons/md";
import { useTranslation } from "react-i18next";
import {
  Button,
  Checkbox,
  DatePicker,
  Input,
  message,
  Modal,
  Tooltip,
} from "antd";
import {
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  OutlinedInput,
  Chip,
  Box,
} from "@mui/material";
import { RiArrowDropDownFill } from "react-icons/ri";
import dayjs from "dayjs";

import tools_logo from "../../assets/images/interactive-map/tools_logo.svg";
import tool_1 from "../../assets/images/interactive-map/tool_1.svg";
import tool_2 from "../../assets/images/interactive-map/tool_2.svg";
import tool_3 from "../../assets/images/interactive-map/tool_3.svg";
import tool_4 from "../../assets/images/interactive-map/tool_4.svg";
import tool_5 from "../../assets/images/interactive-map/tool_5.svg";
import tool_7 from "../../assets/images/interactive-map/tool_7.svg";
import tool_8 from "../../assets/images/interactive-map/tool_8.svg";
import tool_9 from "../../assets/images/interactive-map/tool_9.svg";
import tool_10 from "../../assets/images/interactive-map/tool_10.svg";
import tool_11 from "../../assets/images/interactive-map/tool_11.svg";
import tool_12 from "../../assets/images/interactive-map/tool_12.svg";
import tool_13 from "../../assets/images/interactive-map/topbar_move.svg";
import tool_14 from "../../assets/images/interactive-map/topbar_undo.svg";
import tool_15 from "../../assets/images/interactive-map/topbar_redo.svg";
import tool_16 from "../../assets/images/sidemenu/delete.svg";
import draw_text from "../../assets/images/interactive-map/drawText.svg";
import add_file_icon from "../../assets/images/interactive-map/add_file.svg";

import map_image from "../../assets/images/study-actor/map.png";

import { useEffect, useRef, useState } from "react";
import { SketchPicker } from "react-color";
import Sketch from "@arcgis/core/widgets/Sketch";
import axios from "axios";
import Color from "@arcgis/core/Color";
import { useLocation, useNavigate } from "react-router-dom";
import Graphic from "@arcgis/core/Graphic";
import { drawText, zoomToFeatures } from "../../helper/common_func";
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine";
import { exists } from "i18next";
import { icon } from "@fortawesome/fontawesome-svg-core";

export default function AddStudyActor(props) {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation("sidemenu");
  const userObject = JSON.parse(localStorage.getItem("user"));

  const defaultFormData = {
    tripName: "",
    fromDate: null,
    toDate: null,
    manager: null,
    managerName: "",
    team: [],
    description: "",
    attachments: [],
    maps: [],
  };

  const tools = [
    {
      name: "straight-line",
      src: tool_1,
      label: t("straight_line", { ns: "common" }),
    },
    { name: "free-hand", src: tool_2, label: t("free_hand", { ns: "common" }) },
    {
      name: "line-with-arrows",
      src: tool_3,
      label: t("line_with_arrows", { ns: "common" }),
    },
    { name: "rectangle", src: tool_4, label: t("rectangle", { ns: "common" }) },
    { name: "circle", src: tool_5, label: t("circle", { ns: "common" }) },
    {
      name: "line-right-arrow",
      src: tool_8,
      label: t("line_right_arrow", { ns: "common" }),
    },
    {
      name: "line-left-arrow",
      src: tool_9,
      label: t("line_left_arrow", { ns: "common" }),
    },
    {
      name: "location_marker",
      src: tool_10,
      label: t("location", { ns: "common" }),
    },
    {
      name: "pan",
      src: tool_13,
      label: t("pan", { ns: "common" }),
    },
    {
      name: "undo",
      src: tool_14,
      label: t("undo", { ns: "common" }),
    },
    {
      name: "redo",
      src: tool_15,
      label: t("redo", { ns: "common" }),
    },
    {
      name: "delete",
      src: tool_16,
      label: t("delete", { ns: "common" }),
    },
    {
      name: "pointText",
      src: draw_text,
      label: t("point_text", { ns: "common" }),
    },
  ];

  const graphicLayersToClear = [
    "MonitoringCasesGraphicLayer",
    "PathsGraphicLayer",
    "FieldMissionGraphicLayer",
    "AddStudyGraphicsLayer",
  ];
  const [showTools, setShowTools] = useState(true);
  const fileInputRef = useRef(null);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [checkedMaps, setCheckedMaps] = useState([false, false]);
  const [mapsList, setMapsList] = useState([]);
  const [usersList, setUsersList] = useState([]);

  const initializeFormData = () => {
    if (location.state && location.state.studyData) {
      const studyData = location.state.studyData;

      const manager = studyData.assigned_to
        ? studyData.assigned_to.find((user) => user.is_manager)
        : null;

      const teamIds = studyData.assigned_to
        ? studyData.assigned_to
            .filter((user) => !user.is_manager)
            .map((user) => user.user_id)
        : [];

      const mapIds = studyData.maps
        ? studyData.maps.map((map) => map.map_id)
        : [];

      const attachments = studyData.attachments
        ? studyData.attachments.map((att) => ({
            PrevFileName:
              att.file_name ||
              att.file_url.substring(att.file_url.lastIndexOf("/") + 1),
            data: att.file_url,
          }))
        : [];

      return {
        tripName: studyData.name || "",
        fromDate: studyData.start_date || null,
        toDate: studyData.end_date || null,
        manager: manager ? manager.user_id : null,
        managerName: manager ? manager.user_fullname : "",
        team: teamIds,
        description: studyData.description || "",
        attachments: attachments,
        maps: mapIds,
      };
    }
    return defaultFormData;
  };

  const [formData, setFormData] = useState(defaultFormData);

  const handleChange = (key, value) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
  };

  const getMaps = async () => {
    await axios.get(`${window.ApiUrl}OfflineMap/GetAll`).then((results) => {
      let offlineMapsList = results.data.filter((item) => !item.is_online);
      setMapsList(offlineMapsList);
    });
  };

  const getUsersList = async () => {
    await axios
      .get(
        `${window.ApiUrl}user/GetAll?q=${userObject.department_id}&filter_key=department_id&pageSize=400&page=0`
      )
      .then((results) => {
        setUsersList(results.data.results);
      });
  };

  useEffect(() => {
    let fetchData = async () => {
      await getMaps();
      await getUsersList();
    };
    graphicLayersToClear.forEach((layerName) =>
      props.map.findLayerById(layerName).removeAll()
    );
    fetchData();

    return () => {
      props.map.findLayerById("AddStudyGraphicsLayer").removeAll();
    };
  }, []);

  useEffect(() => {
    if (usersList.length > 0 && mapsList.length > 0) {
      const initializedData = initializeFormData();
      setFormData(initializedData);

      if (location.state && location.state.studyData) {
        const studyData = location.state.studyData;
        const mapIds = studyData.maps
          ? studyData.maps.map((map) => map.map_id)
          : [];

        const newCheckedMaps = mapsList.map((map) => mapIds.includes(map.id));
        setCheckedMaps(newCheckedMaps);

        if (studyData.attachments && studyData.attachments.length > 0) {
          const files = studyData.attachments.map((att) => ({
            name:
              att.file_name ||
              att.file_url.substring(att.file_url.lastIndexOf("/") + 1),
          }));
          setSelectedFiles(files);
        }

        if (studyData.graphics) {
          try {
            const graphicsData = JSON.parse(studyData.graphics);

            let layer = props.map.findLayerById("AddStudyGraphicsLayer");
            graphicsData.forEach((graphicData) => {
              const graphic = Graphic.fromJSON(graphicData);
              layer.add(graphic);
            });
            zoomToFeatures(graphicsData, props.map);
          } catch (error) {
            console.error("Error parsing graphics:", error);
          }
        }
      }
    }
  }, [usersList, mapsList]);

  const handleFileChange = async (e) => {
    const newFiles = Array.from(e.target.files);
    const currentFilesCount = selectedFiles.length;
    const totalFilesCount = currentFilesCount + newFiles.length;

    if (totalFilesCount > 3) {
      message.warning(t("files-count-validation"));
      e.target.value = "";
      return;
    }

    const allowedTypes = [
      // Images
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/bmp",
      "image/webp",
      "image/svg+xml",
      "application/pdf",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];

    const allowedExtensions = [
      ".jpg",
      ".jpeg",
      ".png",
      ".gif",
      ".bmp",
      ".webp",
      ".svg",
      ".pdf",
      ".xls",
      ".xlsx",
      ".doc",
      ".docx",
    ];

    const maxSizeInBytes = 10 * 1024 * 1024;
    const invalidFiles = [];
    const oversizedFiles = [];

    const getFileType = (file) => {
      const fileExtension = "." + file.name.split(".").pop().toLowerCase();

      if (file.type.startsWith("image/")) {
        return "image";
      }
      if (file.type === "application/pdf") {
        return "pdf";
      }
      if (
        file.type === "application/vnd.ms-excel" ||
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ) {
        return "excel";
      }
      if (
        file.type === "application/msword" ||
        file.type ===
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ) {
        return "word";
      }

      const imageExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".bmp",
        ".webp",
        ".svg",
      ];
      const excelExtensions = [".xls", ".xlsx"];
      const wordExtensions = [".doc", ".docx"];

      if (imageExtensions.includes(fileExtension)) {
        return "image";
      }
      if (fileExtension === ".pdf") {
        return "pdf";
      }
      if (excelExtensions.includes(fileExtension)) {
        return "excel";
      }
      if (wordExtensions.includes(fileExtension)) {
        return "word";
      }

      return "unknown";
    };

    for (let file of newFiles) {
      if (file.size > maxSizeInBytes) {
        oversizedFiles.push(file.name);
        continue;
      }

      const fileExtension = "." + file.name.split(".").pop().toLowerCase();
      const isValidType =
        allowedTypes.includes(file.type) ||
        allowedExtensions.includes(fileExtension);

      if (!isValidType) {
        invalidFiles.push(file.name);
      }
    }

    if (oversizedFiles.length > 0) {
      message.warning(t("files-size-validation"));
      e.target.value = "";
      return;
    }

    if (invalidFiles.length > 0) {
      message.warning(t("files-format-validation"));
      e.target.value = "";
      return;
    }

    const uploadFormData = new FormData();
    for (let index = 0; index < e.target.files.length; index++) {
      uploadFormData.append(
        `file[${e.target.files[index]}]`,
        e.target.files[index]
      );
    }

    try {
      const results = await axios.post(
        window.ApiUrl + "uploadMultifiles",
        uploadFormData
      );

      const newAttachments = [...formData.attachments, ...results.data];
      handleChange("attachments", newAttachments);

      const newFilesData = Array.from(e.target.files).map((file) => ({
        name: file.name,
        type: getFileType(file),
      }));

      setSelectedFiles((prev) => [...prev, ...newFilesData]);

      message.success(`تم رفع ${newFiles.length} ملف بنجاح`);
    } catch (error) {
      console.error("Upload error:", error);
      message.error("حدث خطأ أثناء رفع الملفات");
    }

    e.target.value = "";
  };

  const handleFileUploadClick = () => {
    if (selectedFiles.length >= 3) {
      message.warning("تم الوصول للحد الأقصى من الملفات (3 ملفات)");
      return;
    }

    fileInputRef.current.click();
  };

  const handleFileClick = (fileName) => {
    let fileObject = formData.attachments.find((attach) =>
      attach.PrevFileName.includes(fileName)
    );

    if (fileObject) {
      window.open(`${window.filesURL}${fileObject.data}`, "_blank");
    }
  };

  const handleFileDeletion = (fileName) => {
    setSelectedFiles((prev) => prev.filter((file) => file.name !== fileName));

    const updatedAttachments = formData.attachments.filter(
      (attachment) => !attachment.PrevFileName.includes(fileName)
    );

    handleChange("attachments", updatedAttachments);
  };

  const parseDate = (dateStr) => {
    const [day, month, year] = dateStr.split("/").map(Number);
    return new Date(year, month - 1, day);
  };

  const handleCreateStudyButton = async () => {
    console.log("form data", formData);
    handleSelectTool({ name: "pan" });

    //   let hasInitialValue = false;

    if (formData.tripName.length < 3 || formData.tripName.length > 100) {
      message.warning(t("trip-name-validation"));
      return;
    }

    const fromDate = formData.fromDate ? parseDate(formData.fromDate) : null;
    const toDate = formData.toDate ? parseDate(formData.toDate) : null;

    if (!fromDate || !toDate) {
      message.warning(t("pick-date-validation"));
      return;
    }

    if (fromDate && toDate && fromDate > toDate) {
      message.warning(t("date-pick-validation"));
      return;
    }

    if (!formData.manager) {
      message.warning(t("team-leader-validation"));
      return;
    }

    if (formData.team.length == 0) {
      message.warning(t("team-members-validation"));
      return;
    }
    // for (const key in defaultFormData) {
    //   const initialValue = defaultFormData[key];
    //   const currentValue = formData[key];

    //   if (Array.isArray(initialValue)) {
    //     if (currentValue.length === 0) {
    //       hasInitialValue = true;
    //       break;
    //     }
    //   } else {
    //     if (currentValue === initialValue) {
    //       hasInitialValue = true;
    //       break;
    //     }
    //   }
    // }

    // if (hasInitialValue) {
    //   console.log(
    //     "Some field is still at its initial value, skipping API call."
    //   );
    //   message.warning(t("add-study-validation"));
    //   return;
    // }

    if (
      props.map.findLayerById("AddStudyGraphicsLayer").graphics.items.length ==
      0
    ) {
      message.warning(t("graphics-study-validation"));
      return;
    }

    if (formData.description.length < 3 || formData.description.length > 1000) {
      message.warning(t("description-validation"));
      return;
    }

    if (formData.attachments.length == 0) {
      message.warning(t("files-upload-validation"));
      return;
    }

    if (formData.maps.length == 0) {
      message.warning(t("maps-validation"));
      return;
    }

    let stringfiedGraphics = props.map
      .findLayerById("AddStudyGraphicsLayer")
      .graphics.filter((graphic) => {
        if (graphic.visible) {
          return graphic.toJSON();
        }
      });

    stringfiedGraphics = JSON.stringify(stringfiedGraphics);

    const formatDateForAPI = (dateString) => {
      if (!dateString) return null;

      if (dateString.includes("AM") || dateString.includes("PM")) {
        const datePart = dateString.split(" ")[0];
        const parsedDate = dayjs(datePart, ["D/M/YYYY", "DD/MM/YYYY"]);
        return parsedDate.isValid()
          ? parsedDate.format("DD/MM/YYYY")
          : dateString;
      }

      return dateString;
    };

    const isEdit = location.state && location.state.studyData;

    let payload;

    if (isEdit) {
      const originalData = location.state.studyData;

      const updatedAssignedTo = [];

      const originalManager = originalData.assigned_to.find(
        (user) => user.is_manager
      );
      if (formData.manager) {
        const managerUser = usersList.find(
          (user) => user.id === formData.manager
        );
        updatedAssignedTo.push({
          ...originalManager,
          user_id: formData.manager,
          user_fullname: managerUser ? managerUser.name : formData.managerName,
          is_manager: true,
        });
      }

      formData.team.forEach((teamMemberId) => {
        const teamUser = usersList.find((user) => user.id === teamMemberId);
        const originalTeamMember = originalData.assigned_to.find(
          (user) => user.user_id === teamMemberId
        );
        updatedAssignedTo.push({
          ...(originalTeamMember || originalManager),
          user_id: teamMemberId,
          user_fullname: teamUser ? teamUser.name : "",
          is_manager: false,
        });
      });

      // const existingAttachments = originalData.attachments || [];
      // const newAttachments = formData.attachments.filter(
      //   (att) =>
      //     !existingAttachments.some(
      //       (existing) => existing.file_url === att.data
      //     )
      // );

      // const updatedAttachments = [
      //   ...existingAttachments,
      //   ...newAttachments.map((item) => ({
      //     file_name: item.PrevFileName.substring(
      //       item.PrevFileName.lastIndexOf("/") + 1
      //     ),
      //     file_url: item.data,
      //     mission_id: originalData.id,
      //   })),
      // ];
      const updatedAttachments = [
        ...formData.attachments.map((item) => ({
          file_name: item.PrevFileName.substring(
            item.PrevFileName.lastIndexOf("/") + 1
          ),
          file_url: item.data,
          mission_id: originalData.id,
        })),
      ];

      const updatedMaps = formData.maps.map((mapId) => {
        const existingMap = originalData.maps.find(
          (map) => map.map_id === mapId
        );
        if (existingMap) {
          return existingMap;
        }
        const mapData = mapsList.find((map) => map.id === mapId);
        return {
          map_id: mapId,
          mission_id: originalData.id,
          offline_map: mapData || null,
        };
      });

      payload = {
        ...originalData,
        name: formData.tripName,
        name_en: formData.tripName,
        start_date: formatDateForAPI(formData.fromDate),
        end_date: formatDateForAPI(formData.toDate),
        description: formData.description,
        description_en: formData.description,
        graphics: stringfiedGraphics,
        assigned_to: updatedAssignedTo,
        attachments: updatedAttachments,
        maps: updatedMaps,
      };
    } else {
      payload = {
        creator_id: userObject.id,
        name: formData.tripName,
        name_en: formData.tripName,
        start_date: formatDateForAPI(formData.fromDate),
        end_date: formatDateForAPI(formData.toDate),
        description: formData.description,
        description_en: formData.description,
        graphics: stringfiedGraphics,
        is_freezed: false,
        department_id: userObject.department_id,

        attachments: formData.attachments.map((item) => {
          return {
            file_name: item.PrevFileName.substring(
              item.PrevFileName.lastIndexOf("/") + 1
            ),
            file_url: item.data,
          };
        }),
        assigned_to: [
          {
            user_id: formData.manager,
            is_manager: true,
          },
          ...formData.team.map((item) => {
            return {
              user_id: item,
              is_manager: false,
            };
          }),
        ],
        maps: formData.maps.map((item) => {
          return {
            map_id: item,
          };
        }),
      };
    }

    console.log("payload", payload);

    const url = isEdit
      ? `${window.ApiUrl}FieldMission/${location.state.studyData.id}`
      : `${window.ApiUrl}FieldMission`;
    const method = isEdit ? "put" : "post";

    await axios[method](url, payload, {
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => {
        setFormData(defaultFormData);
        props.map.findLayerById("AddStudyGraphicsLayer").removeAll();
        const successMessage = isEdit
          ? `تم تحديث الدراسة الميدانية بنجاح`
          : `تم إنشاء دراسة ميدانية برقم ${response.data.number}`;
        message.success(successMessage);
        navigate("/studies-actor");
      })
      .catch((error) => {
        console.error("Error:", error);
      });
  };

  ///////////// sketch logic /////////////////

  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedColor, setSelectedColor] = useState("#F5A623");
  const [selectedTool, setSelectedTool] = useState("");
  const componentRef = useRef({});
  const { current: sketch } = componentRef;
  const activeSketchName = useRef({});
  const textPointRef = useRef(null);
  const [textModalVisible, setTextModalVisible] = useState(false);
  const [warningMessage, setWarningMessage] = useState(false);
  const activeDelete = useRef(false);
  const activeIconName = useRef();

  const undoStack = useRef([]);
  const redoStack = useRef([]);
  const MAX_HISTORY_SIZE = 50;

  const addToUndoStack = (action) => {
    undoStack.current.push(action);

    if (undoStack.current.length > MAX_HISTORY_SIZE) {
      undoStack.current.shift();
    }
  };

  const cloneGraphic = (graphic) => {
    if (!graphic || !graphic.geometry || !graphic.symbol) return null;
    const cloned = {
      uid: graphic.uid,
      geometry: graphic.geometry.clone(),
      symbol: graphic.symbol.clone(),
      attributes: graphic.attributes ? { ...graphic.attributes } : null,
    };
    return cloned;
  };

  useEffect(() => {
    sketch.current = new Sketch({
      layer: props.map.findLayerById("AddStudyGraphicsLayer"),
      view: props.map.view,
    });

    sketch.current.on("create", (event) => {
      if (event.state == "complete" && activeDelete.current) {
        let intersectedGeometries = [];
        let graphicLayer = props.map.findLayerById("AddStudyGraphicsLayer");

        if (graphicLayer.graphics.items.length > 0) {
          for (
            let index = 0;
            index < graphicLayer.graphics.items.length;
            index++
          ) {
            let graphic = graphicLayer.graphics.items[index];
            if (event.graphic && graphic.visible) {
              if (
                geometryEngine.intersects(
                  event.graphic.geometry,
                  graphic.geometry
                )
              ) {
                intersectedGeometries.push(graphic);
              }
            }
          }
        }

        if (intersectedGeometries.length > 0) {
          let deletedGraphics = [];

          for (let index = 0; index < intersectedGeometries.length; index++) {
            intersectedGeometries[index].visible = false;
            const clonedGraphic = cloneGraphic(intersectedGeometries[index]);
            deletedGraphics.push(clonedGraphic);
          }

          addToUndoStack({
            type: "delete",
            graphics: deletedGraphics,
          });
        }

        graphicLayer.remove(event.graphic);
      } else if (event.state == "complete" && !activeDelete.current) {
        if (activeSketchName.current == "pointText") {
          props.map
            .findLayerById("AddStudyGraphicsLayer")
            .remove(event.graphic);

          if (textPointRef.current.input.value) {
            const textGraphic = drawText(
              event.graphic,
              textPointRef.current.input.value,
              props.map,
              "AddStudyGraphicsLayer",
              15,
              3,
              3,
              event.graphic.symbol.color
            );

            if (textGraphic) {
              addToUndoStack({
                type: "create",
                graphics: [cloneGraphic(textGraphic)],
              });
            }
          }
        } else if (activeSketchName.current == "icon") {
          const drawnIconGraphic = drawIcon(
            event.graphic,
            activeIconName.current
          );
          if (drawnIconGraphic) {
            addToUndoStack({
              type: "create",
              graphics: [cloneGraphic(drawnIconGraphic)],
            });
          }
        } else {
          addToUndoStack({
            type: "create",
            graphics: [cloneGraphic(event.graphic)],
          });
        }
      }
    });

    return () => {
      try {
        if (sketch.current) {
          cancelDraw();
          sketch.current.destroy();
          sketch.current = null;
        }
      } catch (error) {
        console.error("Error during sketch cleanup:", error);
      }
    };
  }, []);

  useEffect(() => {
    console.log("selected tool", selectedTool);
    handleSelectTool(selectedTool);
  }, [selectedColor]);

  const drawPolyLine = () => {
    activeSketchName.current = "polyline";
    sketch.current.viewModel.polylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      style: "solid",
    };
    sketch.current.create("polyline");
  };

  const drawLineWithTwoArrows = () => {
    activeSketchName.current = "polylineWithTwoArrows";
    const arrowPolylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      marker: {
        type: "simple-line",
        color: selectedColor,
        width: 2,
      },
      style: "solid",
      cap: "butt",
      marker: {
        style: "arrow",
        placement: "begin-end",
      },
    };

    sketch.current.viewModel.polylineSymbol = arrowPolylineSymbol;
    sketch.current.create("polyline");
  };

  const drawLineWithRightArrow = () => {
    activeSketchName.current = "polylineWithRighArrow";
    const arrowPolylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      marker: {
        type: "simple-line",
        color: selectedColor,
        width: 2,
      },
      style: "solid",
      cap: "butt",
      marker: {
        style: "arrow",
        placement: "end",
      },
    };

    sketch.current.viewModel.polylineSymbol = arrowPolylineSymbol;
    sketch.current.create("polyline");
  };

  const drawLineWithLeftArrow = () => {
    activeSketchName.current = "polylineWithLeftArrow";
    const arrowPolylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      marker: {
        type: "simple-line",
        color: selectedColor,
        width: 2,
      },
      style: "solid",
      cap: "butt",
      marker: {
        style: "arrow",
        placement: "begin",
      },
    };

    sketch.current.viewModel.polylineSymbol = arrowPolylineSymbol;
    sketch.current.create("polyline");
  };

  const drawRectangle = () => {
    activeSketchName.current = "rectangle";
    let color = Color.fromHex(selectedColor);
    color.a = 0.15;
    sketch.current.viewModel.polygonSymbol = {
      type: "simple-fill",
      color: color,
      outline: {
        color: selectedColor,
        width: 2,
      },
    };
    sketch.current.create("rectangle");
  };

  const drawCircle = () => {
    activeSketchName.current = "circle";
    let color = Color.fromHex(selectedColor);
    color.a = 0.1;
    sketch.current.viewModel.polygonSymbol = {
      type: "simple-fill",
      color: color,
      outline: {
        color: selectedColor,
        width: 2,
      },
    };
    sketch.current.create("circle");
  };

  const drawPolyLineFreeHand = () => {
    activeSketchName.current = "freeHand";
    sketch.current.viewModel.polylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      style: "solid",
    };
    sketch.current.create("polyline", { mode: "freehand" });
  };

  const drawTextHandle = () => {
    if (
      textPointRef.current.input.value === "" ||
      textPointRef.current.input.value.trim().length == 0
    ) {
      setWarningMessage(true);
    } else {
      setWarningMessage(false);
      sketch.current.viewModel.pointSymbol = {
        type: "simple-marker",
        style: "circle",
        color: selectedColor,
        size: "6px",
        outline: {
          color: selectedColor,
          width: 2,
        },
      };

      sketch.current.create("point");
      activeSketchName.current = "pointText";
      setTextModalVisible(false);
    }
  };

  const cancelDraw = () => {
    sketch.current.cancel();
  };

  const handleDeleteIcon = () => {
    activeDelete.current = true;
    cancelDraw();
    sketch.current.viewModel.polygonSymbol = {
      type: "simple-fill",
      color: [0, 0, 0, 0.1],
      outline: {
        color: [0, 0, 0],
        width: 2,
      },
    };

    sketch.current.create("rectangle");
  };

  const handleUndo = () => {
    if (undoStack.current.length === 0) {
      console.log("Nothing to undo");
      message.warning(t("undoWarningMessage"));
      return;
    }

    const action = undoStack.current.pop();
    const graphicLayer = props.map.findLayerById("AddStudyGraphicsLayer");

    if (action.type === "create") {
      action.graphics.forEach((graphicData) => {
        const actualGraphic = graphicLayer.graphics.items.find(
          (graphic) => graphic.uid === graphicData.uid
        );

        if (actualGraphic) {
          actualGraphic.visible = false;
        }
      });

      redoStack.current.push({
        type: "create",
        graphics: action.graphics,
      });
    } else if (action.type === "delete") {
      action.graphics.forEach((graphicData) => {
        const actualGraphic = graphicLayer.graphics.items.find(
          (graphic) => graphic.uid === graphicData.uid
        );

        if (actualGraphic) {
          actualGraphic.visible = true;
        }
      });

      redoStack.current.push({
        type: "delete",
        graphics: action.graphics,
      });
    }
  };

  const handleRedo = () => {
    if (redoStack.current.length === 0) {
      console.log("Nothing to redo");
      message.warning(t("redoWarningMessage"));
      return;
    }

    const action = redoStack.current.pop();
    const graphicLayer = props.map.findLayerById("AddStudyGraphicsLayer");

    if (action.type === "create") {
      action.graphics.forEach((graphicData) => {
        const actualGraphic = graphicLayer.graphics.items.find(
          (graphic) => graphic.uid === graphicData.uid
        );

        if (actualGraphic) {
          actualGraphic.visible = true;
        }
      });

      undoStack.current.push({
        type: "create",
        graphics: action.graphics,
      });
    } else if (action.type === "delete") {
      action.graphics.forEach((graphicData) => {
        const actualGraphic = graphicLayer.graphics.items.find(
          (graphic) => graphic.uid === graphicData.uid
        );

        if (actualGraphic) {
          actualGraphic.visible = false;
        }
      });

      undoStack.current.push({
        type: "delete",
        graphics: action.graphics,
      });
    }
  };

  const handleDrawIcon = (iconName) => {
    activeSketchName.current = "icon";
    activeIconName.current = iconName;
    sketch.current.viewModel.pointSymbol = {
      type: "simple-marker",
      style: "circle",
      color: selectedColor,
      size: "6px",
      outline: {
        color: selectedColor,
        width: 2,
      },
    };
    sketch.current.create("point");
  };

  const drawIcon = (pointGraphic, iconName) => {
    if (!props.map.view) return;

    const iconUrl = require(`../../assets/images/study-actor/pics/${iconName}.png`);
    const pictureMarkerSymbol = {
      type: "picture-marker",
      url: iconUrl.default,
      width: "32px",
      height: "32px",
    };

    pointGraphic.symbol = pictureMarkerSymbol;
    props.map.findLayerById("AddStudyGraphicsLayer").graphics.add(pointGraphic);

    return pointGraphic;
  };

  const handleSelectTool = (tool) => {
    if (tool) {
      setSelectedTool(tool.name);
      if (tool.name != "delete") {
        activeDelete.current = false;
        cancelDraw();
      }
      switch (tool.name) {
        case "straight-line":
          drawPolyLine();
          break;

        case "free-hand":
          drawPolyLineFreeHand();
          break;
        case "line-with-arrows":
          drawLineWithTwoArrows();
          break;
        case "circle":
          drawCircle();
          break;
        case "rectangle":
          drawRectangle();
          break;

        case "line-right-arrow":
          drawLineWithRightArrow();
          break;

        case "line-left-arrow":
          drawLineWithLeftArrow();
          break;

        case "pan":
          cancelDraw();
          break;
        case "undo":
          handleUndo();
          break;
        case "redo":
          handleRedo();
          break;
        case "delete":
          handleDeleteIcon();
          break;

        case "pointText":
          if (!showColorPicker) {
            showTextInput();
          }
          break;
      }

      if (tool.name == "location_marker") {
        handleDrawIcon(tool.name);
      }
    }
  };

  const showTextInput = () => {
    setTextModalVisible(true);
  };

  return (
    <div
      className="study-actor"
      style={{
        padding: "10px",
        overflow: "auto",
        height: "calc(100vh - 80px)",
      }}
    >
      <div
        style={{ height: "1.5px", background: "#fff", marginBlock: "10px" }}
      />
      <div>
        <div>
          <label className="selectLabelStyle">
            {t("studiesActor.trip_name_label")}
          </label>
          <Input
            // maxLength={100}
            // minLength={3}
            placeholder={t("studiesActor.trip_name_label")}
            value={formData.tripName}
            onChange={(e) => handleChange("tripName", e.target.value)}
          />
        </div>

        <div
          style={{
            display: "flex",
            gap: "10px",
          }}
        >
          <div>
            <label className="selectLabelStyle">
              {t("studiesActor.from_label")}
            </label>
            <DatePicker
              value={
                formData.fromDate
                  ? (() => {
                      // Handle different date formats
                      let date;
                      if (
                        formData.fromDate.includes("AM") ||
                        formData.fromDate.includes("PM")
                      ) {
                        // Try both formats for dates with time
                        date = dayjs(formData.fromDate, "D/M/YYYY hh:mm A"); // Single digit day/month
                        if (!date.isValid()) {
                          date = dayjs(formData.fromDate, "DD/MM/YYYY hh:mm A"); // Double digit day/month
                        }
                      } else {
                        // Try both formats for dates without time
                        date = dayjs(formData.fromDate, "D/M/YYYY"); // Single digit day/month
                        if (!date.isValid()) {
                          date = dayjs(formData.fromDate, "DD/MM/YYYY"); // Double digit day/month
                        }
                      }
                      return date.isValid() ? date : null;
                    })()
                  : null
              }
              onChange={(date) =>
                date
                  ? handleChange("fromDate", date.format("DD/MM/YYYY"))
                  : handleChange("fromDate", null)
              }
              placeholder={t("studiesActor.from_placeholder")}
            />
          </div>
          <div>
            <label className="selectLabelStyle">
              {t("studiesActor.to_label")}
            </label>
            <DatePicker
              value={
                formData.toDate
                  ? (() => {
                      // Handle different date formats
                      let date;
                      if (
                        formData.toDate.includes("AM") ||
                        formData.toDate.includes("PM")
                      ) {
                        // Try both formats for dates with time
                        date = dayjs(formData.toDate, "D/M/YYYY hh:mm A"); // Single digit day/month
                        if (!date.isValid()) {
                          date = dayjs(formData.toDate, "DD/MM/YYYY hh:mm A"); // Double digit day/month
                        }
                      } else {
                        // Try both formats for dates without time
                        date = dayjs(formData.toDate, "D/M/YYYY"); // Single digit day/month
                        if (!date.isValid()) {
                          date = dayjs(formData.toDate, "DD/MM/YYYY"); // Double digit day/month
                        }
                      }
                      return date.isValid() ? date : null;
                    })()
                  : null
              }
              onChange={(date) =>
                date
                  ? handleChange("toDate", date.format("DD/MM/YYYY"))
                  : handleChange("toDate", null)
              }
              placeholder={t("studiesActor.to_placeholder")}
            />
          </div>
        </div>

        <div>
          <label className="selectLabelStyle">
            {t("studiesActor.manager_label")}
          </label>
          <FormControl fullWidth>
            <Select
              value={formData.manager || ""}
              onChange={(event) => {
                const value = event.target.value;
                const selectedUser = usersList.find(
                  (user) => user.id === value
                );
                handleChange("manager", value);
                handleChange(
                  "managerName",
                  selectedUser ? selectedUser.name : ""
                );
              }}
              displayEmpty
              className="mui-select-global"
              renderValue={(selected) => {
                if (!selected) {
                  return t("studiesActor.select_team_placeholder");
                }
                const found = usersList.find((user) => user.id === selected);
                return found ? found.name : selected;
              }}
              MenuProps={{
                PaperProps: {
                  className: "mui-select-menu-global",
                  style: {
                    zIndex: 9999,
                  },
                },
              }}
            >
              {usersList
                .filter((userItem) => userItem.id && userItem.name)
                .map((userItem) => (
                  <MenuItem key={userItem.id} value={userItem.id}>
                    {userItem.name}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </div>

        <div>
          <label className="selectLabelStyle">
            {t("studiesActor.select_team_label")}
          </label>

          <FormControl fullWidth>
            <Select
              multiple
              value={formData.team || []}
              onChange={(event) => {
                const values =
                  typeof event.target.value === "string"
                    ? event.target.value.split(",")
                    : event.target.value;
                handleChange("team", values);
              }}
              displayEmpty
              className="mui-select-global"
              renderValue={(selected) => {
                if (!selected || selected.length === 0) {
                  return t("studiesActor.select_team_placeholder");
                }
                return (
                  <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                    {selected.map((id) => {
                      const found = usersList.find((user) => user.id === id);
                      const value = found ? found.name : id;
                      return (
                        <Chip sx={{ color: "#fff" }} key={id} label={value} />
                      );
                    })}
                  </Box>
                );
              }}
              MenuProps={{
                PaperProps: {
                  className: "mui-select-menu-global",
                  style: {
                    zIndex: 9999,
                  },
                },
              }}
            >
              {usersList
                .filter(
                  (userItem) =>
                    userItem.id &&
                    userItem.name &&
                    userItem.id !== formData.manager
                )
                .map((userItem) => (
                  <MenuItem key={userItem.id} value={userItem.id}>
                    {userItem.name}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </div>

        <>
          <div
            style={{
              fontSize: "16px",
              fontWeight: "700",
              color: "#fff",
              textAlign: "start",
              marginBlock: "15px",
            }}
          >
            {t("studiesActor.define_scope")}
          </div>
          <div className="box">
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                color: "#fff",
                cursor: "pointer",
              }}
              onClick={() => setShowTools(!showTools)}
            >
              <div
                style={{
                  display: "flex",
                  gap: "5px",
                  alignItems: "center",
                }}
              >
                <img src={tools_logo} alt="tools logo" />
                <span
                  style={{
                    textAlign: "start",
                    display: "block",
                  }}
                >
                  {t("drawingShape")}
                </span>
              </div>

              <div
                style={{
                  display: "flex",
                  gap: "5px",
                  alignItems: "center",
                }}
              >
                {/* <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSelectTool({ name: "delete" });
                  }}
                >
                  {t("stopDraw")}
                </Button> */}
                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    props.map
                      .findLayerById("AddStudyGraphicsLayer")
                      .removeAll();
                  }}
                >
                  {t("clearAll")}
                </Button>
                <MdKeyboardArrowDown
                  size={20}
                  style={{
                    transform: `rotate(${!showTools ? "180deg" : 0})`,
                  }}
                />
              </div>
            </div>

            {showTools && (
              <div
                className="images"
                style={{
                  display: "grid",
                  gridTemplateColumns: "repeat(6, 1fr)",
                  gap: "10px",
                  marginTop: "10px",
                }}
              >
                {tools.map((tool, indx) => (
                  <Tooltip title={tool.label} placement="bottom">
                    <div
                      key={indx}
                      name={tool.name}
                      className="image"
                      style={{
                        cursor: "pointer",
                        background: selectedTool === tool.name ? "#B55433" : "",
                      }}
                      onClick={() => {
                        handleSelectTool({
                          name: tool.name,
                          src: tool.src,
                          index: indx,
                        });
                      }}
                    >
                      <img
                        src={tool.src}
                        alt="tool"
                        style={{
                          filter:
                            selectedTool === tool.name
                              ? "brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(2%) hue-rotate(264deg) brightness(105%) contrast(101%)"
                              : "",
                        }}
                      />
                    </div>
                  </Tooltip>
                ))}

                {/* start color picker */}
                <div
                  className="image"
                  onClick={() => {
                    setShowColorPicker(!showColorPicker);
                  }}
                  style={{
                    background: selectedColor,
                  }}
                >
                  <img
                    src={tool_12}
                    alt="select color"
                    style={{
                      filter:
                        selectedColor === "#ffffff"
                          ? "brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(7500%) hue-rotate(16deg) brightness(94%) contrast(106%)"
                          : "",
                    }}
                  />
                </div>

                {showColorPicker && (
                  <div
                    style={{
                      position: "absolute",
                      direction: "ltr",
                    }}
                  >
                    <SketchPicker
                      color={selectedColor}
                      onChange={(color) => {
                        setSelectedColor(color.hex);
                        setShowColorPicker(false);
                      }}
                    />
                  </div>
                )}
                {/* end color picker */}
              </div>
            )}
          </div>
        </>

        <div>
          <label className="selectLabelStyle">
            {t("studiesActor.description_label")}
          </label>
          <Input
            // maxLength={1000}
            // minLength={3}
            placeholder={t("studiesActor.description_placeholder")}
            value={formData.description}
            onChange={(e) => handleChange("description", e.target.value)}
          />
        </div>

        <div className="upload-file">
          <input
            type="file"
            style={{
              display: "none",
            }}
            multiple
            ref={fileInputRef}
            accept="
      image/*,
      application/pdf,
      application/vnd.ms-excel,
      application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,
      application/msword,
      application/vnd.openxmlformats-officedocument.wordprocessingml.document
    "
            onChange={handleFileChange}
          />
          <button
            onClick={handleFileUploadClick}
            className="SearchBtn"
            style={{ marginBlock: "10px" }}
          >
            {t("studiesActor.attach_file")}
          </button>
        </div>

        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "10px",
          }}
        >
          {[...selectedFiles].map((file) => (
            <div
              key={file.name}
              style={{
                color: "#fff",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                gap: "10px",
                padding: "10px",
                backgroundColor: "rgb(255 255 255 / 40%)",
                borderRadius: "5px",
              }}
            >
              <div
                onClick={() => {
                  handleFileClick(file.name);
                }}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                  flexShrink: "0",
                }}
              >
                <FaMap />
                <div
                  style={{
                    maxWidth: "200px",
                    wordWrap: "break-word",
                  }}
                >
                  {file.name}
                </div>
              </div>
              <FaTrashAlt
                style={{ cursor: "pointer" }}
                onClick={() => handleFileDeletion(file.name)}
              />
            </div>
          ))}
        </div>

        <div
          style={{
            display: "flex",
            flexDirection: "column",
            // gap: "10px",
            alignItems: "start",
          }}
        >
          {/* <button className="SearchBtn" style={{ marginBlock: "10px" }}>
            {t("studiesActor.attach_map")}
          </button> */}

          <label className="selectLabelStyle">
            {t("studiesActor.attach_map")}
          </label>

          {/* attacched maps style */}

          {mapsList.length > 0 && (
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(2, 1fr)",
                gap: "10px",
                marginTop: "10px",
                textAlign: "start",
                fontSize: "14px",
                width: "100%",
              }}
            >
              {mapsList.map((mapItem, index) => (
                <div
                  style={{
                    backgroundColor: checkedMaps[index]
                      ? "#F4E5E1"
                      : "#FFFFFF66",
                    color: checkedMaps[index] ? "#85736E" : "#fff",
                    padding: "7px",
                    borderRadius: "5px",
                    borderBottom: "1px solid #B45333",
                    position: "relative",
                    transition: "background 0.2s, color 0.2s",
                  }}
                  key={index}
                >
                  <Checkbox
                    checked={checkedMaps[index]}
                    onChange={(e) => {
                      const newChecked = [...checkedMaps];
                      newChecked[index] = e.target.checked;
                      setCheckedMaps(newChecked);
                      let newMapIds;
                      if (formData.maps.includes(mapItem.id)) {
                        newMapIds = formData.maps.filter(
                          (id) => id !== mapItem.id
                        );
                      } else {
                        newMapIds = [mapItem.id, ...formData.maps];
                      }
                      handleChange("maps", newMapIds);
                    }}
                    style={{
                      position: "absolute",
                      right: "10px",
                      top: "10px",
                    }}
                  />
                  <img
                    src={mapItem.thumbnail ? mapItem.thumbnail : map_image}
                    style={{ width: "100%" }}
                    alt=""
                  />
                  <div>{mapItem.title_ar}</div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div>
          <button
            className="SearchBtn"
            style={{ marginBlock: "10px" }}
            onClick={handleCreateStudyButton}
          >
            {location.state && location.state.studyData
              ? t("studiesActor.update_trip")
              : t("studiesActor.create_trip")}
          </button>
        </div>
      </div>

      <>
        <Modal
          title={t("enterText", { ns: "common" })}
          centered
          visible={textModalVisible}
          onCancel={() => {
            setTextModalVisible(false);
            setWarningMessage(false);
          }}
          okText={t("edit")}
          cancelText={t("cancel")}
        >
          <Input
            name="pointText"
            maxLength={70}
            ref={textPointRef}
            placeholder={t("enterText2", { ns: "common" })}
            onChange={(e) => {
              setWarningMessage(
                e.target.value === "" || e.target.value.trim().length == 0
              );
            }}
          />
          {warningMessage && (
            <span
              style={{
                display: "inline-flex",
                alignItems: "center",
                padding: "4px 8px",
                borderRadius: "4px",
                fontSize: "16px",
                color: "#ff3333",
              }}
            >
              <span style={{ marginRight: "4px", fontSize: "18px" }}>⚠️</span>
              <span style={{ fontWeight: "bold" }}>
                {t("enterText", { ns: "common" })}
              </span>
            </span>
          )}
          <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                drawTextHandle();
              }}
            >
              {t("confirm", { ns: "common" })}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                setTextModalVisible(false);
                setWarningMessage(false);
              }}
            >
              {t("close", { ns: "common" })}
            </Button>
          </div>
        </Modal>
      </>
    </div>
  );
}
