import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import MuiDrawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import { Tooltip } from "@mui/material";
import { Button, Upload, message } from "antd";
import { Fade } from "react-reveal";
import { clearGraphicLayer } from "../helper/common_func";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import Draw from "@arcgis/core/views/draw/Draw";
import Zoom from "@arcgis/core/widgets/Zoom";
import Graphic from "@arcgis/core/Graphic";
import Extent from "@arcgis/core/geometry/Extent";
import { useTranslation } from "react-i18next";
import AllTools from "../mapComponents/tools/AllTools";
import { Md<PERSON>eyboardDoubleArrowRight } from "react-icons/md";
import login_img from "../assets/images/mapTools/login.svg";
import help_imag from "../assets/images/mapTools/help.svg";
import full_screen_img from "../assets/images/mapTools/full_screen.svg";
import identify_img from "../assets/images/mapTools/identify.svg";
import layers_img from "../assets/images/mapTools/layers.svg";
import basemap_img from "../assets/images/mapTools/basemap.svg";
import google_img from "../assets/images/mapTools/google.svg";
import compare_layers_img from "../assets/images/mapTools/compare_layers.svg";
import small_map_img from "../assets/images/mapTools/small_map.svg";
import full_map_img from "../assets/images/mapTools/full_map.svg";
import zoomin_img from "../assets/images/mapTools/zoomin.svg";
import zoomout_img from "../assets/images/mapTools/zoomout.svg";
import next_img from "../assets/images/mapTools/next.svg";
import previous_img from "../assets/images/mapTools/previous.svg";
import move_img from "../assets/images/mapTools/move.svg";
import clear_img from "../assets/images/mapTools/clear.svg";
import ServicesSearch from "../mapComponents/Services/ServicesSearch";

export default function MapToolsMenu(props) {
  const location = useLocation();

  const { t } = useTranslation("map");

  const [openServSearch, setServSearch] = useState(false);
  const [activeService, setActiveService] = useState(0);
  const [activeServiceItem, setActiveServiceItem] = useState(null);
  const [openToolData, setToolData] = useState(false);
  const [activeTool, setActiveTool] = useState(0);

  const openServiceSearch = (e) => {
    setServSearch(true);
    setActiveService(e.id);
    setActiveServiceItem(e);
    setToolData(false);
    setActiveTool("");
  };

  const openToolsMenu = () => {
    if (openToolservice) {
      setToolService(false);
      setToolData(false);
      setActiveTool("");
      setActiveService(0);
      setServSearch(false);
    } else {
      setToolService(true);
      setToolData(false);
      setActiveTool("");
      setActiveService(0);
      setServSearch(false);
    }
  };

  const closeToolsData = (e) => {
    setToolData(false);
    setActiveTool("");
  };

  const closeServiceSearch = () => {
    setServSearch(false);
    setActiveService(0);
  };

  const enableViewPanning = () => {
    if (window.__evtViewDragHandler) {
      window.__evtViewDragHandler.remove();
      window.__evtViewDragHandler = null;
    }
    if (window.__evtViewKeyDownHandler) {
      window.__evtViewKeyDownHandler.remove();
      window.__evtViewKeyDownHandler = null;
    }
  };

  const disableViewPanning = () => {
    if (window.__evtViewDragHandler) {
      window.__evtViewDragHandler.remove();
      window.__evtViewDragHandler = null;
    }
    if (window.__evtViewKeyDownHandler) {
      window.__evtViewKeyDownHandler.remove();
      window.__evtViewKeyDownHandler = null;
    }
    window.__evtViewDragHandler = props.map.view.on("drag", (event) => {
      // prevents panning with the mouse drag event
      if (activeTool != "dis") event.stopPropagation();
    });

    window.__evtViewKeyDownHandler = props.map.view.on("key-down", (event) => {
      // prevents panning with the arrow keys
      var keyPressed = event.key;
      if (keyPressed.slice(0, 5) === "Arrow") {
        if (activeTool != "dis") event.stopPropagation();
      }
    });
  };

  const displayZoomOutCursor = () => {
    props.map.view.container.style.cursor = "zoom-out";
  };
  const displayZoomInCursor = () => {
    props.map.view.container.style.cursor = "zoom-in";
  };
  // const displayCrosshairCursor = () => {
  //   props.map.view.container.style.cursor = "crosshair";
  // };
  // const displayPointerCursor = () => {
  //   props.map.view &&
  //     props.map.view.container &&
  //     props.map.view.container.style &&
  //     "pointer" !== props.map.view.container.style.cursor &&
  //     (props.map.view.container.style.cursor = "pointer");
  // };
  const displayDefaultCursor = () => {
    props.map.view &&
      props.map.view.container &&
      props.map.view.container.style &&
      "default" !== props.map.view.container.style.cursor &&
      (props.map.view.container.style.cursor = "default");
  };

  const removeCurrentSelTool = () => {
    props.map.view.popup.close();
  };

  const getExtentfromVertices = (vertices) => {
    var sx = vertices[0][0],
      sy = vertices[0][1];
    var ex = vertices[1][0],
      ey = vertices[1][1];
    var rect = {
      x: Math.min(sx, ex),
      y: Math.max(sy, ey),
      width: Math.abs(sx - ex),
      height: Math.abs(sy - ey),
      spatialReference: props.map.view.spatialReference,
    };
    if (rect.width > -1 || rect.height > -1) {
      return new Extent({
        xmin: parseFloat(rect.x),
        ymin: parseFloat(rect.y) - parseFloat(rect.height),
        xmax: parseFloat(rect.x) + parseFloat(rect.width),
        ymax: parseFloat(rect.y),
        spatialReference: rect.spatialReference,
      });
    } else {
      return null;
    }
  };

  const drawRect = (event) => {
    var vertices = event.vertices;
    //remove existing graphic
    props.map.view.graphics.removeAll();
    if (vertices.length < 2) {
      return;
    }

    // create a new extent
    var extent = getExtentfromVertices(vertices);

    var graphic = new Graphic({
      geometry: extent,
      symbol: {
        type: "simple-fill", // autocasts as SimpleFillSymbol
        color: [157, 66, 35, 0.1],
        style: "solid",
        outline: {
          // autocasts as SimpleLineSymbol
          color: [180, 83, 51],
          width: 1.5,
        },
      },
    });

    props.map.view.graphics.add(graphic);
  };

  function zoomOut(evt) {
    var vertices = evt.vertices;
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
    props.map.view.graphics.removeAll();
    var action = window.__draw.create("rectangle");
    //view.focus();
    action.on("vertex-add", drawRect);
    action.on("draw-complete", zoomOut);
    action.on("cursor-update", drawRect);
    if (evt.vertices.length === 1) {
      props.map.view.goTo({ scale: props.map.view.scale * 2 });
      return;
    }
    var sx = vertices[0][0],
      sy = vertices[0][1];
    var ex = vertices[1][0],
      ey = vertices[1][1];
    var rect = {
      x: Math.min(sx, ex),
      y: Math.max(sy, ey),
      width: Math.abs(sx - ex),
      height: Math.abs(sy - ey),
      spatialReference: props.map.view.spatialReference,
    };
    if (rect.width !== 0 || rect.height !== 0) {
      var scrPnt1 = props.map.view.toScreen(rect);
      var scrPnt2 = props.map.view.toScreen({
        x: rect.x + rect.width,
        y: rect.y,
        spatialReference: rect.spatialReference,
      });
      var mWidth = props.map.view.extent.width;
      var zoomFactor = 0.001; // 10% reduction in zoom-out
      var delta =
        ((mWidth * props.map.view.width) / Math.abs(scrPnt2.x - scrPnt1.x) -
          mWidth) *
        zoomFactor;
      var vExtent = props.map.view.extent;
      props.map.view.goTo(
        new Extent({
          xmin: vExtent.xmin - delta,
          ymin: vExtent.ymin - delta,
          xmax: vExtent.xmax + delta,
          ymax: vExtent.ymax + delta,
          spatialReference: vExtent.spatialReference,
        })
      );
    }
  }

  const zoomIn = (evt) => {
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
    props.map.view.graphics.removeAll();
    var action = window.__draw.create("rectangle");
    //props.map.view.focus();
    action.on("vertex-add", drawRect);
    action.on("draw-complete", zoomIn);
    action.on("cursor-update", drawRect);
    if (evt.vertices.length === 1) {
      props.map.view.goTo({ scale: props.map.view.scale * 0.02 });
      return;
    }
    var extent = getExtentfromVertices(evt.vertices);

    if (extent.width !== 0 || extent.height !== 0) {
      props.map.view.goTo(extent);
    }
  };

  const activeZoomIn = (e) => {
    if (true) {
      setActiveTool("zoomIn");
      removeCurrentSelTool();
      disableViewPanning();
      props.map.view.graphics.removeAll();
      if (!window.__draw) {
        window.__draw = new Draw({
          view: props.map.view,
        });
      }
      var action = window.__draw.create("rectangle");
      displayZoomInCursor();
      //props.map.view.focus();
      action.on("vertex-add", drawRect);
      action.on("draw-complete", zoomIn);
      action.on("cursor-update", drawRect);
    } else {
      var zoom = new Zoom({
        view: props.map.view,
        visible: false,
      });

      zoom.zoomIn();
    }
  };

  const activeZoomOut = () => {
    if (true) {
      setActiveTool("zoomOut");
      removeCurrentSelTool();
      disableViewPanning();
      props.map.view.graphics.removeAll();
      if (!window.__draw) {
        window.__draw = new Draw({
          view: props.map.view,
        });
      }
      var action = window.__draw.create("rectangle");
      displayZoomOutCursor();
      //view.focus();
      action.on("vertex-add", drawRect);
      action.on("draw-complete", zoomOut);
      action.on("cursor-update", drawRect);
    } else {
      var zoom = new Zoom({
        view: props.map.view,
        visible: false,
      });

      zoom.zoomOut();
    }
  };

  const goToPreviousExtent = () => {
    setActiveTool("prev");
    if (window.__extentHistory[window.__extentHistoryIndx].preExtent) {
      window.__prevExtent = true;
      if (window.__extentHistoryIndx > 0) {
        props.map.view.goTo(
          window.__extentHistory[window.__extentHistoryIndx].preExtent
        );
        window.__extentHistoryIndx--;
      }
    }
  };

  const goToNextExtent = () => {
    setActiveTool("next");
    window.__nextExtent = true;
    if (window.__extentHistory.length > window.__extentHistoryIndx + 1) {
      window.__extentHistoryIndx++;
      props.map.view.goTo(
        window.__extentHistory[window.__extentHistoryIndx].currentExtent
      );
    }
  };

  const goToFullExtent = () => {
    setActiveTool("fullExt");
    props.map.view.goTo(window.__fullExtent);
    if (props.setIndicatorFullExtent) {
      props.setIndicatorFullExtent();
    }
  };

  const disableActiveTool = () => {
    setActiveTool("dis");
    removeCurrentSelTool();

    enableViewPanning();
    displayDefaultCursor();
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
  };

  window.DisableActiveTool = () => {
    disableActiveTool();
  };

  const removeAllGraphicsOnMap = () => {
    props.map.view.graphics.removeAll();

    props.map.layers.items.forEach((layer) => {
      clearGraphicLayer(layer.id, props.map);
    });
  };

  const extentChangeHandler = (evt) => {
    if (window.__prevExtent || window.__nextExtent) {
      window.__currentExtent = evt;
    } else {
      window.__preExtent = window.__currentExtent;
      window.__currentExtent = evt;
      window.__extentHistory = window.__extentHistory || [];
      window.__extentHistory.push({
        preExtent: window.__preExtent,
        currentExtent: window.__currentExtent,
      });
      window.__extentHistoryIndx = window.__extentHistory.length - 1;
    }
    window.__prevExtent = window.__nextExtent = false;
    //console.log('extent--------',_extentHistory);
    //extentHistoryChange();
  };

  useEffect(() => {
    watchUtils.whenTrue(props.map.view, "ready", () => {
      window.__fullExtent = props.map.view.extent.clone();
      window.__draw = new Draw({
        view: props.map.view,
      });
      watchUtils.whenOnce(props.map.view, "extent", () => {
        watchUtils.when(props.map.view, "stationary", (evt) => {
          if (evt) {
            extentChangeHandler(props.map.view.extent);
          }
        });
      });
    });
  }, []);

  const openedMixin = (theme) => ({
    width:
      location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "generalSearch" ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "search" ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "coordinateSearch" ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "marsed"
        ? 450
        : location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ==
          ""
          ? 200
          : location.pathname == process.env.PUBLIC_URL
            ? 200
            : location.pathname.substring(
              location.pathname.lastIndexOf("/") + 1
            ) === "metaDataSearch"
              ? 200
              : 370,
    transition: theme.transitions.create("width", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    overflowX: "hidden",
  });

  const closedMixin = (theme) => ({
    transition: theme.transitions.create("width", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    overflowX: "hidden",
    width: `calc(${theme.spacing(7)} + 1px)`,
    [theme.breakpoints.up("sm")]: {
      width: `calc(${theme.spacing(9)} + 1px)`,
    },
  });

  const openHelp = (e) => {
    e.preventDefault();
    setServSearch(false);
    setActiveService(0);
    setToolData(true);
    e !== undefined && e.target !== undefined
      ? setActiveTool(e.target.name)
      : setActiveTool("");
    localStorage.removeItem("showHelp");
    localStorage.removeItem("showMetaHelp");
    localStorage.removeItem("showOpenSideHelp");
    localStorage.removeItem("showCardsResultHelp");
    localStorage.removeItem("showCardDetailsHelp");
    setTimeout(() => {
      props.setHelpShow(false);
      props.setHelpShow(true);
    }, 1);
    props.setHelpShow(false);
  };

  const openToolsData = (e) => {
    setServSearch(false);
    setActiveService(0);
    setToolData(true);
    e !== undefined && e.currentTarget !== undefined
      ? setActiveTool(e.currentTarget.name)
      : setActiveTool("");
    // if (e.currentTarget.name == "compareLayers") {
    //   var swipe = props.map.view.ui._components.find(
    //     (x) =>
    //       x.widget._container &&
    //       x.widget._container.className.indexOf("swipe") > -1
    //   );

    //   if (swipe) {
    //     swipe.widget.destroy();
    //     props.map.view.ui.remove(swipe);
    //     setActiveTool("");
    //   } else {
    //     var swipeLayer = props.map.findLayerById("baseMap");
    //     let swipe = new Swipe({
    //       view: props.map.view,
    //       leadingLayers: [swipeLayer],
    //       direction: "horizontal", // swipe widget will move from top to bottom of view
    //       position: 50, // position set to middle of the view (50%)
    //     });
    //     props.map.view.ui.add(swipe);
    //   }
    // } else if (e.currentTarget.name == "traffic") {
    //   if (!props.map.findLayerById("trafficLayerId")) {
    //     let layer = new CustomTileLayer({
    //       urlTemplate: window.trafficUrl,
    //       id: "trafficLayerId",
    //     });

    //     props.map.layers.add(layer);
    //   } else {
    //     props.map.remove(props.map.findLayerById("trafficLayerId"));
    //   }
    // }
  };

  const [openToolservice, setToolService] = useState(true);

  let leftSidebarOperation = [
    {
      id: 5,
      title: "mapToolsServices.mapKey",
      name: "layersMenu",
      image: layers_img,
      onClick: openToolsData,
    },
    {
      id: 4,
      title: "mapToolsServices.inquiry",
      name: "inquiry",
      image: identify_img,
      onClick: openToolsData,
    },
    {
      id: 6,
      title: "mapToolsServices.Basemap",
      name: "Basemap",
      image: basemap_img,
      onClick: openToolsData,
    },
    {
      id: 7,
      title: "mapToolsServices.googleMaps",
      name: "googleMaps",
      image: google_img,
      onClick: openToolsData,
    },
    {
      id: 8,
      title: "mapToolsServices.compareLayers",
      name: "compareLayers",
      image: compare_layers_img,
      onClick: openToolsData,
    },
    {
      id: 9,
      title: "mapToolsServices.smallMap",
      name: "smallMap",
      image: small_map_img,
      onClick: openToolsData,
    },
    {
      id: 10,
      title: "mapTools.fullMap",
      name: "fullMap",
      image: full_map_img,
      onClick: goToFullExtent,
    },
    {
      id: 11,
      title: "mapTools.zoomIn",
      name: "zoomIn",
      image: zoomin_img,
      onClick: activeZoomIn,
    },
    {
      id: 12,
      title: "mapTools.zoomOut",
      name: "zoomOut",
      image: zoomout_img,
      onClick: activeZoomOut,
    },
    {
      id: 13,
      title: "mapTools.next",
      name: "next",
      image: next_img,
      onClick: goToNextExtent,
    },
    {
      id: 14,
      title: "mapTools.prev",
      name: "prev",
      image: previous_img,
      onClick: goToPreviousExtent,
    },
    {
      id: 15,
      title: "mapTools.move",
      name: "move",
      image: move_img,
      onClick: disableActiveTool,
    },
    {
      id: 16,
      title: "mapTools.removeAll",
      name: "removeAll",
      image: clear_img,
      onClick: removeAllGraphicsOnMap,
    },
    {
      id: 3,
      title: "mapTools.fullScreen",
      name: "fullScreen",
      image: full_screen_img,
      onClick: props.handle.enter,
    },
    {
      id: 2,
      title: "mapToolsServices.help",
      name: "help",
      image: help_imag,
      onClick: openHelp,
    },
  ];

  if (localStorage.getItem("user")) {
    leftSidebarOperation.push({
      id: 17,
      title: "mapToolsServices.logout",
      name: "logout",
      image: login_img,
      onClick: openToolsData,
    });
  } else {
    leftSidebarOperation.unshift({
      id: 1,
      title: "mapToolsServices.login",
      name: "login",
      image: login_img,
      onClick: openToolsData,
    });
  }

  const [sideLinks, setSideLinks] = useState(leftSidebarOperation);

  const [openMapToolsDrawer, setOpenMapToolsDrawer] = useState(true);

  return (
    <MuiDrawer
      variant="permanent"
      open={openMapToolsDrawer}
      // open={props.openMapToolsDrawer}
      anchor="right"
      className={`mapToolsHelp SideMenu map-tools-menu ${location.pathname.substring(location.pathname.lastIndexOf("/")) === "/"
          ? "sidemenu-home"
          : ""
        } ${!openMapToolsDrawer && "closed-sidemenu"}`}
    >
      <Fade left>
        <>
          <div className="open-close-map-tool-menu">
            <IconButton
              className="openSideHelp"
              color="inherit"
              aria-label="open drawer"
              onClick={
                () => {
                  setOpenMapToolsDrawer(!openMapToolsDrawer);
                  props.setCloseBoth(true);
                }

                // props.setOpenMapToolsDrawer(!props.openMapToolsDrawer)
              }
              edge="start"
              sx={{
                background: "#fff !important",
                width: "30px",
                height: "30px",
                borderRadius: "50%",
                cursor: "pointer",
                border: "1px solid #ddd",
              }}
            >
              <MdKeyboardDoubleArrowRight
                style={{
                  transform: !openMapToolsDrawer
                    ? "rotate(180deg)"
                    : "rotate(0deg)",
                }}
              />
            </IconButton>
          </div>

          <div className={`MuiList-root maptoolsHelp`}>
            {sideLinks.map((text, index) => (
              <Tooltip title={t(text.title)} placement="left" key={index}>
                <div className="sideLinkDiv" id={text.name}>
                  <Button
                    id={text.name}
                    variant="contained"
                    component="label"
                    onClick={(e) => {
                      debugger
                      text.onClick(e);
                      if (text.name === "login") {
                        window.open(
                          `${window.portalUrl}`,
                          "_self"
                        );
                        //props.setIsModalOpen(true);
                      } else if (text.name == "logout") {
                        localStorage.removeItem("user");
                        localStorage.removeItem("token");
                        localStorage.removeItem("esriToken");
                        localStorage.removeItem("redirect");
                        window.location.reload();
                      }
                      // console.log(e.currentTarget.name);
                    }}
                    name={text.name}
                    style={{
                      border: "none",
                      outline: "none",
                      boxShadow: "none",
                      backgroundColor: "transparent",
                      width: "100%",
                      height: "100%",
                      padding: "0",
                      margin: "0",
                    }}
                  >
                    <ListItem button>
                      <div style={{ textAlign: "center" }}>
                        <img
                          src={text.image}
                          alt={text.name}
                          style={{
                            width:
                              text.name === "zoomIn" || text.name === "zoomOut"
                                ? "25px"
                                : "20px",
                          }}
                        />
                      </div>
                      {openMapToolsDrawer ? (
                        <ListItemText
                          primary={t(text.title)}
                          className={`${!props.closeBoth ? "close" : ""}`}
                        />
                      ) : null}
                    </ListItem>

                    {openToolData &&
                      String(activeTool) === String(text.name) ? (
                      <AllTools
                        languageState={props.languageState}
                        mainData={props.mainData}
                        setPopupInfo={props.setPopupInfo}
                        popupInfo={props.popupInfo}
                        activeTool={text.name}
                        map={props.map}
                        closeToolsData={closeToolsData}
                        openToolsData={openToolsData}
                        openToolData={openToolData}
                      />
                    ) : null}
                  </Button>
                </div>
              </Tooltip>
            ))}
          </div>
        </>
      </Fade>

      {openServSearch ? (
        <ServicesSearch
          mainData={props.mainData}
          outerResultMenuShown={props.outerResultMenuShown}
          outerOpenResultMenu={props.outerOpenResultMenu}
          handleDrawerOpen={props.handleDrawerOpen}
          setFilteredResult={props.setFilteredResult}
          activeService={activeServiceItem}
          map={props.map}
          closeServiceSearch={closeServiceSearch}
        />
      ) : null}
    </MuiDrawer>
  );
}
