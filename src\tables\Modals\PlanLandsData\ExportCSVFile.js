import { DownloadOutlined } from "@ant-design/icons";
import { faFileCsv, faFileDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Tooltip } from "@mui/material";
import React from "react";
import ReactExport from "react-export-excel";
import { useTranslation } from "react-i18next";

const ExcelFile = ReactExport.ExcelFile;
const ExcelSheet = ReactExport.ExcelFile.ExcelSheet;
const ExcelColumn = ReactExport.ExcelFile.ExcelColumn;

function ExportCSV({
  dataSet,
  columns,
  labels,
  layerName,
  isForceClick,
  isPlanLandModal,
  isGeneralSearch,
  fileName,
}) {
  const [t] = useTranslation("common");
  const downloadExcelRef = React.useRef();
  React.useEffect(() => {
    if (dataSet?.length && isForceClick) {
      downloadExcelRef?.current?.click();
    }
  }, [dataSet]);
  return (
    <ExcelFile
      filename={fileName ? fileName : layerName + "CSV"}
      element={
        <span ref={downloadExcelRef} id="main-elem-for-export">
          {/* <Tooltip placement="topLeft" title={` استخراج ملف CSV `}> */}

          {isPlanLandModal ? (
            <Tooltip placement="leftTop" title={t("common:extractExcelFile")}>
              <DownloadOutlined style={{ fontSize: "2rem" }} />
            </Tooltip>
          ) : isGeneralSearch ? (
            <Tooltip placement="leftTop" title={t("extractExcelFile")}>
              <FontAwesomeIcon icon={faFileCsv} size="lg" />
            </Tooltip>
          ) : (
            t("extractExcelFile")
          )}
          {/* </Tooltip> */}
        </span>
      }
    >
      <ExcelSheet data={dataSet} name="AttributeTable">
        {labels.map((head, index) => (
          <ExcelColumn
            label={head}
            value={(col) => {
              return col[columns[index]] ? col[columns[index]] : t("without");
            }}
          />
        ))}
      </ExcelSheet>
    </ExcelFile>
  );
}

export default ExportCSV;
