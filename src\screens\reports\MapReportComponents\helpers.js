import Map from "@arcgis/core/Map";

import MapImageLayer from '@arcgis/core/layers/MapImageLayer';
import FeatureLayer from '@arcgis/core/layers/FeatureLayer';
import GraphicsLayer from '@arcgis/core/layers/GraphicsLayer';
import MapView from "@arcgis/core/views/MapView";
import Polygon from "@arcgis/core/geometry/Polygon";
import Point from "@arcgis/core/geometry/Point";
import Polyline from "@arcgis/core/geometry/Polyline";
// import * as projection from "@arcgis/core/geometry/projection";
import SpatialReference from "@arcgis/core/geometry/SpatialReference";
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine.js";


export const createLayer = (layer) => {
    var out = null;
    if (layer.type === "MapImageLayer") {
        out = new MapImageLayer(layer.url);
    }
    else if (layer.type === "GraphicsLayer") {
        out = new GraphicsLayer();
    }
    else if (layer.type === "FeatureLayer") {
        out = new FeatureLayer(layer.url, {
            opacity: 0,
            id: layer.id,
            mode: layer.mode,
            outFields: layer.outFields || ["*"]
        });
    }
    out.opacity = layer?.opacity || 1;
    out.id = layer?.id;
    out.featureCollection = layer?.featureCollection;
    return out;
};

export const createMap = (mapDiv, settings, layers) => {
        
        const map = new Map();
        
        let view = new MapView({
            container: mapDiv,
            map: map,
            settings,
            ui: {
              components: ["zoom","attribution"]
          },
        });
        layers.forEach(function (layer, key) {
            map.add(createLayer(layer));
        });
        return {map, view};
}

export function getCornersAndLength(
    resultGraphic,
    projection
  ) {
    let max = 0,
      min,
      maxPoint,
      minPoint;
    let wgsGeographic = new SpatialReference({ wkid: 4326 });
    let UTM39 = new SpatialReference(32639);
    let polygonInWithOriginCoords = new Polygon(
      resultGraphic.geometry,
      resultGraphic.geometry.spatialReference
    );
    let polygonInXYCoords = projection.project(
      polygonInWithOriginCoords,
      UTM39
    );
    let cornersArray = [], boundriesArray = [];
    for (let i = 0; i < resultGraphic.geometry.rings.length; i++) {
      for (let j = 0; j < resultGraphic.geometry.rings[i].length - 1; j++) {
        let ringPoint = resultGraphic.geometry.rings[i][j];
        let boundry = {
          attributes: {},
        };
        var UTM39fromPoint, UTM39toPoint, originFromPoint, originToPoint;
        originFromPoint = new Point(
          ringPoint[0],
          ringPoint[1],
          resultGraphic.geometry.spatialReference
        );
        UTM39fromPoint = projection.project(originFromPoint, UTM39);
        var GCSfromPoint = projection.project(UTM39fromPoint, wgsGeographic);

        boundry.attributes.FROM_CORNER = j + 1;
        cornersArray.push({
          geometry: UTM39fromPoint,
          attributes: {
            XUTM_COORD: UTM39fromPoint.x,
            YUTM_COORD: UTM39fromPoint.y,
            XGCS_COORD: GCSfromPoint.x.toString(),
            YGCS_COORD: GCSfromPoint.y.toString(),
            CORNER_NO: j + 1,
            PARCEL_SPATIAL_ID:
              resultGraphic.attributes.PARCEL_SPATIAL_ID,
          },
        });
        originToPoint = new Point(
          resultGraphic.geometry.rings[i][j + 1],
          resultGraphic.geometry.spatialReference
        );
        UTM39toPoint = projection.project(originToPoint, UTM39);

        boundry.attributes.TO_CORNER =
          j + 2 === resultGraphic.geometry.rings[i].length ? 1 : j + 2;

        boundry.attributes.BOUNDARY_NO = j + 1;
        boundry.attributes.PARCEL_SPATIAL_ID =
          resultGraphic.attributes.PARCEL_SPATIAL_ID;
        boundry.attributes.BOUNDARY_DIRECTION = null;

        if (UTM39fromPoint.x > max) {
          max = UTM39fromPoint.x;
          maxPoint = UTM39fromPoint;
        }

        if (!min || UTM39fromPoint.x < min) {
          min = UTM39fromPoint.x;
          minPoint = UTM39fromPoint;
        }

        if (UTM39toPoint.x > max) {
          max = UTM39toPoint.x;
          maxPoint = UTM39toPoint;
        }

        if (!min || UTM39toPoint.x < min) {
          min = UTM39toPoint.x;
          minPoint = UTM39toPoint;
        }

        var paths = [
          [UTM39fromPoint.x, UTM39fromPoint.y],
          [UTM39toPoint.x, UTM39toPoint.y],
        ];

        var polyLine = new Polyline({
          paths: [paths],
          spatialReference: UTM39,
        });

        boundry.geometry = polyLine;
        boundriesArray.push(boundry);
        // let projectedPolyLine = projection.project(polyLine,wgsGeographic);
        let lengthOfPloyLine = geometryEngine.distance(UTM39fromPoint,
            UTM39toPoint);
        // let lengthOfPloyLines = geodesicUtils.geodesicLengths(
        //     [projectedPolyLine]
        // )
        boundriesArray[j].attributes.BOUNDARY_LENGTH = lengthOfPloyLine;
        console.log(cornersArray, boundriesArray);
      }
    }
    //handle boundries' directions
    let boundriesWithDirect = [];
    let polygonCenterPoint = polygonInXYCoords.extent.center;
    boundriesArray.forEach((boundry, key) => {
      var centerPointofLine = boundry.geometry.extent.center;

      var diffrenceInXWithMaxPoint = Math.abs(centerPointofLine.x - maxPoint.x);
      var diffrenceWithPolygonCenterPoint = Math.abs(
        centerPointofLine.x - polygonCenterPoint.x
      );

      if (diffrenceInXWithMaxPoint < diffrenceWithPolygonCenterPoint) {
        // boundry.attributes.BOUNDARY_DIRECTION = 2       //east
        checkDirectionAndGetFinalBoundaryObj(
          2,
          boundriesArray,
          boundriesWithDirect,
          boundry,
          cornersArray,
        );
      } else {
        var diffrenceInXWithMinPoint = Math.abs(
          centerPointofLine.x - minPoint.x
        );
        if (diffrenceInXWithMinPoint < diffrenceWithPolygonCenterPoint) {
          // boundry.attributes.BOUNDARY_DIRECTION = 4       //west
          checkDirectionAndGetFinalBoundaryObj(
            4,
            boundriesArray,
            boundriesWithDirect,
            boundry,
            cornersArray,
          );
        } else if (centerPointofLine.y > polygonCenterPoint.y) {
          // boundry.attributes.BOUNDARY_DIRECTION = 3       //north
          checkDirectionAndGetFinalBoundaryObj(
            3,
            boundriesArray,
            boundriesWithDirect,
            boundry,
            cornersArray,
          );
        } else {
          // boundry.attributes.BOUNDARY_DIRECTION = 1       //south
          checkDirectionAndGetFinalBoundaryObj(
            1,
            boundriesArray,
            boundriesWithDirect,
            boundry,
            cornersArray,
          );
        }
      }
    });
    let boundsFinal = [];
    boundriesWithDirect.reduce((acc, currentValue) => {
      if (acc) {
        currentValue.attributes.BOUNDARY_NO = acc.BOUNDARY_NO + 1;
        currentValue.attributes.FROM_CORNER = acc.TO_CORNER;
        currentValue.attributes.TO_CORNER = acc.TO_CORNER + 1;
      }
      boundsFinal.push(currentValue);
      return {
        BOUNDARY_NO: currentValue.attributes.BOUNDARY_NO,
        FROM_CORNER: currentValue.attributes.FROM_CORNER,
        TO_CORNER: currentValue.attributes.TO_CORNER,
      };
    }, null);
    return {
      corners: cornersArray,
      boundaries: boundsFinal,
    };
  }
  export function checkDirectionAndGetFinalBoundaryObj(
    direction,
    oldBoundriesArray,
    newBoundriesArray,
    currentBoundary,
    cornersArray
  ) {
    // let direction = currentBoundary.attributes.BOUNDARY_DIRECTION;
    let prevBoundary = newBoundriesArray.find(
      (b) => b.attributes.BOUNDARY_DIRECTION == direction
    );
    if (prevBoundary) {
      let finalBoundary = {
        attributes: {},
      };
      let cornerPoint3 = cornersArray[currentBoundary.attributes.TO_CORNER - 1];
      finalBoundary.attributes.FROM_CORNER =
        prevBoundary.attributes.FROM_CORNER;
      finalBoundary.attributes.TO_CORNER = prevBoundary.attributes.TO_CORNER;
      finalBoundary.attributes.BOUNDARY_NO =
        prevBoundary.attributes.BOUNDARY_NO;
      finalBoundary.attributes.PARCEL_SPATIAL_ID =
        prevBoundary.attributes.PARCEL_SPATIAL_ID;
      finalBoundary.attributes.BOUNDARY_DIRECTION = direction;
      finalBoundary.attributes.BOUNDARY_LENGTH =
        parseFloat(prevBoundary.attributes.BOUNDARY_LENGTH) +
        parseFloat(currentBoundary.attributes.BOUNDARY_LENGTH);
      let paths = [...prevBoundary.geometry.paths[0]];
      console.log(paths);
      paths.push([cornerPoint3.geometry.x, cornerPoint3.geometry.y]);

      let polyLine = new Polyline({
        paths: [paths],
        spatialReference: prevBoundary.geometry.spatialReference,
      });
      finalBoundary.geometry = polyLine;
      newBoundriesArray.splice(
        newBoundriesArray.findIndex(
          (b) => b.attributes.BOUNDARY_DIRECTION == direction
        ),
        1,
        finalBoundary
      );
    } else {
      currentBoundary.attributes.BOUNDARY_DIRECTION = direction;
      newBoundriesArray.push(currentBoundary);
    }
  }

  export const boundariesEnum = {
    1: 'southBoundary',
    2: 'eastBoundary',
    3: 'northBoundary',
    4: 'westBoundary'
  }