import React from "react";

export const SideMenuContext = React.createContext();
// it is created to enable preserver form input values in sidemenu even if it is collapsed 
function SideMenuProvider({children}) {
    const [generalSearchState, setGeneralSearchState] = React.useState();       
    const [printState, setPrintState] = React.useState();       
    const [coordinatesSearchState, setCoordinatesSearchState] = React.useState();       
    const [bookmarkState, setBookmarkState] = React.useState();       
    const resetSideMenuContextData = () => {
        if (generalSearchState) setGeneralSearchState();
        if(coordinatesSearchState) setCoordinatesSearchState();
        if (printState) setPrintState();
        if (bookmarkState) setBookmarkState();
    }
  return <SideMenuContext.Provider
  value={{
    generalSearchState, setGeneralSearchState,
    coordinatesSearchState, setCoordinatesSearchState,
    printState, setPrintState,
    bookmarkState, setBookmarkState,
    resetSideMenuContextData,
}}
  >
      {children}
  </SideMenuContext.Provider>;
}

export default SideMenuProvider;
