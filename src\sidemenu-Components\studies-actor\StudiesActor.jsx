import { useState, useRef, useEffect, useCallback } from "react";
import { FaPlus, FaSearch, FaTrashAlt, FaEdit } from "react-icons/fa";
import { MdOutlineRemoveRedEye } from "react-icons/md";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import StudyDetails from "./StudyDetails";
import PathDetails from "./PathDetails";
import MonitoringCaseDetails from "./MonitoringCaseDetails";
import { CiImageOn } from "react-icons/ci";
import {
  Button,
  DatePicker,
  Input,
  message,
  Modal,
  Select,
  Upload,
} from "antd";
import gallery_image from "../../assets/images/sidemenu/ncw.jpeg";

import { RiArrowDropDownFill } from "react-icons/ri";
import i18next from "i18next";
import axios from "axios";
import { formatDayMonth } from "../../helper/common_func";
const userObject = JSON.parse(localStorage.getItem("user"));
const is_mission_member =
  !userObject.is_mission_reviewer && !userObject.is_mission_gis_approval;
const language = localStorage.getItem("lang");
const Card = ({ id, onClick, data, onDelete, activeTabId, onEdit }) => {
  const { t } = useTranslation("sidemenu");
  return (
    <div
      className={"generalSearchCard"}
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "8px 10px",
        cursor: "auto",
        margin: "10px 0",
        borderInlineStartColor:
          id === 1
            ? "#34A853"
            : id === 2
            ? "#B55433"
            : id === 3
            ? "#D9D9D9"
            : "#B55433",
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
        {/* data.id is the study number so please add it to the card */}
        <div
          style={{
            // position: "absolute",
            // top: "10px",
            // insetInlineEnd: "10px",
            width: "fit-content",
            padding: "2px 8px",
            borderRadius: "5px",
            background: "#b55433",
            color: "#fff",
            fontSize: "12px",
          }}
        >
          {data.id}
        </div>
        <div style={{ fontWeight: "600", fontSize: "16px" }}>{data.name}</div>
        {data.manager_name && (
          <div style={{ fontWeight: "400", fontSize: "12px" }}>
            {t("studiesActor.manager_label")} : {data.manager_name}
          </div>
        )}
        {(data.start_date || data.end_date) && (
          <div style={{ fontWeight: "400", fontSize: "12px" }}>
            {t("studiesActor.from_label")}{" "}
            {/* {formatDayMonth(data.start_date, language)}-{" "} */}
            {data.start_date}- {t("studiesActor.to_label")}{" "}
            {/* {formatDayMonth(data.end_date, language)} */}
            {data.end_date}
          </div>
        )}
      </div>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: "8px",
        }}
      >
        <MdOutlineRemoveRedEye
          style={{ cursor: "pointer" }}
          color={"#fff"}
          size={20}
          onClick={onClick}
        />
        {userObject.is_mission_creator && activeTabId == 5 && (
          <>
            {" "}
            <FaEdit
              style={{ cursor: "pointer" }}
              color={"#fff"}
              size={16}
              onClick={onEdit}
            />
            <FaTrashAlt
              style={{ cursor: "pointer" }}
              color={"#fff"}
              size={16}
              onClick={onDelete}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default function StudiesActor(props) {
  const { t } = useTranslation("sidemenu");
  const navigate = useNavigate();

  const montioringLayersNames = [
    "INCIDENT_SIGHTING",
    "SPECIE_SIGHTING",
    "IMPORTANT_SITE",
  ];

  const tabs = [
    {
      id: 5,
      name: t("my_missions"),
      for_creator: true,
    },
    {
      id: 1,
      name: t("current"),
      for_member: true,
    },
    {
      id: 2,
      name: t("under_review"),
      for_member: true,
      for_creator: true,
      for_reviewer: true,
    },
    {
      id: 6,
      name: t("under_approval"),
      for_member: true,
      for_creator: true,
      for_reviewer: true,
      for_gis_approval: true,
    },
    {
      id: 3,
      name: t("finished"),
      for_member: true,
      for_creator: true,
      for_reviewer: true,
      for_gis_approval: true,
    },
    {
      id: 4,
      name: t("shared"),
    },
  ];

  const screens = [
    {
      id: 1,
      screen: "studies",
    },
    {
      id: 2,
      screen: "study-details",
    },
    {
      id: 3,
      screen: "path-details",
    },
    {
      id: 4,
      screen: "monitoring-case-details",
    },
  ];

  const [activeTabId, setActiveTabId] = useState(null);
  const [activeScreen, setActiveScreen] = useState(screens[0]);
  const [activeItemData, setActiveItemData] = useState(undefined);
  const [pathDetailsData, setPathDetailsData] = useState();
  const [monitorCaseDetailsData, setMonitorCaseDetailsData] = useState();
  const [firstTabFlag, setFirstTabFlag] = useState(false);
  const [tabData, setTabData] = useState({
    1: { items: [], page: 1, hasMore: true },
    2: { items: [], page: 1, hasMore: true },
    3: { items: [], page: 1, hasMore: true },
    4: { items: [], page: 1, hasMore: true },
    5: { items: [], page: 0, hasMore: true },
    6: { items: [], page: 1, hasMore: true },
  });

  const [isLoading, setIsLoading] = useState(false);

  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  const containerRef = useRef(null);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  }, [activeScreen]);

  useEffect(() => {
    if (!firstTabFlag && tabs.length > 0) {
      const firstAvailableTab = tabs.find(
        (tab) =>
          (tab.for_member && is_mission_member) ||
          (tab.for_creator && userObject.is_mission_creator) ||
          (tab.for_reviewer && userObject.is_mission_reviewer) ||
          (tab.for_gis_approval && userObject.is_mission_gis_approval)
      );

      if (firstAvailableTab) {
        setFirstTabFlag(true);
        setActiveTabId(firstAvailableTab.id);
        setActiveScreen(screens[0]);
        console.log("first tab is", firstAvailableTab);
      }
    }
  }, [tabs, firstTabFlag, is_mission_member, userObject, screens]);

  const fetchTabData = async (tabId, reset = false) => {
    if (isLoading || (!tabData[tabId].hasMore && !reset)) return;

    setIsLoading(true);
    const page = reset ? (tabId === 5 ? 0 : 1) : tabData[tabId].page;

    const urlMap = {
      1: `${window.ApiUrl}fieldMissionsByUserRole/inprogress?pageNum=${page}&pageSize=30`,
      2: `${window.ApiUrl}fieldMissionsByUserRole/inreview?pageNum=${page}&pageSize=30`,
      3: `${window.ApiUrl}fieldMissionsByUserRole/finished?pageNum=${page}&pageSize=30`,
      4: `${window.ApiUrl}FieldMission/GetAll?page=${page}`,
      5: `${window.ApiUrl}FieldMission/GetAll?page=${page}&pageSize=30`,
      6: `${window.ApiUrl}fieldMissionsByUserRole/inapproval?pageNum=${page}&pageSize=30`,
    };

    let url = urlMap[tabId];

    const params = [];
    if (searchQuery && searchQuery.length >= 3) {
      params.push(`mission_name=${encodeURIComponent(searchQuery)}`);
    }
    if (startDate) {
      params.push(`start_date=${startDate}`);
    }
    if (endDate) {
      params.push(`end_date=${endDate}`);
    }
    if (params.length > 0) {
      url += `&${params.join("&")}`;
    }

    try {
      const response = await axios.get(url);

      setTabData((prev) => {
        let data =
          response.data && response.data.results
            ? [...response.data.results]
            : [...response.data];
        let dataItems = reset ? data : [...prev[tabId].items, ...data];
        return {
          ...prev,
          [tabId]: {
            items: dataItems,
            page: page + 1,
            hasMore: response.data.totalPages > page + 1,
          },
        };
      });
    } catch (error) {
      message.warning(t("ErrorRequest"));
    } finally {
      setIsLoading(false);
    }
  };

  //// pagination logic ////
  const loaderRef = useRef(null);
  const handleIntersection = useCallback(
    (entries) => {
      const entry = entries[0];
      if (entry.isIntersecting && !isLoading) {
        if (activeTabId && tabData[activeTabId].hasMore) {
          fetchTabData(activeTabId);
        }
      }
    },
    [activeTabId, tabData, isLoading, activeScreen]
  );

  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      threshold: 1.0,
    });

    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }

    return () => {
      if (loaderRef.current) {
        observer.unobserve(loaderRef.current);
      }
    };
  }, [handleIntersection]);

  useEffect(() => {
    if (activeTabId != null) {
      const initialPage = activeTabId === 5 ? 0 : 1;

      setTabData((prev) => ({
        ...prev,
        [activeTabId]: { items: [], page: initialPage, hasMore: true },
      }));

      fetchTabData(activeTabId, true);
    }
  }, [activeTabId, activeScreen]);

  useEffect(() => {
    if (activeTabId != null) {
      setTabData((prev) => ({
        ...prev,
        [activeTabId]: {
          items: [],
          page: activeTabId === 5 ? 0 : 1,
          hasMore: true,
        },
      }));
      fetchTabData(activeTabId, true);
    }
  }, [activeTabId, searchQuery, startDate, endDate]);

  //////// delete logic //////////////////////

  const [deleteModalVisible, setdeleteModalVisible] = useState(false);
  const [activeDeleteName, setActiveDeleteName] = useState(undefined);
  const [activeDeleteId, setActiveDeleteId] = useState(undefined);

  const submitMissionDeletion = async () => {
    const itemToDelete = tabData[activeTabId].items.find(
      (item) => item.id == activeDeleteId
    );
    console.log("item to delete is", itemToDelete);
    if (itemToDelete) {
      try {
        const result = await axios.delete(
          `${window.ApiUrl}FieldMission/${itemToDelete.id}`
        );
        setTabData((prevTabData) => {
          const updatedItems = prevTabData[activeTabId].items.filter(
            (i) => i.id !== itemToDelete.id
          );

          return {
            ...prevTabData,
            [activeTabId]: {
              ...prevTabData[activeTabId],
              items: updatedItems,
            },
          };
        });

        fileDeletedSuccessfully();
        message.success(t("DeletionSuccess"));
      } catch (error) {
        console.error("Error deleting item:", error);
        message.warning(t("ErrorOccurd"));
      }
    }
  };

  const showDelete = (item) => {
    console.log("item to delete", item);
    setActiveDeleteId(item.id);
    setActiveDeleteName(item.name);
    setdeleteModalVisible(true);
  };

  const fileDeletedSuccessfully = () => {
    setActiveDeleteName();
    setActiveDeleteId();
    setdeleteModalVisible(false);
  };
  return (
    <div
      ref={containerRef}
      className="study-actor"
      style={{
        padding: "10px",
        overflow: "auto",
        height: "calc(100vh - 80px)",
      }}
    >
      <div
        style={{ height: "1.5px", background: "#fff", marginBlock: "10px" }}
      />

      <div className="tabs">
        <div
          style={{
            display: "flex",
            gap: "15px",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div
            style={{
              color: "#B45333",
              display: "flex",
              gap: "7px",
              alignItems: "center",
              flex: "1",
              overflow: "auto",
              paddingBlock: "10px",
              marginBottom: "10px",
            }}
          >
            {activeTabId &&
              tabs.map((tab) => {
                if (
                  (tab.for_member && is_mission_member) ||
                  (tab.for_creator && userObject.is_mission_creator) ||
                  (tab.for_reviewer && userObject.is_mission_reviewer) ||
                  (tab.for_gis_approval && userObject.is_mission_gis_approval)
                ) {
                  return (
                    <div
                      key={tab.id}
                      style={{
                        backgroundColor:
                          activeTabId === tab.id ? "#B45333" : "#fff",
                        color: activeTabId === tab.id ? "#fff" : "#B45333",
                        padding: "5px 10px",
                        borderRadius: "20px",
                        cursor: "pointer",
                        flexShrink: "0",
                      }}
                      onClick={() => {
                        console.log("active id is", tab.id);
                        setActiveTabId(tab.id);
                        setActiveScreen(screens[0]);
                      }}
                    >
                      {tab.name}
                    </div>
                  );
                }
              })}
          </div>

          {userObject.is_mission_creator && (
            <div
              style={{
                backgroundColor: "#F4DFD9",
                width: "30px",
                height: "30px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: "50%",
                cursor: "pointer",
                marginTop: "-15px",
              }}
              onClick={() => {
                navigate("/add-studies-actor");
              }}
            >
              <FaPlus color={"#B45333"} size={16} />
            </div>
          )}
        </div>

        {/* start studies screen */}
        {activeScreen.id === 1 && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "10px",
              marginBottom: "10px",
            }}
          >
            {/* start filter and search */}
            {/* Replace the search input and date pickers in the JSX */}
            <div style={{ marginBottom: "20px" }}>
              <div style={{ position: "relative" }}>
                <label className="selectLabelStyle">
                  {t("studiesActor.search")}
                </label>
                <Input
                  placeholder={t("studiesActor.search_placeholder")}
                  className="search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <FaSearch
                  color="#fff"
                  size={18}
                  style={{
                    position: "absolute",
                    insetInlineStart: "15px",
                    top: i18next.language === "en" ? "46px" : "51px",
                  }}
                />
              </div>

              <div style={{ display: "flex", gap: "10px" }}>
                <div>
                  <label className="selectLabelStyle">
                    {t("studiesActor.from_label")}
                  </label>
                  <DatePicker
                    placeholder={t("studiesActor.from_placeholder")}
                    onChange={(date, dateString) => setStartDate(dateString)}
                    format="YYYY-MM-DD"
                  />
                </div>
                <div>
                  <label className="selectLabelStyle">
                    {t("studiesActor.to_label")}
                  </label>
                  <DatePicker
                    placeholder={t("studiesActor.to_placeholder")}
                    onChange={(date, dateString) => setEndDate(dateString)}
                    format="YYYY-MM-DD"
                  />
                </div>
              </div>
            </div>
            {/* end filter and search */}

            {activeTabId && tabData[activeTabId].items.length > 0 ? (
              <>
                {tabData[activeTabId].items.map((item, index) => (
                  <Card
                    key={index}
                    id={activeTabId}
                    data={item}
                    activeTabId={activeTabId}
                    onEdit={() => {
                      console.log("edit missioin logic", item);
                      navigate("/add-studies-actor", {
                        state: { studyData: item },
                      });
                    }}
                    onDelete={() => {
                      showDelete(item);
                    }}
                    onClick={() => {
                      console.log("active screen", screens[activeTabId]);
                      console.log("item clicked is ", item);
                      setActiveScreen(screens[1]);
                      setActiveItemData(item);
                    }}
                  />
                ))}
                <div ref={loaderRef} style={{ height: "40px" }} />
              </>
            ) : (
              <div
                style={{
                  color: "#fff",
                  fontSize: "14px",
                  textAlign: "center",
                }}
              >
                {t("studiesActor.no_data")}
              </div>
            )}
          </div>
        )}
        {/* end studies screen */}

        {/* start study details screen */}
        {activeScreen.id === 2 && activeItemData && (
          <>
            <StudyDetails
              setActiveScreen={setActiveScreen}
              screens={screens}
              data={activeItemData}
              language={language}
              map={props.map}
              setPathDetailsData={setPathDetailsData}
              setMonitorCaseDetailsData={setMonitorCaseDetailsData}
              userObject={userObject}
              is_mission_member={is_mission_member}
              activeTabId={activeTabId}
              montioringLayersNames={montioringLayersNames}
            />
          </>
        )}
        {/* end study details screen */}

        {/* start path details screen */}
        {activeScreen.id === 3 && (
          <>
            <PathDetails
              map={props.map}
              data={pathDetailsData}
              language={language}
            />
          </>
        )}
        {/* end path details screen */}

        {/* start monitoring case details screen */}
        {activeScreen.id === 4 && (
          <>
            <MonitoringCaseDetails
              map={props.map}
              data={monitorCaseDetailsData}
              language={language}
              montioringLayersNames={montioringLayersNames}
            />
          </>
        )}
        {/* end monitoring case details screen */}
      </div>

      {/* start modals */}

      <>
        <Modal
          title={t("deleteTripConfirmation") + activeDeleteName}
          centered
          visible={deleteModalVisible}
          onCancel={() => setdeleteModalVisible(false)}
          okText={t("yes")}
          cancelText={t("no")}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                submitMissionDeletion();
              }}
            >
              {t("yes", { ns: "common" })}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => setdeleteModalVisible(false)}
            >
              {t("no", { ns: "common" })}
            </Button>
          </div>
        </Modal>
      </>
    </div>
  );
  {
    /* end modals */
  }
}
