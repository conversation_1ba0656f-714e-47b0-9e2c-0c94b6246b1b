import React from "react";
import { useTranslation } from "react-i18next";
import { Spin } from "antd";
import { notificationMessage } from "../../../helper/utilsFunc";
import { getFeatureDomainName, getLayerId, queryTask } from "../../../helper/common_func";
import { PARCEL_LANDS_LAYER_NAME, PLAN_DATA_LAYER_NAME } from "../../../helper/constants";
import { Table } from "react-bootstrap";
import ExportCSV from "../../ExportsFeatures/ExportCSV";
function PlanLandsDataBody({ map, planNo, landbaseParcelData }) {
    const [shownData, setShownData] = React.useState([]);
    const [exportedData, setExportedData] = React.useState({
        dataSet:[], columns:[], labels:[], layerName:'',whereClause:''
    })
    const { t, i18n } = useTranslation("common", "layers");
    React.useEffect(() => {
        if (map) {
            //todo --> parse data to required format
            /**
             * [{
             * label:"", value:''}]
             */
            gewtPlanLandsData()

        }
        return () => {
            setShownData();
        }
    }, [])
    const gewtPlanLandsData = async (planNum) => {
        let promises = [];
        //total lands [الاراضي المخططة]
        promises.push(new Promise((resolve, reject) => {
            let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
            let queryParams = {
                url: window.mapUrl + "/" + layerID,
                notShowLoading: false,
                returnGeometry: false,
                where: `PLAN_SPATIAL_ID=${planNo}`,
                statistics: [
                    {
                        type: "count",
                        field: "USING_SYMBOL",
                        name: "count",
                    },
                ],
                //   groupByFields:["USING_SYMBOL"]
            };
            queryTask({
                ...queryParams, callbackResult: ({ features }) => {
                    resolve([{
                        label: "عدد الأراضي المخططة",
                        value: features[0]?.attributes?.COUNT || 0,
                        whereClause: `PLAN_SPATIAL_ID=${planNo}`,
                        id: 0
                    }])
                },
                callbackError: (err) => {
                    notificationMessage(t("common:retrievError"), 4);

                    reject(err);
                },
            })
        }));
        //resident lands [الاراضي السكنية]
        promises.push(new Promise((resolve, reject) => {
            let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
            let queryParams = {
                url: window.mapUrl + "/" + layerID,
                notShowLoading: false,
                returnGeometry: false,
                where: `PLAN_SPATIAL_ID=${planNo} AND PARCEL_MAIN_LUSE IN (10,30,40)`,       //10 resident, 30 general services, 40 marafiq
                statistics: [
                    {
                        type: "count",
                        field: "PARCEL_MAIN_LUSE",
                        name: "count",
                    },
                ],
                groupByFields: ["PARCEL_MAIN_LUSE"]
            };
            queryTask({
                ...queryParams, callbackResult: ({ features }) => {
                    resolve(features.map(i => {
                        return {
                            label: i.attributes?.PARCEL_MAIN_LUSE === 10 ? "عدد الأراضي السكنية" :
                                i.attributes?.PARCEL_MAIN_LUSE === 30 ? "عدد أراضي الخدمات العامة" : "عدد أراضي المرافق",
                            value: i?.attributes?.COUNT || 0,
                            whereClause: `PLAN_SPATIAL_ID=${planNo} AND PARCEL_MAIN_LUSE='${i.attributes?.PARCEL_MAIN_LUSE}'`,
                            id: i.attributes?.PARCEL_MAIN_LUSE === 10 ? 1 :
                                i.attributes?.PARCEL_MAIN_LUSE === 30 ? 2 : 3,
                        }
                    }))
                },
                callbackError: (err) => {
                    notificationMessage(t("common:retrievError"), 4);

                    reject(err);
                },
            })
        }));
        //commercial, investment 
        promises.push(new Promise((resolve, reject) => {
            let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
            let queryParams = {
                url: window.mapUrl + "/" + layerID,
                notShowLoading: false,
                returnGeometry: false,
                where: `PLAN_SPATIAL_ID=${planNo} AND (USING_SYMBOL LIKE 'ت%' OR USING_SYMBOL LIKE 'ت-ث%')`,       //10 resident, 30 general services, 40 marafiq
                statistics: [
                    {
                        type: "count",
                        field: "USING_SYMBOL",
                        name: "count",
                    },
                ],
                groupByFields: ["USING_SYMBOL"]
            };
            queryTask({
                ...queryParams, callbackResult: ({ features }) => {
                    let feats = features.reduce((cumulative, item) => {
                        if (!cumulative.length) {
                            cumulative.push(getNeededItemObj(item?.attributes))
                        } else {
                            let isExistBefore = cumulative.find(i => i.symbol === getSymbolCharacters(item?.attributes));
                            if (!isExistBefore) getNeededItemObj(item?.attributes);
                            else {
                                isExistBefore.value += item?.attributes?.COUNT || 0
                            }
                        }
                        return cumulative
                    }, [])
                    resolve(feats)
                },
                callbackError: (err) => {
                    notificationMessage(t("common:retrievError"), 4);

                    reject(err);
                },
            })
        }));
        // owner type [private - royal - sales]
        promises.push(new Promise((resolve, reject) => {
            let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
            let queryParams = {
                url: window.mapUrl + "/" + layerID,
                notShowLoading: false,
                returnGeometry: false,
                where: `PLAN_SPATIAL_ID=${planNo} AND OWNER_TYPE IS NOT Null`,       //1 private, 2 royal, 3 sales
                statistics: [
                    {
                        type: "count",
                        field: "OWNER_TYPE",
                        name: "count",
                    },
                ],
                groupByFields: ["OWNER_TYPE"]
            };
            queryTask({
                ...queryParams, callbackResult: ({ features }) => {
                    resolve(features.map(i => {
                        return {
                            label: i.attributes?.OWNER_TYPE === 1 ? t("common:allocatedLandsNum") :
                                i.attributes?.PARCEL_MAIN_LUSE === 2 ? t("common:royalLandsNum") : t("common:salesLandsNum"),
                            value: i?.attributes?.COUNT || 0,
                            whereClause: `PLAN_SPATIAL_ID=${planNo} AND OWNER_TYPE='${i.attributes?.OWNER_TYPE}'`,
                            id: i.attributes?.OWNER_TYPE === 1 ? 1 :
                                i.attributes?.OWNER_TYPE === 2 ? 2 : 3,
                        }
                    }))
                },
                callbackError: (err) => {
                    notificationMessage(t("common:retrievError"), 4);

                    reject(err);
                },
            })
        }));
        let results = await Promise.all(promises);
        console.log(results.flat().sort((a, b) => a.id - b.id));
        setShownData(results.flat().sort((a, b) => a.id - b.id))
    }

    //helpers
    function getNeededItemObj(item) {
        if (item.USING_SYMBOL.startsWith('ت-ث'))
            return ({
                label: "عدد الأراضي الاستثمارية",
                value: item.COUNT,
                whereClause: `PLAN_SPATIAL_ID=${planNo} AND USING_SYMBOL LIKE 'ت-ث%'`,
                symbol: "ت-ث",
                id: 5
            })
        else if (item.USING_SYMBOL.startsWith('ت') && !item.USING_SYMBOL.startsWith('ت-ث'))
            return ({
                label: "عدد الأراضي التجارية",
                value: item.COUNT,
                whereClause: `PLAN_SPATIAL_ID=${planNo} AND USING_SYMBOL LIKE 'ت%'`,
                symbol: "ت", id: 6
            })

    }
    function getSymbolCharacters(item) {
        if (item.USING_SYMBOL.startsWith('ت-ث'))
            return "ت-ث"

        else if (item.USING_SYMBOL.startsWith('ت') && !item.USING_SYMBOL.startsWith('ت-ث'))
            return "ت"

        else if (item.USING_SYMBOL.startsWith('خ'))
            return "خ"

        else if (item.USING_SYMBOL.startsWith('س'))
            return "س"

        else if (item.USING_SYMBOL.startsWith('م'))
            return "م"

    }
    function exportCSVFile(evt,item) {
        
    if (evt.target !== evt.currentTarget && evt.currentTarget.querySelector("#main-elem-for-export")) return;
        let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
        
        let queryParams = {
            url: window.mapUrl + "/" + layerID,
            notShowLoading: false,
            returnGeometry: false,
            where:item.whereClause
        };
        queryTask({
            ...queryParams,
            callbackResult: ({ features }) => {
                if (features.length)
                    getFeatureDomainName(features, layerID).then((feats) => {
                        let reqData = feats.map((f) => {
                            return { ...f.attributes };
                        });
                        
                        setExportedData({
                            dataSet:reqData,
                            columns:landbaseParcelData.fields?.map(i=>i.fieldName),
                            labels:landbaseParcelData.fields?.map(i=>i?.alias && (i?.alias)
                            .match('[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]')?
                            i.alias:t(`layers:${i.alias}`)),
                            layerName:item.label.slice(4),
                            whereClause:item.whereClause
                        })
                    });
            }
        })
    }
    return <div>
        {!shownData?.length ?<div style={{width:'100%', textAlign:'center'}}> <Spin /></div> :
            <Table className={[`${i18n.language === 'ar' ? 'rtl-direction' : 'ltr-direction'}`
                , "text-center"
            ].join(" ")} responsive striped bordered hover>

                <thead>
                    <tr>

                        <th>#</th>
                        <th>{t('common:count')}</th>
                        <th>{t('common:export')}</th>
                    </tr>
                </thead>
                <tbody>
                    {
                        shownData?.map((item, idx) => (
                            <tr key={idx + "plandata"}>
                                <td>{item?.label}</td>
                                <td>{item?.value}</td>
                                <td style={{cursor:'pointer'}} onClick={(e)=>exportCSVFile(e, item)}>{  exportedData.dataSet.length && item.whereClause===exportedData.whereClause?
                                <ExportCSV isForceClick={true} {...exportedData} />:t("common:extractExcelFile") }</td>
                            </tr>
                        ))
                    }
                </tbody>
            </Table>
        }
    </div>;
}

export default PlanLandsDataBody;
