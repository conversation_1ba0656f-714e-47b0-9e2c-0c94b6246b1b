import { useTranslation } from "react-i18next";
import bio_logo from "../../assets/images/interactive-map/bio_logo.svg";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { MdKeyboardArrowDown } from "react-icons/md";
import bio_1 from "../../assets/images/interactive-map/bio_1.svg";
import bio_2 from "../../assets/images/interactive-map/bio_2.svg";
import bio_3 from "../../assets/images/interactive-map/bio_3.svg";
import bio_4 from "../../assets/images/interactive-map/bio_4.svg";
import bio_5 from "../../assets/images/interactive-map/bio_5.svg";
import bio_6 from "../../assets/images/interactive-map/bio_6.svg";
import bio_7 from "../../assets/images/interactive-map/bio_7.svg";
import bio_8 from "../../assets/images/interactive-map/bio_8.svg";
import bio_9 from "../../assets/images/interactive-map/bio_9.svg";
import bio_10 from "../../assets/images/interactive-map/bio_10.svg";
import bio_11 from "../../assets/images/interactive-map/bio_11.svg";
import bio_12 from "../../assets/images/interactive-map/bio_12.svg";
import { useState } from "react";

export default function BiodiversityBox(props) {
  const { t } = useTranslation("layersmenu");

  const bio_list = [
    { name: "fish", src: bio_1, label: t("fish", { ns: "common" }) },
    { name: "dolphin", src: bio_2, label: t("dolphin", { ns: "common" }) },
    { name: "turtle", src: bio_3, label: t("turtle", { ns: "common" }) },
    { name: "Water_tree", src: bio_4, label: t("mangrove", { ns: "common" }) },
    { name: "coral", src: bio_5, label: t("coralreefs", { ns: "common" }) },
    { name: "water_grass", src: bio_6, label: t("seaweed", { ns: "common" }) },
    { name: "deer", src: bio_7, label: t("deer", { ns: "common" }) },
    { name: "lizard", src: bio_8, label: t("gecko", { ns: "common" }) },
    { name: "scorpion", src: bio_9, label: t("scorpion", { ns: "common" }) },
    { name: "bird", src: bio_10, label: t("bird", { ns: "common" }) },
    { name: "frog", src: bio_11, label: t("frog", { ns: "common" }) },
    { name: "tree", src: bio_12, label: t("tree", { ns: "common" }) },
  ];

  const [showBio, setShowBio] = useState(true);

  const handleSelectBio = (bio) => {
    if (
      props.generalSelectedItem &&
      props.generalSelectedItem.index === bio.index &&
      props.generalSelectedBox === "bio"
    ) {
      props.setGeneralSelectedItem(null);
      props.setGeneralSelectedBox("");
      props.updateGraphicState(undefined);

      return;
    }

    props.setGeneralSelectedItem(bio);
    props.setGeneralSelectedBox("bio");
    props.updateGraphicState({
      graphicsLayerName: "BioDiversity_InteractiveGraphicLayer",
      symbolName: bio.name,
    });
  };

  const handleRemoveAll = () => {
    props.setGeneralSelectedItem(null);
    props.setGeneralSelectedBox("");
    let graphicLayer = props.map.layers.items.find(
      (layer) => layer.id == "InteractiveMapGraphicLayer"
    );

    let graphicsToRemove = graphicLayer.graphics.items.filter(
      (graphic) => graphic._layerName == "BioDiversity_InteractiveGraphicLayer"
    );

    if (graphicsToRemove.length > 0) {
      props.history.current.undoStack.push(graphicsToRemove);
      graphicLayer.graphics.removeMany(graphicsToRemove);
    }
    props.updateGraphicState(undefined);
  };

  return (
    <div className="box">
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          color: "#fff",
          cursor: "pointer",
        }}
        onClick={() => setShowBio(!showBio)}
      >
        <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
          <img src={bio_logo} alt="bio logo" />
          <span>{t("Biodiversity")}</span>
        </div>

        <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
          <Button
            onClick={(e) => {
              e.stopPropagation();
              handleRemoveAll();
            }}
          >
            {t("clearAll")}
          </Button>
          <MdKeyboardArrowDown
            size={20}
            style={{
              // cursor: "pointer",
              transform: `rotate(${!showBio ? "180deg" : 0})`,
            }}
            // onClick={() => setShowBio(!showBio)}
          />
        </div>
      </div>

      {showBio && (
        <div
          className="images"
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(6, 1fr)",
            gap: "10px",
            marginTop: "10px",
          }}
        >
          {bio_list.map((bio, indx) => (
            <Tooltip title={bio.label} placement="bottom">
              <div
                key={indx}
                name={bio.name}
                className="image"
                style={{
                  background: props.generalSelectedItem
                    ? props.generalSelectedItem.index === indx &&
                      props.generalSelectedBox === "bio"
                      ? "#B55433"
                      : "transparent"
                    : "transparent",
                }}
                onClick={() => {
                  handleSelectBio({
                    name: bio.name,
                    src: bio.src,
                    index: indx,
                  });
                }}
              >
                <img
                  src={bio.src}
                  alt="tool"
                  style={{
                    filter: props.generalSelectedItem
                      ? props.generalSelectedItem.index === indx &&
                        props.generalSelectedBox === "bio"
                        ? "brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(2%) hue-rotate(264deg) brightness(105%) contrast(101%)"
                        : ""
                      : "",
                  }}
                />
              </div>
            </Tooltip>
          ))}
        </div>
      )}
    </div>
  );
}
