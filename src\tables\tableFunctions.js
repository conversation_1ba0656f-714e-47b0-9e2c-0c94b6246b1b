import { FaInfoCircle } from "react-icons/fa";

import { getFeatureDomainName, queryTask } from "../helper/common_func";

export const getStatisticsForFeatsLayer = async (
  layerID,
  layerDataConfig,
  where = "1=1"
) => {
  let promise = new Promise((resolve, reject) => {
    let queryParams = {
      url: window.mapUrl + "/" + layerID,
      notShowLoading: true,
      returnGeometry: false,
      statistics: layerDataConfig.layerMetadata.statistics || [
        {
          type: "count",
          field: "OBJECTID",
          name: "countResult",
        },
      ],
      where,
      // returnExecuteObject: true
    };
    queryTask({
      ...queryParams,
      callbackResult: ({ features }) => {
        if (features.length) {
          let result = { countPlusArea: features[0].attributes };
          result.countPlusArea.COUNT = result.countPlusArea.countResult;
          resolve(result);
        }
      },
      callbackError: (err) => {
        reject(err);
      },
    });
  });
  return promise;
};
export const getStatisticsForChart = async (
  layerID,
  layerDataConfig,
  where = "1=1"
) => {
  console.log("layer id", layerID);
  console.log("layer data config", layerDataConfig);
  let promise = new Promise((resolve, reject) => {
    if (layerDataConfig.layerMetadata.statFromDifferentLayer) {
      let queryParams = {
        url: window.mapUrl + "/" + layerID,
        notShowLoading: true,
        returnGeometry: false,
        outFields:
          layerDataConfig.layerMetadata.statFromDifferentLayer.groupByFields,
        // returnDistinctValues: true,
        groupByFields:
          layerDataConfig.layerMetadata.statFromDifferentLayer.groupByFields,
        statistics:
          layerDataConfig.layerMetadata.statFromDifferentLayer.statistics,
        where: where,
      };
      queryTask({
        ...queryParams,
        callbackResult: ({ features }) => {
          console.log("features are", features);
          if (features.length)
            getFeatureDomainName(features, layerID).then((data) => {
              console.log("data from get feature domain", data);
              resolve({ data });
            });
        },
        callbackError: (err) => {
          reject(err);
        },
      });
    } else {
      let queryParams = {
        url: window.mapUrl + "/" + layerID,
        notShowLoading: true,
        returnGeometry: false,
        // outFields:layerDataConfig.layerMetadata.groupByFields,
        returnDistinctValues: true,
        groupByFields: layerDataConfig.layerMetadata.groupByFields,
        statistics: layerDataConfig.layerMetadata.statistics,
        where: where,
      };
      queryTask({
        ...queryParams,
        callbackResult: ({ features }) => {
          if (features.length)
            getFeatureDomainName(features, layerID).then((data) => {
              resolve({ data });
            });
        },
        callbackError: (err) => {
          reject(err);
        },
      });
    }
  });
  return promise;
};

export const getDistictValuesForChart = async (
  layerID,
  layerDataConfig,
  where = "1=1"
) => {
  let promise = new Promise((resolve, reject) => {
    let queryParams = {
      url: window.mapUrl + "/" + layerID,
      notShowLoading: true,
      returnGeometry: false,
      outFields: layerDataConfig.layerMetadata.groupByFields,
      returnDistinctValues: true,
      // groupByFields:layerDataConfig.layerMetadata.groupByFields,
      // statistics:[...statisticsFields, ...layerDataConfig.layerMetadata.statistics] ,
      where: where + " AND PARCEL_SUB_LUSE IS NOT NULL",
    };
    queryTask({
      ...queryParams,
      callbackResult: ({ features }) => {
        if (features.length) {
          let dict = {};
          features
            .map((f) => f.attributes)
            .forEach((f) => {
              if (Object.keys(dict).includes(f.PARCEL_MAIN_LUSE)) {
                if (!dict[f.PARCEL_MAIN_LUSE].inculdes(f.PARCEL_SUB_LUSE)) {
                  dict[f.PARCEL_MAIN_LUSE].push(f.PARCEL_SUB_LUSE);
                }
              } else {
                if (typeof dict[f.PARCEL_MAIN_LUSE] === "object")
                  dict[f.PARCEL_MAIN_LUSE].push(f.PARCEL_SUB_LUSE);
                else dict[f.PARCEL_MAIN_LUSE] = [f.PARCEL_SUB_LUSE];
              }
            });
          resolve({ data: dict });
        }
      },
      callbackError: (err) => {
        reject(err);
      },
    });
  });
  return promise;
};
export const getSubtypes = (fieldName, map, layerName) => {
  let layer = map.__mapInfo.info.$layers.layers.find(
    (lay) => lay.name === layerName
  );
  let subtypeFieldName = layer?.subtypeFieldName;
  let layerSubtypes = layer?.subtypes || [];
  let hasSubtype = layerSubtypes.find(
    (sub) => sub.domains[fieldName]?.codedValues?.length
  );
  if (hasSubtype && subtypeFieldName) {
    let subTypeData = layerSubtypes
      .filter((sub) => sub.domains[fieldName]?.codedValues?.length)
      .map((sub) => {
        return {
          [subtypeFieldName]: sub.name,
          [subtypeFieldName + "_Code"]: sub.code,
          domains: sub.domains[fieldName].codedValues,
        };
      });
    return { subTypeData, subtypeFieldName };
  } else {
    return false;
  }
};
export const wrappedColRender = (text, record, settings) => {
  const {
    maxTextLen = 50,
    shownTextLen = 15,
    onClickHandler,
    tooltip,
  } = settings;
  if (!text || text.length <= maxTextLen) {
    return text;
  } else {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-around",
          alignItems: "center",
        }}
      >
        <p>{text.slice(0, shownTextLen)} ...</p>
        <span
          style={{
            marginRight: "0.5rem",
            marginBottom: "1rem",
            cursor: "pointer",
          }}
          title={tooltip}
          onClick={() => {
            onClickHandler(text);
          }}
        >
          <FaInfoCircle />
        </span>
      </div>
    );
  }
};
