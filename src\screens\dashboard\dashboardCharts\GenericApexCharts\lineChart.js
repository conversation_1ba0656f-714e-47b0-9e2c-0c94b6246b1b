import React, { useEffect, useState } from "react";
import ReactApex<PERSON>hart from "react-apexcharts";
import { useTranslation } from "react-i18next";
import isDeepEqual from 'fast-deep-equal/react'
import { getMinMax } from "../../../../helper/utilsFunc";

function LineChartComp(props) {
  const { t,i18n } = useTranslation("dashboard");
  const preparedChartDataRef = React.useRef();
  const [series, setSeries] = useState();
  const [labels, setLabels] = useState();
  const [options, setOptions] = useState();
  if (!isDeepEqual(preparedChartDataRef.current, props.preparedChartData)) {
    preparedChartDataRef.current = props.preparedChartData
  }
  useEffect(() => {
    let { preparedChartData, title, shownDataTypes } = props;
    if (preparedChartData?.length) {
      let hasArea = shownDataTypes.find((i) => i === "area");
      let max = getMinMax(preparedChartData.map((i) => i.count)).max;

      if (hasArea) {
        setSeries([
          {
            name: t("count"),
            data:
              max < 10000
                ? preparedChartData.map((i) => parseFloat(i.count).toFixed(2))
                : preparedChartData.map((i) =>
                  parseFloat(i.count / 1000).toFixed(2)
                ),
          },
          {
            name: t("area"),
            data: preparedChartData.map((i) =>
              parseFloat(i.area).toFixed(2)
            ),
          },
        ]);
      } else
        setSeries([
          {
            name: title,
            data:
              max < 10000
                ? preparedChartData.map((i) => parseFloat(i.count).toFixed(2))
                : preparedChartData.map((i) =>
                  parseFloat(i.count / 1000).toFixed(2)
                ),
          },
        ]);
      setLabels(
        preparedChartData.map((i) => {
          return {
            value: i.label,
            countInK: !(max < 10000),
          };
        })
      );
      setOptions({
        chart: {
          defaultLocale: i18n.language==='ar'?'ar':'en',
          locales: [{
            name: i18n.language==='ar'?'ar':'en',
            options: {
               toolbar: {
                exportToSVG:t('exportToSVG'),
                exportToPNG:t('exportToPNG'),
                exportToCSV:t('exportToCSV'),
                menu:t('menu'),
                selection: t('selection'),
                selectionZoom: t('selectionZoom'),
                download: t('downloadSVG'),
                zoomIn: t('zoomIn'),
                zoomOut: t('zoomOut'),
                pan: t('pan'),
                reset: t('reset'),
              }
            }
          }],
          // height: 250,
          // type: 'line',
          id: "areachart-2",
          toolbar: {
            show: false,
          },
        },

        legend: {
          show: false,
        },
        // title: {
        //   text: title,
        //   align: "center",
        //   style: {
        //     fontSize: "14",
        //     fontWeight: "bold",
        //     fontFamily: "NeoSansArabic",
        //   },
        // },
        // yaxis: {
        //   reversed: false,
        //   forceNiceScale: true,
        //   labels: {
        //     show: true,


        //   },
        // },
        tooltip: {
          enabled: true,
          x: {
            formatter: function (
              value,
              { series, seriesIndex, dataPointIndex, w }
            ) {
              console.log(value, { series, seriesIndex, dataPointIndex, w });
              return value;
            },
          },
          y: {
            formatter: function (
              value,
              { series, seriesIndex, dataPointIndex, w }
            ) {
              console.log(value, { series, seriesIndex, dataPointIndex, w });

              if (hasArea) {
                if (seriesIndex === 1) return `${t("area")}: ${value}`;
                else return `${t("count")}:${value}`;
              }
              return `${t("count")}:${value}`;
            },
            title: {
              formatter: (seriesName) => "",
            },
          },
        },
        yaxis: {
          forceNiceScale: true,

          tickPlacement: 'between',
          labels: {
            show: true,
            offsetX: -20,
          },
        },
        markers: {
          size: 4,
          color: "#28a745",
        },
        xaxis: {
          tickPlacement: 'between',
          categories: preparedChartData.map((i) => i.label),
          labels: {
            trim: true,
            show: false,
            rotate: 90,
            // rotateAlways: true,
          },
          title: {
            text: props.shownDataTypes.includes("area")
              ? max < 10000
                ? t("CountAndAreaWthKM")
                : t("CountKAndAreaWthKM")
              : max < 10000
                ? t("count")
                : `${t("count")} * 1000`,
          },
        },
      });
    } else {
      setSeries([]);
      setOptions({});
      setLabels([]);
    }

  }, [preparedChartDataRef.current]);
  useEffect(() => {
    return () => {
      setSeries();
      setOptions();
      setLabels();
    };
  }, []);
  useEffect(() => {
    if (props.sideTblTitle === props.title && series) {
      props.onClickTitle({
        data: series,
        title: props.title,
        labels,
      })
    }

  }, [series]);

  return (
    <div className="ape-chart">
      <div className="col text-center">

        <h6
        title={t("ClickHereForDetails")}
          onClick={() => {
            if ((options && series) && (Object.keys(options).length && series.length))
              props.onClickTitle({
                title: props.title,
                data: series,
                labels,
                hasArea: props.shownDataTypes.find((i) => i === "area"),
              })
            //todo: show a toast there is no data to show in else

          }
          }
          style={{
            fontFamily: "NeoSansArabic",
            textAlign: "center",
            fontWeight: "bold",
            cursor: "pointer",
          }}
        >
          {props.title}
        </h6>
        {/* <img src={props.mapLogoSrc} className="map-pointer" alt="map logo" onClick={handleHeatMap} /> */}
      </div>

      {(options && series) && (!Object.keys(options).length || !series?.length) ?
        <h4 className="text-center"> لا يوجد بيانات للعرض</h4>
        : (options && series) && (Object.keys(options).length && series.length) ? <ReactApexChart
          options={options}
          series={series}
          type="line"
          // width={'100%'}
          height={150}
        /> : null}
    </div>
  );
}

export default LineChartComp;
