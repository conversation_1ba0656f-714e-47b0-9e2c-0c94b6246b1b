import React, { useContext, useEffect, useState } from "react";
import { Form, Input, Row, Col, Button, message, Modal } from "antd";
import axios from "axios";
import { Container } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import { CiBookmark } from "react-icons/ci";

import Extent from "@arcgis/core/geometry/Extent";
import { faEdit, faTrash } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { convertToArabic } from "../helper/common_func";

export default function BookMark(props) {
  const { t } = useTranslation("common");
  const [formValues, setFormValues] = useState({
    bookmark: "",
  });
  const [bookmarks, setBookmarks] = useState([]);

  const [editModalVisible, setEditModalVisible] = useState(false);
  const [activeBookMark, setActiveBookMark] = useState(-1);
  const User = localStorage.user ? JSON.parse(localStorage.user) : null;
  const handleChangeInput = (e) => {
    setFormValues({ ...formValues, [e.target.name]: e.target.value });
  };

  useEffect(() => {
    if (User) {
      axios
        .get(window.ApiUrl + "Bookmark/GetAll?filter_key=user_id&q=" + User.id)
        .then(({ data }) => {
          setBookmarks(data.results);
        });
    } else {
      setBookmarks(
        localStorage.bookmarks ? JSON.parse(localStorage.bookmarks) : []
      );
    }
  }, []);

  const zoomToBookmark = (bookmark, index) => {
    setActiveBookMark(index);

    if (typeof bookmark.extent == "string") {
      bookmark.extent = JSON.parse(bookmark.extent);
    }

    props.map.view.goTo(
      new Extent({
        xmin: bookmark.extent.xmin,
        ymin: bookmark.extent.ymin,
        xmax: bookmark.extent.xmax,
        ymax: bookmark.extent.ymax,
        spatialReference: bookmark.extent.spatialReference,
      })
    );
  };

  const addBookMark = (e) => {
    if (
      formValues.bookmark.length === 0 ||
      formValues.bookmark.trim().length === 0
    ) {
      message.warning(t("EmptyBookmarkName"));
      return;
    }

    if (formValues.bookmark.length < 3 || formValues.bookmark.length > 50) {
      message.warning(t("textLengthValidationError"));
      return;
    }
    let temp = [...bookmarks];
    let isExisted = temp.find((b) => b.title == formValues.bookmark);
    if (isExisted) {
      message.warning(t("BookmarkNameDuplication"));
      return;
    }

    if (User) {
      axios
        .post(window.ApiUrl + "api/Bookmark", {
          title: formValues.bookmark,
          extent: JSON.stringify(props.map.view.extent),
          user_id: User.id,
        })
        .then(({ data }) => {
          temp.unshift(data);
          setBookmarks(temp);
        });
    } else {
      temp.unshift({
        title: formValues.bookmark,
        extent: JSON.stringify(props.map.view.extent),
      });
      localStorage.bookmarks = JSON.stringify(temp);
      setBookmarks(temp);
    }

    setFormValues({ ...formValues, bookmark: "" });
    message.success(t("saveLocationToBookMark"));
  };

  const showEdit = (bookmark, index) => {
    zoomToBookmark(bookmark, index);
    setFormValues({ ...formValues, editName: bookmark.title });
    setEditModalVisible(true);
  };

  const afterEditModal = () => {
    if (
      formValues.editName.length === 0 ||
      formValues.editName.trim().length === 0
    ) {
      message.warning(t("EmptyBookmarkName"));
      return;
    }

    if (formValues.editName.length < 3 || formValues.editName.length > 50) {
      message.warning(t("textLengthValidationError"));
      return;
    }

    let otherBookmarks = bookmarks.filter(
      (b) => b.title !== bookmarks[activeBookMark].title
    );
    let isExisted = otherBookmarks.find((b) => b.title == formValues.editName);
    if (isExisted) {
      message.warning(t("BookmarkNameDuplication"));
      return;
    }
    bookmarks[activeBookMark].title = formValues.editName;
    setEditModalVisible(false);

    if (User) {
      axios
        .put(window.ApiUrl + "api/Bookmark/" + bookmarks[activeBookMark].id, {
          ...bookmarks[activeBookMark],
        })
        .then(() => {});
    } else {
      let temp = [...bookmarks];
      temp[activeBookMark].title = formValues.editName;
      localStorage.bookmarks = JSON.stringify(temp);
    }

    message.success(t("edited"));
  };
  useEffect(() => {
    let delayDebounceFn;
    if (formValues.bookmark) {
      delayDebounceFn = setTimeout(() => {
        setFormValues({
          ...formValues,
          bookmark: convertToArabic(formValues.bookmark),
        });
      }, 1000);
    }
    return () => delayDebounceFn && clearTimeout(delayDebounceFn);
  }, [formValues.bookmark]);

  //// delete ////

  const submitDeleteBookMark = () => {
    console.log("active delete bookmark", activeDeleteBookMark);
    let arr = [...bookmarks];
    arr.splice(activeDeleteBookMark.index, 1);

    if (User) {
      axios
        .delete(window.ApiUrl + "Bookmark/" + activeDeleteBookMark.bookMark.id)
        .then(() => {});
    } else {
      localStorage.bookmarks = JSON.stringify(arr);
    }

    message.success(t("removed"));

    setBookmarks(arr);
    setdeleteModalVisible(false);
  };

  const [deleteModalVisible, setdeleteModalVisible] = useState(false);
  const [activeDeleteBookMark, setActiveDeleteBookMark] = useState();

  const deleteBookMarkHandler = (bookMark) => {
    console.log("bookmark to delete", bookMark);
    setActiveDeleteBookMark(bookMark);
    setdeleteModalVisible(true);
  };
  return (
    <div className="coordinates mb-4 mt-2 add-bookmark">
      <Container>
        <Form className="GeneralForm" layout="vertical" name="validate_other">
          <Row className="bookmarkRow">
            <Form.Item
              label={t("bookmark")}
              rules={[
                {
                  message: t("enterBookmark"),
                  required: true,
                },
              ]}
              style={{ width: "100%", marginBottom: "0" }}
            >
              <Input
                type="text"
                name="bookmark"
                onChange={handleChangeInput}
                value={formValues.bookmark}
                placeholder={t("bookmark")}
              />
            </Form.Item>
            {/* </Col> */}

            <button
              className="SearchBtn mt-3 bookmark_searchBtn"
              size="large"
              htmlType="submit"
              block
              onClick={addBookMark}
            >
              {t("sidemenu:add")}
            </button>
            {/* </Col> */}
          </Row>
        </Form>
        <div
          style={{
            height: "calc(100vh - 230px)",
            overflow: "auto",
            padding: "5px",
          }}
        >
          {bookmarks &&
            bookmarks.length > 0 &&
            bookmarks.map((b, index) => (
              <div
                className="generalSearchCard"
                style={{ padding: "15px 5px", marginInline: "0" }}
              >
                <Row
                  className="bookmarkRowEnglish"
                  style={{ alignItems: "center" }}
                >
                  <Col span={3}>
                    <CiBookmark
                      className="starMark"
                      style={{ color: "#fff" }}
                    />
                  </Col>
                  <Col
                    onClick={() => zoomToBookmark(b, index)}
                    span={14}
                    style={{
                      whiteSpace: "break-spaces",
                      wordBreak: "break-word",
                      paddingRight: "5px",
                      color: "#fff",
                    }}
                  >
                    <p style={{ margin: "10px" }}>{b.title}</p>
                  </Col>
                  <Col span={7} className="bookmarkColRight">
                    <FontAwesomeIcon
                      icon={faEdit}
                      className="mx-2"
                      style={{ color: "#fff" }}
                      onClick={() => showEdit(b, index)}
                    />
                    <FontAwesomeIcon
                      style={{ color: "#fff" }}
                      icon={faTrash}
                      className="mx-2"
                      onClick={() =>
                        deleteBookMarkHandler({ bookMark: b, index: index })
                      }
                    />
                  </Col>
                </Row>
              </div>
            ))}
        </div>
      </Container>
      <Modal
        className="edit-book-mark-modal"
        title={t("editBookmarkName")}
        centered
        visible={editModalVisible}
        //onOk={() => afterEditModal()}
        onCancel={() => setEditModalVisible(false)}
        okText={t("edit")}
        cancelText={t("cancel")}
      >
        <Input
          name="editName"
          onChange={handleChangeInput}
          value={formValues.editName}
          placeholder={t("bookmarkName")}
        />

        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            justifyContent: "end",
          }}
        >
          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => {
              afterEditModal();
            }}
          >
            {t("edit")}
          </Button>

          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => setEditModalVisible(false)}
          >
            {t("close")}
          </Button>
        </div>
      </Modal>

      <Modal
        title={
          t("bookMarDeletionConfirmation") +
          activeDeleteBookMark?.bookMark?.title
        }
        centered
        visible={deleteModalVisible}
        onCancel={() => setdeleteModalVisible(false)}
        okText={t("yes")}
        cancelText={t("no")}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            justifyContent: "end",
          }}
        >
          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => {
              submitDeleteBookMark();
            }}
          >
            {t("yes")}
          </Button>

          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => setdeleteModalVisible(false)}
          >
            {t("no")}
          </Button>
        </div>
      </Modal>
    </div>
  );
}
