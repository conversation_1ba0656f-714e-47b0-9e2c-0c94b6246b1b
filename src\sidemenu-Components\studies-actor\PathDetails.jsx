import {
  Button,
  Input,
  Modal,
  Select,
  Switch,
  Tooltip,
  Upload,
  message,
} from "antd";
import mapImg from "../../assets/images/study-actor/gis_map-route.svg";
import { RiArrowDropDownFill } from "react-icons/ri";
import { SketchPicker } from "react-color";
import { useEffect, useState, useRef } from "react";
import { MdKeyboardArrowDown } from "react-icons/md";
import axios from "axios";
import monitor_modal_image from "../../assets/images/study-actor/monitor_modal.svg";
import tools_logo from "../../assets/images/interactive-map/tools_logo.svg";
import tool_1 from "../../assets/images/interactive-map/tool_1.svg";
import tool_2 from "../../assets/images/interactive-map/tool_2.svg";
import tool_3 from "../../assets/images/interactive-map/tool_3.svg";
import tool_4 from "../../assets/images/interactive-map/tool_4.svg";
import tool_5 from "../../assets/images/interactive-map/tool_5.svg";
import tool_7 from "../../assets/images/interactive-map/tool_7.svg";
import tool_8 from "../../assets/images/interactive-map/tool_8.svg";
import tool_9 from "../../assets/images/interactive-map/tool_9.svg";
import tool_10 from "../../assets/images/interactive-map/tool_10.svg";
import tool_11 from "../../assets/images/interactive-map/tool_11.svg";
import tool_12 from "../../assets/images/interactive-map/tool_12.svg";
import tool_13 from "../../assets/images/interactive-map/topbar_move.svg";
import tool_14 from "../../assets/images/interactive-map/topbar_undo.svg";
import tool_15 from "../../assets/images/interactive-map/topbar_redo.svg";
import tool_16 from "../../assets/images/sidemenu/delete.svg";
import draw_text from "../../assets/images/interactive-map/drawText.svg";
import gallery_icon from "../../assets/images/study-actor/gallery_thumbnail.svg";
import time_icon from "../../assets/images/study-actor/mingcute_time-line.svg";
import weather_icon from "../../assets/images/study-actor/typcn_weather-partly-sunny.svg";
import height_icon from "../../assets/images/study-actor/height.svg";
import spead_icon from "../../assets/images/study-actor/material-symbols_speed-outline.svg";
import kml_icon from "../../assets/images/study-actor/gis_kml-file.svg";
import gallery_image from "../../assets/images/sidemenu/ncw.jpeg";
import { useTranslation } from "react-i18next";
import { FaTrashAlt } from "react-icons/fa";
import { CiImageOn } from "react-icons/ci";
import Sketch from "@arcgis/core/widgets/Sketch";
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine";
import Color from "@arcgis/core/Color";
import Graphic from "@arcgis/core/Graphic";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import {
  convertTimeStampToDate,
  getLayerId,
  getMapInfo,
  getTimeDifferenceFromTimeStamp,
  studyTotalTimeString,
  zoomToFeatures,
  showLoading,
  executeGPTool,
  showGeneralDataTable,
  drawText,
} from "../../helper/common_func";
import { getUniqeID } from "../../helper/utilsFunc";
import ReactImageGallery from "react-image-gallery";

const { Option } = Select;

export default function PathDetails(props) {
  const pathsLayer = props.map.findLayerById("PathsGraphicLayer");
  const pathsDrawingsLayer = props.map.findLayerById(
    "PathDrawingsGraphicsLayer"
  );
  const monitoringCasesLayer = props.map.findLayerById(
    "MonitoringCasesGraphicLayer"
  );
  const [showTools, setShowTools] = useState(true);
  const [importedFiles, setImportedFiles] = useState([]);
  const [currentLoadedFileID, setCurrentLoadedFileID] = useState(null);
  const fileInputRef = useRef(null);
  const { t } = useTranslation("sidemenu");

  const tools = [
    {
      name: "straight-line",
      src: tool_1,
      label: t("straight_line", { ns: "common" }),
    },
    { name: "free-hand", src: tool_2, label: t("free_hand", { ns: "common" }) },
    {
      name: "line-with-arrows",
      src: tool_3,
      label: t("line_with_arrows", { ns: "common" }),
    },
    { name: "rectangle", src: tool_4, label: t("rectangle", { ns: "common" }) },
    { name: "circle", src: tool_5, label: t("circle", { ns: "common" }) },
    {
      name: "line-right-arrow",
      src: tool_8,
      label: t("line_right_arrow", { ns: "common" }),
    },
    {
      name: "line-left-arrow",
      src: tool_9,
      label: t("line_left_arrow", { ns: "common" }),
    },
    {
      name: "location_marker",
      src: tool_10,
      label: t("location", { ns: "common" }),
    },
    {
      name: "pan",
      src: tool_13,
      label: t("pan", { ns: "common" }),
    },
    {
      name: "undo",
      src: tool_14,
      label: t("undo", { ns: "common" }),
    },
    {
      name: "redo",
      src: tool_15,
      label: t("redo", { ns: "common" }),
    },
    {
      name: "delete",
      src: tool_16,
      label: t("delete", { ns: "common" }),
    },
    {
      name: "pointText",
      src: draw_text,
      label: t("point_text", { ns: "common" }),
    },
  ];

  const [isGalleryModalOpen, setIsGalleryModalOpen] = useState(false);
  const [isMonitorCaseModalOpen, setIsMonitorCaseModalOpen] = useState(false);
  const [monitoringCases, setMonitoringCases] = useState(
    props.data.monitoringCases
  );

  const drawPath = async () => {
    await getMapInfo(window.mobileAppUrl).then(async (mapInfo) => {
      let pathId = getLayerId(mapInfo, "SIGHTING_TOURPATH");
      const sourceFeatureLayer = new FeatureLayer({
        url: `${window.mobileAppUrl}/${pathId} `,
      });
      if (!pathsLayer || !(pathsLayer instanceof GraphicsLayer)) {
        console.error(
          `GraphicsLayer with id "PathsGraphicLayer" not found or is not a GraphicsLayer.`
        );
        return;
      }
      const pathGraphic = props.data.pathData;
      await sourceFeatureLayer.load();
      const renderer = sourceFeatureLayer.renderer;
      pathGraphic.symbol = renderer.symbol;
      pathsLayer.add(pathGraphic);
      zoomToFeatures([pathGraphic], props.map);
    });
  };

  const drawMonitoringCases = async () => {
    await getMapInfo(window.mobileAppUrl).then(async (mapInfo) => {
      let pathId = getLayerId(mapInfo, "INCIDENT_SIGHTING");
      const sourceFeatureLayer = new FeatureLayer({
        url: `${window.mobileAppUrl}/${pathId} `,
      });
      if (
        !monitoringCasesLayer ||
        !(monitoringCasesLayer instanceof GraphicsLayer)
      ) {
        console.error(
          `GraphicsLayer with id "MonitoringCasesGraphicLayer" not found or is not a GraphicsLayer.`
        );
        return;
      }
      monitoringCasesLayer.removeAll();
      await sourceFeatureLayer.load();
      const renderer = sourceFeatureLayer.renderer;
      if (monitoringCases.length > 0) {
        monitoringCases.forEach((monitorCase) => {
          monitorCase.feature.symbol = renderer.symbol;
          monitoringCasesLayer.add(monitorCase.feature);
        });
      }
    });
  };

  const [clickedGraphics, setClickedGraphics] = useState([]);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    const drawData = async () => {
      await fetchPathDrawings();
      if (monitoringCases.length > 0) {
        await drawMonitoringCases();
      }
    };
    drawData();
    const clickHandler = props.map.view.on("click", async (event) => {
      try {
        const hitTestResult = await props.map.view.hitTest(event);
        const hitGraphics = hitTestResult.results
          .filter(
            (result) =>
              result.graphic && result.graphic.layer === monitoringCasesLayer
          )
          .map((result) => result.graphic);
        if (hitGraphics.length > 0) {
          setClickedGraphics(hitGraphics);
          setActiveTab(0);
          showMonitorCaseModal();
          console.log(`hit Graphics`, hitGraphics);
          hitGraphics.forEach((graphic, index) => {
            console.log(`Graphic ${index + 1} attributes:`, graphic.attributes);
          });
        } else {
          setClickedGraphics([]);
          setIsMonitorCaseModalOpen(false);
        }
      } catch (error) {
        console.error("Hit test error:", error);
      }
    });
    return () => {
      clickHandler.remove();
      pathsLayer.graphics.removeAll();
      monitoringCasesLayer.graphics.removeAll();
      pathsDrawingsLayer.graphics.removeAll();
    };
  }, [monitoringCases]);

  const showGalleryModal = () => {
    setIsGalleryModalOpen(true);
  };

  const handleGalleryModalOk = () => {
    setIsGalleryModalOpen(false);
  };

  const handleCancelGalleryModal = () => {
    setIsGalleryModalOpen(false);
  };

  const handleTogglePathSwitch = (checked) => {
    let currentPathGraphic = pathsLayer.graphics.items.find(
      (graphic) => graphic.uid == props.data.pathData.uid
    );
    if (currentPathGraphic) {
      currentPathGraphic.visible = !currentPathGraphic.visible;
    }
  };

  const handleToggleOtherPathsSwitch = (checked) => {
    let otherPathsGraphics = pathsLayer.graphics.items.filter(
      (graphic) => graphic.uid != props.data.pathData.uid
    );
    if (otherPathsGraphics.length > 0) {
      otherPathsGraphics.forEach(
        (graphic) => (graphic.visible = !graphic.visible)
      );
    }
  };

  const exportKML = async () => {
    if (!props.data.pathData) {
      message.error(
        t("studiesActor.no_data_to_export", {
          default: "لا توجد بيانات للتصدير",
        })
      );
      return;
    }
    showLoading(true);
    try {
      const mapInfo = await getMapInfo(window.mobileAppUrl);
      const pathLayerName = "SIGHTING_TOURPATH";
      const monitoringLayerName = "INCIDENT_SIGHTING";
      const pathObjectId = props.data.pathData.attributes.OBJECTID;
      const pathWhere = `OBJECTID = ${pathObjectId}`;
      let filters = [{ ["NCWEGDB.GIS." + pathLayerName]: pathWhere }];
      if (monitoringCases && monitoringCases.length > 0) {
        const monitoringObjectIds = monitoringCases
          .map((item) => item.feature.attributes.OBJECTID)
          .join(",");
        const monitoringWhere = `OBJECTID IN (${monitoringObjectIds})`;
        filters.push({
          ["NCWEGDB.GIS." + monitoringLayerName]: monitoringWhere,
        });
      }
      let userObj = localStorage.getItem("user");
      if (userObj) userObj = JSON.parse(userObj);
      let params = {
        Filters: filters,
        FileType: "kml",
        token: userObj ? userObj.esriToken : "",
      };
      executeGPTool(
        window.exportFeaturesGPUrl,
        params,
        (result) => {
          if (result) {
            let anchor = document.createElement("a");
            anchor.href = window.filesURL + result;
            document.body.appendChild(anchor);
            anchor.click();
            document.body.removeChild(anchor);
          }
          showLoading(false);
        },
        (err) => {
          console.error("Error exporting KML:", err);
          message.error(err);
          showLoading(false);
        },
        "output_value",
        "execute",
        userObj?.esriToken
      );
    } catch (error) {
      console.error("Error during KML export:", error);
      message.error(error);
      showLoading(false);
    }
  };

  const handleKMLImport = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const fileExt = file.name.split(".").pop().toLowerCase();
    if (fileExt !== "kml" && fileExt !== "kmz") {
      message.error(t("invalidKMLFileType"));
      return;
    }
    const formData = new FormData();
    formData.append(`file[${file.name}]`, file);
    showLoading(true);
    axios
      .post(`${window.ApiUrl}uploadMultifiles`, formData)
      .then((res) => {
        let filePath = res.data[0].data;
        let fileNamesList = res.data[0].PrevFileName.split("/");
        let fileName = fileNamesList[fileNamesList.length - 1];
        axios.post(`${window.ApiUrl}/ImportedFile`, {
          name: fileName,
          path: filePath,
        });
        let params = {
          KML_File_Name: filePath,
        };
        let userObj = localStorage.getItem("user");
        if (userObj) userObj = JSON.parse(userObj);
        executeGPTool(
          `${window.kmlToJSONGPUrl}?token=${userObj ? userObj.esriToken : ""}`,
          params,
          (result) => {
            showLoading(false);
            let fileID = getUniqeID();
            setImportedFiles((prev) => {
              let newFileData = {
                name: fileName,
                path: filePath,
                result,
                fileID,
              };
              setCurrentLoadedFileID(() => {
                loadImportedFile(newFileData);
                return fileID;
              });
              return [...prev, newFileData];
            });
            message.success(t("studiesActor.importSuccess"));
          },
          (error) => {
            console.error("Error importing KML:", error);
            showLoading(false);
            message.error(t("showFileToMapError"));
          },
          "output_value",
          "submitJob",
          userObj?.esriToken
        );
      })
      .catch((err) => {
        console.error("Error uploading KML:", err);
        showLoading(false);
        message.error(t("uploadFilesError"));
      });
    fileInputRef.current.value = "";
  };

  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const loadImportedFile = (file) => {
    showGeneralDataTable({
      type: "importGisFile",
      data: file.result,
      map: props.map,
      uploadFileType: "kmz",
      show: true,
      fileID: file.fileID,
    });
    setCurrentLoadedFileID(file.fileID);
  };

  const deleteImportedFile = (fileID) => {
    setImportedFiles((prev) => prev.filter((file) => file.fileID !== fileID));
    if (currentLoadedFileID === fileID) {
      showGeneralDataTable({ show: false });
      setCurrentLoadedFileID(null);
    }
  };

  const showMonitorCaseModal = () => {
    setIsMonitorCaseModalOpen(true);
  };

  const handleMonitorCaseModalOk = () => {
    setIsMonitorCaseModalOpen(false);
    setClickedGraphics([]);
    setActiveTab(0);
  };

  const handleMonitorCaseModalCancel = () => {
    setIsMonitorCaseModalOpen(false);
    setClickedGraphics([]);
    setActiveTab(0);
  };

  const getLayerType = (attributes) => {
    if ("INCIDENT_ID" in attributes) return "incident";
    if ("SIGHTING_ID" in attributes) return "sighting";
    if ("IMPORTANT_SITE_ID" in attributes) return "site";
    return "unknown";
  };

  const getModalFields = (graphic) => {
    const attributes = graphic?.attributes || {};
    const layerType = getLayerType(attributes);
    const dateFormatter = (timestamp) =>
      timestamp
        ? new Date(timestamp).toLocaleDateString("ar-SA", {
            calendar: "gregory",
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          })
        : "غير متوفر";
    switch (layerType) {
      case "incident":
        return {
          plantName:
            attributes.INCIDENT_SUBJECT ||
            attributes.INCIDENT_DESCRIPTION?.substring(0, 50) ||
            "غير محدد",
          date: dateFormatter(attributes.INCIDENT_DATE),
          area: attributes.AR_REGION || "غير محدد",
          reserve: attributes.AR_PROTECTED_AREA_NAME || "غير محدد",
          observationType: attributes.AR_INCIDENT_TYPE || "غير محدد",
          reportName:
            attributes.INCIDENT_SUBJECT ||
            `بلاغ ${attributes.INCIDENT_ID || ""}`,
          observationDescription: attributes.INCIDENT_DESCRIPTION || "غير محدد",
          observationCount: "1",
          importance: attributes.AR_INCIDENT_PRIORITY || "غير محدد",
          photos: attributes.PHOTOS
            ? attributes.PHOTOS.split(",").map(
                (photoPath) => `${window.filesURL}${photoPath}`
              )
            : [],
          videos: attributes.VIDEOS
            ? attributes.VIDEOS.split(",").map(
                (videoPath) => `${window.filesURL}${videoPath}`
              )
            : [],
        };
      case "sighting":
        return {
          plantName:
            attributes.AR_SPECIE || attributes.SPECIE_ALIAS || "غير محدد",
          date: dateFormatter(attributes.SIGHTING_DATE),
          area: attributes.AR_REGION || "غير محدد",
          reserve: attributes.AR_PROTECTED_AREA_NAME || "غير محدد",
          observationType: attributes.AR_SIGHTING_METHOD || "غير محدد",
          reportName: `${attributes.AR_SPECIE || "مشاهدة"} ${
            attributes.SIGHTING_ID || ""
          }`,
          observationDescription: attributes.AR_NOTES || "غير محدد",
          observationCount: attributes.SAME_SPECIES_COUNT?.toString() || "1",
          importance: attributes.AR_SPECIE_STATUS || "غير محدد",
          photos: attributes.PHOTOS
            ? attributes.PHOTOS.split(",").map(
                (photoPath) => `${window.filesURL}${photoPath}`
              )
            : [],
          videos: attributes.VIDEOS
            ? attributes.VIDEOS.split(",").map(
                (videoPath) => `${window.filesURL}${videoPath}`
              )
            : [],
        };
      case "site":
        return {
          plantName: attributes.AR_SITE_NAME || "غير محدد",
          date: dateFormatter(
            attributes.SIGHTING_DATE || attributes.CREATED_DATE
          ),
          area: attributes.AR_REGION || "غير محدد",
          reserve: attributes.AR_PROTECTED_AREA_NAME || "غير محدد",
          observationType: attributes.AR_SITE_TYPE || "غير محدد",
          reportName:
            attributes.AR_SITE_NAME ||
            `موقع ${attributes.IMPORTANT_SITE_ID || ""}`,
          observationDescription: attributes.AR_SITE_DESCRIPTION || "غير محدد",
          observationCount: "1",
          importance: attributes.AR_IMPORTANCE_LEVEL || "غير محدد",
          photos: attributes.PHOTOS
            ? attributes.PHOTOS.split(",").map(
                (photoPath) => `${window.filesURL}${photoPath}`
              )
            : [],
          videos: attributes.VIDEOS
            ? attributes.VIDEOS.split(",").map(
                (videoPath) => `${window.filesURL}${videoPath}`
              )
            : [],
        };
      default:
        return {
          plantName: "غير محدد",
          date: "غير متوفر",
          area: "غير محدد",
          reserve: "غير محدد",
          observationType: "غير محدد",
          reportName: "غير محدد",
          observationDescription: "غير محدد",
          observationCount: "1",
          importance: "غير محدد",
          photos: [],
          videos: [],
        };
    }
  };

  const handleDeleteMedia = async (monitorCase, mediaUrl, mediaType) => {
    showLoading(true);
    try {
      const feature = monitorCase.feature;
      const layerName = monitorCase.ownedLayerName;
      const objectId = feature.attributes.OBJECTID;

      const mapInfo = await getMapInfo(window.mobileAppEditUrl);
      const layerId = getLayerId(mapInfo, layerName);
      const featureLayer = new FeatureLayer({
        url: `${window.mobileAppEditUrl}/${layerId}`,
      });

      await featureLayer.load();

      const attributeName = mediaType === "image" ? "PHOTOS" : "VIDEOS";
      const currentMedia = feature.attributes[attributeName] || "";
      const mediaArray = currentMedia ? currentMedia.split(",") : [];
      const mediaPath = mediaUrl.replace(window.filesURL, "");
      const updatedMediaArray = mediaArray.filter((path) => path !== mediaPath);
      const updatedMediaString = updatedMediaArray.join(",");

      const updatedFeature = {
        attributes: {
          OBJECTID: objectId,
          [attributeName]: updatedMediaString || null,
        },
      };

      const editResult = await featureLayer.applyEdits({
        updateFeatures: [updatedFeature],
      });

      console.log("updatedFeature ", updatedFeature);
      console.log("edit result", editResult);

      if (
        editResult.updateFeatureResults.length > 0 &&
        !editResult.updateFeatureResults[0].error
      ) {
        setMonitoringCases((prevCases) => {
          console.log("prevCases", prevCases);
          let updatedData = prevCases.map((caseItem) =>
            caseItem.feature.attributes.OBJECTID === objectId
              ? {
                  ...caseItem,
                  feature: {
                    ...caseItem.feature,
                    attributes: {
                      ...caseItem.feature.attributes,
                      [attributeName]: updatedMediaString,
                    },
                  },
                }
              : caseItem
          );
          console.log("prevCases", updatedData);
        });

        setClickedGraphics((prevGraphics) =>
          prevGraphics.map((graphic) =>
            graphic.attributes.OBJECTID === objectId
              ? {
                  ...graphic,
                  attributes: {
                    ...graphic.attributes,
                    [attributeName]: updatedMediaString,
                  },
                }
              : graphic
          )
        );
        message.success(t("studiesActor.image_deleted"));
      }
    } catch (error) {
      console.error("Error deleting media:", error);
      message.error(t("studiesActor.delete_error"));
    } finally {
      showLoading(false);
    }
  };

  const activeGraphic = clickedGraphics[activeTab];
  const modalFields = getModalFields(activeGraphic);
  const mediaItems = [...modalFields.photos, ...modalFields.videos];

  ///////////// sketch logic /////////////////
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedColor, setSelectedColor] = useState("#F5A623");
  const [selectedTool, setSelectedTool] = useState("");
  const componentRef = useRef({});
  const { current: sketch } = componentRef;
  const activeSketchName = useRef({});
  const textPointRef = useRef(null);
  const [textModalVisible, setTextModalVisible] = useState(false);
  const [warningMessage, setWarningMessage] = useState(false);
  const activeDelete = useRef(false);
  const activeIconName = useRef();
  const undoStack = useRef([]);
  const redoStack = useRef([]);
  const MAX_HISTORY_SIZE = 50;

  const addToUndoStack = (action) => {
    undoStack.current.push(action);
    if (undoStack.current.length > MAX_HISTORY_SIZE) {
      undoStack.current.shift();
    }
  };

  const cloneGraphic = (graphic) => {
    if (!graphic || !graphic.geometry || !graphic.symbol) return null;
    const cloned = {
      uid: graphic.uid,
      geometry: graphic.geometry.clone(),
      symbol: graphic.symbol.clone(),
      attributes: graphic.attributes ? { ...graphic.attributes } : null,
    };
    return cloned;
  };

  useEffect(() => {
    sketch.current = new Sketch({
      layer: pathsDrawingsLayer,
      view: props.map.view,
    });
    sketch.current.on("create", (event) => {
      if (event.state == "complete" && activeDelete.current) {
        let intersectedGeometries = [];
        if (pathsDrawingsLayer.graphics.items.length > 0) {
          for (
            let index = 0;
            index < pathsDrawingsLayer.graphics.items.length;
            index++
          ) {
            let graphic = pathsDrawingsLayer.graphics.items[index];
            if (event.graphic && graphic.visible) {
              if (
                geometryEngine.intersects(
                  event.graphic.geometry,
                  graphic.geometry
                )
              ) {
                intersectedGeometries.push(graphic);
              }
            }
          }
        }
        if (intersectedGeometries.length > 0) {
          let deletedGraphics = [];
          for (let index = 0; index < intersectedGeometries.length; index++) {
            intersectedGeometries[index].visible = false;
            const clonedGraphic = cloneGraphic(intersectedGeometries[index]);
            deletedGraphics.push(clonedGraphic);
          }
          addToUndoStack({
            type: "delete",
            graphics: deletedGraphics,
          });
        }
        pathsDrawingsLayer.remove(event.graphic);
      } else if (event.state == "complete" && !activeDelete.current) {
        if (activeSketchName.current == "pointText") {
          pathsDrawingsLayer.remove(event.graphic);
          if (textPointRef.current.input.value) {
            const textGraphic = drawText(
              event.graphic,
              textPointRef.current.input.value,
              props.map,
              "PathDrawingsGraphicsLayer",
              15,
              3,
              3,
              event.graphic.symbol.color
            );
            if (textGraphic) {
              addToUndoStack({
                type: "create",
                graphics: [cloneGraphic(textGraphic)],
              });
            }
          }
        } else if (activeSketchName.current == "icon") {
          const drawnIconGraphic = drawIcon(
            event.graphic,
            activeIconName.current
          );
          if (drawnIconGraphic) {
            addToUndoStack({
              type: "create",
              graphics: [cloneGraphic(drawnIconGraphic)],
            });
          }
        } else {
          addToUndoStack({
            type: "create",
            graphics: [cloneGraphic(event.graphic)],
          });
        }
      }
    });
    return () => {
      try {
        if (sketch.current) {
          cancelDraw();
          sketch.current.destroy();
          sketch.current = null;
        }
      } catch (error) {
        console.error("Error during sketch cleanup:", error);
      }
    };
  }, []);

  useEffect(() => {
    handleSelectTool(selectedTool);
  }, [selectedColor]);

  const drawPolyLine = () => {
    activeSketchName.current = "polyline";
    sketch.current.viewModel.polylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      style: "solid",
    };
    sketch.current.create("polyline");
  };

  const drawLineWithTwoArrows = () => {
    activeSketchName.current = "polylineWithTwoArrows";
    const arrowPolylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      marker: {
        type: "simple-line",
        color: selectedColor,
        width: 2,
      },
      style: "solid",
      cap: "butt",
      marker: {
        style: "arrow",
        placement: "begin-end",
      },
    };
    sketch.current.viewModel.polylineSymbol = arrowPolylineSymbol;
    sketch.current.create("polyline");
  };

  const drawLineWithRightArrow = () => {
    activeSketchName.current = "polylineWithRighArrow";
    const arrowPolylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      marker: {
        type: "simple-line",
        color: selectedColor,
        width: 2,
      },
      style: "solid",
      cap: "butt",
      marker: {
        style: "arrow",
        placement: "end",
      },
    };
    sketch.current.viewModel.polylineSymbol = arrowPolylineSymbol;
    sketch.current.create("polyline");
  };

  const drawLineWithLeftArrow = () => {
    activeSketchName.current = "polylineWithLeftArrow";
    const arrowPolylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      marker: {
        type: "simple-line",
        color: selectedColor,
        width: 2,
      },
      style: "solid",
      cap: "butt",
      marker: {
        style: "arrow",
        placement: "begin",
      },
    };
    sketch.current.viewModel.polylineSymbol = arrowPolylineSymbol;
    sketch.current.create("polyline");
  };

  const drawRectangle = () => {
    activeSketchName.current = "rectangle";
    let color = Color.fromHex(selectedColor);
    color.a = 0.15;
    sketch.current.viewModel.polygonSymbol = {
      type: "simple-fill",
      color: color,
      outline: {
        color: selectedColor,
        width: 2,
      },
    };
    sketch.current.create("rectangle");
  };

  const drawCircle = () => {
    activeSketchName.current = "circle";
    let color = Color.fromHex(selectedColor);
    color.a = 0.1;
    sketch.current.viewModel.polygonSymbol = {
      type: "simple-fill",
      color: color,
      outline: {
        color: selectedColor,
        width: 2,
      },
    };
    sketch.current.create("circle");
  };

  const drawPolyLineFreeHand = () => {
    activeSketchName.current = "freeHand";
    sketch.current.viewModel.polylineSymbol = {
      type: "simple-line",
      color: selectedColor,
      width: 2,
      style: "solid",
    };
    sketch.current.create("polyline", { mode: "freehand" });
  };

  const drawTextHandle = () => {
    if (
      textPointRef.current.input.value === "" ||
      textPointRef.current.input.value.trim().length == 0
    ) {
      setWarningMessage(true);
    } else {
      setWarningMessage(false);
      sketch.current.viewModel.pointSymbol = {
        type: "simple-marker",
        style: "circle",
        color: selectedColor,
        size: "6px",
        outline: {
          color: selectedColor,
          width: 2,
        },
      };
      sketch.current.create("point");
      activeSketchName.current = "pointText";
      setTextModalVisible(false);
    }
  };

  const cancelDraw = () => {
    sketch.current.cancel();
  };

  const handleDeleteIcon = () => {
    activeDelete.current = true;
    cancelDraw();
    sketch.current.viewModel.polygonSymbol = {
      type: "simple-fill",
      color: [0, 0, 0, 0.1],
      outline: {
        color: [0, 0, 0],
        width: 2,
      },
    };
    sketch.current.create("rectangle");
  };

  const handleUndo = () => {
    if (undoStack.current.length === 0) {
      console.log("Nothing to undo");
      message.warning(t("undoWarningMessage"));
      return;
    }
    const action = undoStack.current.pop();
    if (action.type === "create") {
      action.graphics.forEach((graphicData) => {
        const actualGraphic = pathsDrawingsLayer.graphics.items.find(
          (graphic) => graphic.uid === graphicData.uid
        );
        if (actualGraphic) {
          actualGraphic.visible = false;
        }
      });
      redoStack.current.push({
        type: "create",
        graphics: action.graphics,
      });
    } else if (action.type === "delete") {
      action.graphics.forEach((graphicData) => {
        const actualGraphic = pathsDrawingsLayer.graphics.items.find(
          (graphic) => graphic.uid === graphicData.uid
        );
        if (actualGraphic) {
          actualGraphic.visible = true;
        }
      });
      redoStack.current.push({
        type: "delete",
        graphics: action.graphics,
      });
    }
  };

  const handleRedo = () => {
    if (redoStack.current.length === 0) {
      console.log("Nothing to redo");
      message.warning(t("redoWarningMessage"));
      return;
    }
    const action = redoStack.current.pop();
    if (action.type === "create") {
      action.graphics.forEach((graphicData) => {
        const actualGraphic = pathsDrawingsLayer.graphics.items.find(
          (graphic) => graphic.uid === graphicData.uid
        );
        if (actualGraphic) {
          actualGraphic.visible = true;
        }
      });
      undoStack.current.push({
        type: "create",
        graphics: action.graphics,
      });
    } else if (action.type === "delete") {
      action.graphics.forEach((graphicData) => {
        const actualGraphic = pathsDrawingsLayer.graphics.items.find(
          (graphic) => graphic.uid === graphicData.uid
        );
        if (actualGraphic) {
          actualGraphic.visible = false;
        }
      });
      undoStack.current.push({
        type: "delete",
        graphics: action.graphics,
      });
    }
  };

  const handleDrawIcon = (iconName) => {
    activeSketchName.current = "icon";
    activeIconName.current = iconName;
    sketch.current.viewModel.pointSymbol = {
      type: "simple-marker",
      style: "circle",
      color: selectedColor,
      size: "6px",
      outline: {
        color: selectedColor,
        width: 2,
      },
    };
    sketch.current.create("point");
  };

  const drawIcon = (pointGraphic, iconName) => {
    if (!props.map.view) return;
    const iconUrl = require(`../../assets/images/study-actor/pics/${iconName}.png`);
    const pictureMarkerSymbol = {
      type: "picture-marker",
      url: iconUrl.default,
      width: "32px",
      height: "32px",
    };
    pointGraphic.symbol = pictureMarkerSymbol;
    pathsDrawingsLayer.graphics.add(pointGraphic);
    return pointGraphic;
  };

  const handleSelectTool = (tool) => {
    if (tool) {
      setSelectedTool(tool.name);
      if (tool.name != "delete") {
        activeDelete.current = false;
        cancelDraw();
      }
      switch (tool.name) {
        case "straight-line":
          drawPolyLine();
          break;
        case "free-hand":
          drawPolyLineFreeHand();
          break;
        case "line-with-arrows":
          drawLineWithTwoArrows();
          break;
        case "circle":
          drawCircle();
          break;
        case "rectangle":
          drawRectangle();
          break;
        case "line-right-arrow":
          drawLineWithRightArrow();
          break;
        case "line-left-arrow":
          drawLineWithLeftArrow();
          break;
        case "pan":
          cancelDraw();
          break;
        case "undo":
          handleUndo();
          break;
        case "redo":
          handleRedo();
          break;
        case "delete":
          handleDeleteIcon();
          break;
        case "pointText":
          if (!showColorPicker) {
            showTextInput();
          }
          break;
      }
      if (tool.name == "location_marker") {
        handleDrawIcon(tool.name);
      }
    }
  };

  const showTextInput = () => {
    setTextModalVisible(true);
  };

  const postDrawnGraphicsData = async (formData) => {
    await axios
      .post(`${window.ApiUrl}/generate_graphics`, formData)
      .then((response) => {
        console.log("response", response);
        message.success(t("studiesActor.graphics_saved"));
      });
  };

  const handleSaveDrawnGraphics = async () => {
    let graphics = pathsDrawingsLayer.graphics;
    let visibleGraphics;
    if (graphics.length > 0) {
      visibleGraphics = graphics.filter((graphic) => graphic.visible);
      if (visibleGraphics.items.length == 0) {
        message.warning("studiesActor.no_graphics_to_save");
      } else {
        let stringfiedGraphics = visibleGraphics.map((graphic) =>
          graphic.toJSON()
        );
        stringfiedGraphics = JSON.stringify(stringfiedGraphics);
        const formData = {
          gid: `${props.data.pathData.attributes.APP_USER_NAME}_${props.data.pathData.attributes.CREATED_DATE}`,
          layer_name: "SIGHTING_TOURPATH",
          graphics: stringfiedGraphics,
        };
        await postDrawnGraphicsData(formData);
      }
    } else {
      message.warning("studiesActor.no_graphics_to_save");
    }
  };

  const fetchPathDrawings = async () => {
    let gid = `${props.data.pathData.attributes.APP_USER_NAME}_${props.data.pathData.attributes.CREATED_DATE}`;
    let user_id = JSON.parse(localStorage.getItem("user")).id;
    await axios
      .get(
        `${window.ApiUrl}/user-graphics?gid=${gid}&layer_name=SIGHTING_TOURPATH&user_id=${user_id}`
      )
      .then((response) => {
        let graphicsArray = response.data?.graphics;
        if (graphicsArray) {
          graphicsArray = JSON.parse(graphicsArray);
          if (graphicsArray.length > 0) {
            graphicsArray.forEach((graphicData) => {
              const graphic = Graphic.fromJSON(graphicData);
              pathsDrawingsLayer.add(graphic);
            });
          }
        }
      });
  };

  const galleryItems = monitoringCases
    .filter(
      (item) => item.feature.attributes.PHOTOS || item.feature.attributes.VIDEOS
    )
    .flatMap((item) => {
      const photos = item.feature.attributes.PHOTOS
        ? item.feature.attributes.PHOTOS.split(",").map((photoPath) => ({
            original: `${window.filesURL}${photoPath}`,
            thumbnail: `${window.filesURL}${photoPath}`,
            type: "image",
          }))
        : [];
      const videos = item.feature.attributes.VIDEOS
        ? item.feature.attributes.VIDEOS.split(",").map((videoPath) => ({
            original: `${window.filesURL}${videoPath}`,
            thumbnail: gallery_image, // Use a placeholder thumbnail for videos
            type: "video",
          }))
        : [];
      return [...photos, ...videos];
    });

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "10px",
      }}
    >
      {/* start path */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "start",
          }}
        >
          <img src={mapImg} alt="" />
          <div
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}
          >
            {props.data.pathData.attributes.PATH_NAME && (
              <div style={{ fontWeight: "400", fontSize: "16px" }}>
                {props.data.pathData.attributes.PATH_NAME}
              </div>
            )}
            {props.data.pathData.attributes.CREATE_DATE && (
              <div style={{ fontWeight: "400", fontSize: "12px" }}>
                {t("studiesActor.date", {
                  date: convertTimeStampToDate(
                    props.data.pathData.attributes.CREATE_DATE,
                    false
                  ),
                })}
              </div>
            )}
            {props.data.pathData.attributes.DISTANCE_KM && (
              <div style={{ fontWeight: "400", fontSize: "12px" }}>
                {t("studiesActor.distance", {
                  distance: `${props.data.pathData.attributes.DISTANCE_KM.toFixed(
                    2
                  )} ${props.language == "ar" ? "كم" : "KM"}`,
                })}
              </div>
            )}
          </div>
        </div>
      </div>
      {/* end path */}

      {/* start gallery */}
      {
        <div
          className="generalSearchCardWithoutHover"
          style={{
            padding: "16px",
            display: "flex",
            flexDirection: "column",
            gap: "10px",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              alignItems: "start",
            }}
          >
            <img src={gallery_icon} alt="" />
            <div style={{ fontWeight: "700", fontSize: "15px" }}>
              {t("studiesActor.gallery")}
            </div>
          </div>
          <div
            style={{
              display: "flex",
              gap: "10px",
              flexWrap: "wrap",
            }}
          >
            {monitoringCases.length > 0 ? (
              (() => {
                const mediaItems = monitoringCases
                  .filter(
                    (item) =>
                      item.feature.attributes.PHOTOS ||
                      item.feature.attributes.VIDEOS
                  )
                  .flatMap((item, index) => {
                    const photos = item.feature.attributes.PHOTOS
                      ? item.feature.attributes.PHOTOS.split(",").map(
                          (photoPath) => ({
                            url: `${window.filesURL}${photoPath}`,
                            type: "image",
                          })
                        )
                      : [];
                    const videos = item.feature.attributes.VIDEOS
                      ? item.feature.attributes.VIDEOS.split(",").map(
                          (videoPath) => ({
                            url: `${window.filesURL}${videoPath}`,
                            type: "video",
                          })
                        )
                      : [];
                    return [...photos, ...videos];
                  });

                return mediaItems.length > 0 ? (
                  mediaItems.map((media, mediaIndex) => {
                    const monitorCase = monitoringCases.find((item) =>
                      media.type === "image"
                        ? item.feature.attributes.PHOTOS?.includes(
                            media.url.replace(window.filesURL, "")
                          )
                        : item.feature.attributes.VIDEOS?.includes(
                            media.url.replace(window.filesURL, "")
                          )
                    );
                    return (
                      <div
                        key={`media-${mediaIndex}`}
                        style={{ position: "relative", flex: "0 0 80px" }}
                        onClick={showGalleryModal}
                      >
                        {media.type === "image" ? (
                          <img
                            src={media.url}
                            alt=""
                            style={{
                              width: "80px",
                              height: "50px",
                              objectFit: "cover",
                              borderRadius: "8px",
                            }}
                          />
                        ) : (
                          <video
                            src={media.url}
                            style={{
                              width: "80px",
                              height: "50px",
                              objectFit: "cover",
                              borderRadius: "8px",
                            }}
                            muted
                          />
                        )}
                        <FaTrashAlt
                          style={{
                            position: "absolute",
                            top: "5px",
                            left: "5px",
                            color: "#fff",
                            cursor: "pointer",
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (monitorCase) {
                              handleDeleteMedia(
                                monitorCase,
                                media.url,
                                media.type
                              );
                            }
                          }}
                        />
                      </div>
                    );
                  })
                ) : (
                  <div
                    style={{
                      color: "#fff",
                      fontSize: "14px",
                      textAlign: "center",
                    }}
                  >
                    {t("studiesActor.no_data")}
                  </div>
                );
              })()
            ) : (
              <div
                style={{
                  color: "#fff",
                  fontSize: "14px",
                  textAlign: "center",
                }}
              >
                {t("studiesActor.no_data")}
              </div>
            )}
          </div>
        </div>
      }
      {/* end gallery */}

      {/* start time */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "start",
          }}
        >
          <img src={time_icon} alt="" />
          <div
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <div style={{ fontWeight: "700", fontSize: "15px" }}>
              {t("studiesActor.time")}
            </div>
            <div style={{ fontWeight: "400", fontSize: "12px" }}>
              {props.data.pathData.attributes.END_TIME &&
              props.data.pathData.attributes.START_TIME
                ? `${t("studiesActor.total_time")} ${studyTotalTimeString(
                    props.data.pathData.attributes.END_TIME,
                    props.data.pathData.attributes.START_TIME,
                    props.language
                  )}`
                : `${t("studiesActor.no_time_data")}`}
            </div>
          </div>
        </div>
      </div>
      {/* end time */}

      {/* start weather */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "start",
          }}
        >
          <img src={weather_icon} alt="" />
          <div
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <div style={{ fontWeight: "700", fontSize: "15px" }}>
              {t("studiesActor.weather")}
            </div>
            <div style={{ fontWeight: "400", fontSize: "12px" }}>
              {props.data.pathData.attributes.MAX_TEMPERATURE
                ? `${t("studiesActor.max_temp")} ${
                    props.data.pathData.attributes.MAX_TEMPERATURE
                  }`
                : `${t("studiesActor.no_tempreature_data")}`}
            </div>
            <div style={{ fontWeight: "400", fontSize: "12px" }}>
              {props.data.pathData.attributes.MAX_WIND_SPEED
                ? `${t("studiesActor.max_wind")} ${
                    props.data.pathData.attributes.MAX_WIND_SPEED
                  }`
                : `${t("studiesActor.no_wind_data")}`}
            </div>
          </div>
        </div>
      </div>
      {/* end weather */}

      {/* start height */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
          display: "flex",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "start",
          }}
        >
          <img src={height_icon} alt="" />
          <div
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <div style={{ fontWeight: "700", fontSize: "15px" }}>
              {t("studiesActor.height")}
            </div>
            <div style={{ fontWeight: "400", fontSize: "12px" }}>
              {props.data.pathData.attributes.MAX_ALTITUDE
                ? `${t("studiesActor.height_value")} ${
                    props.data.pathData.attributes.MAX_ALTITUDE
                  }`
                : `${t("studiesActor.no_height_data")}`}
            </div>
          </div>
        </div>
        {props.data.pathData.attributes.MAX_ALTITUDE &&
          props.data.pathData.attributes.MIN_ALTITUDE && (
            <div
              style={{
                height: "100px",
                width: "100%",
                background: "#D9D9D9",
                borderRadius: "8px",
              }}
            ></div>
          )}
      </div>
      {/* end height */}

      {/* start speed */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
          display: "flex",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "start",
          }}
        >
          <img src={spead_icon} alt="" />
          <div
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <div style={{ fontWeight: "700", fontSize: "15px" }}>
              {t("studiesActor.speed")}
            </div>
            <div style={{ fontWeight: "400", fontSize: "12px" }}>
              {props.data.pathData.attributes.MAX_SPEED
                ? `${t("studiesActor.max_speed")} ${
                    props.data.pathData.attributes.MAX_SPEED
                  }`
                : `${t("studiesActor.no_speed_data")}`}
            </div>
            <div style={{ fontWeight: "400", fontSize: " Boards 12px" }}>
              {props.data.pathData.attributes.MAX_SPEED &&
              props.data.pathData.attributes.MIN_SPEED
                ? `${t("studiesActor.avg_speed")} ${
                    (props.data.pathData.attributes.MAX_SPEED +
                      props.data.pathData.attributes.MIN_SPEED) /
                    2
                  }`
                : `${t("studiesActor.no_speed_data")}`}
            </div>
          </div>
        </div>
        {props.data.pathData.attributes.MAX_SPEED &&
          props.data.pathData.attributes.MIN_SPEED && (
            <div
              style={{
                height: "100px",
                width: "100%",
                background: "#D9D9D9",
                borderRadius: "8px",
              }}
            ></div>
          )}
      </div>
      {/* end speed */}

      {/* start share */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
          display: "flex",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <label htmlFor="switch-1">{t("studiesActor.this_path_label")}</label>
          <Switch
            defaultChecked
            id="switch-1"
            style={{ width: "45px" }}
            onChange={handleTogglePathSwitch}
          />
        </div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <label htmlFor="switch-2">
            {t("studiesActor.other_paths_label")}
          </label>
          <Switch
            defaultChecked
            id="switch-2"
            onChange={handleToggleOtherPathsSwitch}
          />
        </div>
      </div>
      {/* end share */}

      {/* start tools */}
      <div className="box">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            color: "#fff",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "5px",
              alignItems: "center",
            }}
          >
            <img src={tools_logo} alt="tools logo" />
            <span>{t("drawingShape")}</span>
          </div>
          <div
            style={{
              display: "flex",
              gap: "5px",
              alignItems: "center",
            }}
          >
            <Button
              onClick={async (e) => {
                e.stopPropagation();
                await handleSaveDrawnGraphics();
              }}
            >
              {t("save")}
            </Button>
            <Button
              onClick={(e) => {
                e.stopPropagation();
                pathsDrawingsLayer.removeAll();
              }}
            >
              {t("clearAll")}
            </Button>
            <MdKeyboardArrowDown
              size={20}
              style={{
                cursor: "pointer",
                transform: `rotate(${!showTools ? "180deg" : 0})`,
              }}
              onClick={() => setShowTools(!showTools)}
            />
          </div>
        </div>
        {showTools && (
          <div
            className="images"
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(6, 1fr)",
              gap: "10px",
              marginTop: "10px",
            }}
          >
            {tools.map((tool, indx) => (
              <Tooltip key={indx} title={tool.label} placement="bottom">
                <div
                  name={tool.name}
                  className="image"
                  style={{
                    cursor: "pointer",
                    background: selectedTool === tool.name ? "#B55433" : "",
                  }}
                  onClick={() => {
                    handleSelectTool({
                      name: tool.name,
                      src: tool.src,
                      index: indx,
                    });
                  }}
                >
                  <img
                    src={tool.src}
                    alt="tool"
                    style={{
                      filter:
                        selectedTool === tool.name
                          ? "brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(2%) hue-rotate(264deg) brightness(105%) contrast(101%)"
                          : "",
                    }}
                  />
                </div>
              </Tooltip>
            ))}
            <div
              className="image"
              onClick={() => {
                setShowColorPicker(!showColorPicker);
              }}
              style={{
                background: selectedColor,
              }}
            >
              <img
                src={tool_12}
                alt="select color"
                style={{
                  filter:
                    selectedColor === "#ffffff"
                      ? "brightness(0) saturate(100%) inver(0%) sepia(100%) saturate(7500%) hue-rotate(16deg) brightness(94%) contrast(106%)"
                      : "",
                }}
              />
            </div>
            {showColorPicker && (
              <div
                style={{
                  position: "absolute",
                  direction: "ltr",
                }}
              >
                <SketchPicker
                  color={selectedColor}
                  onChange={(color) => {
                    setSelectedColor(color.hex);
                    setShowColorPicker(false);
                  }}
                />
              </div>
            )}
          </div>
        )}
      </div>
      {/* end tools */}

      <button
        style={{
          color: "#B55433",
          backgroundColor: "#EEE7E1",
          padding: "16px",
          borderRadius: "8px",
          fontWeight: "700",
          display: "flex",
          alignItems: "center",
          gap: "10px",
          justifyContent: "center",
        }}
        onClick={exportKML}
      >
        <img
          src={kml_icon}
          alt=""
          style={{
            filter:
              "brightness(0) saturate(100%) invert(33%) sepia(75%) saturate(479%) hue-rotate(329deg) brightness(103%) contrast(97%)",
          }}
        />
        {t("studiesActor.export_kml")}
      </button>
      <button
        style={{
          color: "#B55433",
          backgroundColor: "#EEE7E1",
          padding: "16px",
          borderRadius: "8px",
          fontWeight: "700",
          display: "flex",
          alignItems: "center",
          gap: "10px",
          justifyContent: "center",
        }}
        onClick={handleImportClick}
      >
        <img
          src={kml_icon}
          alt=""
          style={{
            filter:
              "brightness(0) saturate(100%) invert(33%) sepia(75%) saturate(479%) hue-rotate(329deg) brightness(103%) contrast(97%)",
          }}
        />
        {t("studiesActor.import_file")}
      </button>
      <input
        type="file"
        style={{ display: "none" }}
        ref={fileInputRef}
        accept=".kml,.kmz"
        onChange={handleKMLImport}
      />

      {/* files */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        {importedFiles.map((file) => (
          <div
            key={file.fileID}
            className="generalSearchCard"
            style={{
              padding: "16px",
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              cursor: "pointer",
            }}
            onClick={() => loadImportedFile(file)}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "10px",
                flexShrink: "0",
              }}
            >
              <img src={kml_icon} alt="" />
              <div
                style={{
                  maxWidth: "200px",
                  wordWrap: "break-word",
                }}
              >
                {file.name}
              </div>
            </div>
            <FaTrashAlt
              style={{ cursor: "pointer" }}
              onClick={(e) => {
                e.stopPropagation();
                deleteImportedFile(file.fileID);
              }}
            />
          </div>
        ))}
      </div>

      {/* Gallery Modal */}
      <Modal
        className="study-actor-modal"
        title={t("studiesActor.gallery_modal_title")}
        closable={{ "aria-label": "Custom Close Button" }}
        open={isGalleryModalOpen}
        onOk={handleGalleryModalOk}
        onCancel={handleCancelGalleryModal}
        width={"50%"}
      >
        <ReactImageGallery items={galleryItems} />
      </Modal>

      {/* Monitoring Case Modal */}
      <Modal
        className="study-actor-modal"
        title={t("studiesActor.monitoring_case_info")}
        closable={{ "aria-label": "Custom Close Button" }}
        open={isMonitorCaseModalOpen}
        onOk={handleMonitorCaseModalOk}
        onCancel={handleMonitorCaseModalCancel}
        width="500px"
        style={{ height: "500px" }}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "center",
            justifyContent: "space-between",
            marginBlock: "20px",
          }}
        >
          {clickedGraphics.map((_, index) => (
            <div
              key={index}
              style={{
                color: activeTab === index ? "#fff" : "#B45333",
                backgroundColor: activeTab === index ? "#B45333" : "#FFFFFF",
                borderRadius: "25px",
                padding: "5px 10px",
                flex: 1,
                textAlign: "center",
                cursor: "pointer",
              }}
              onClick={() => setActiveTab(index)}
            >
              {`${t("studiesActor.monitoring_case")} ${index + 1}`}
            </div>
          ))}
        </div>
        <div
          className="generalSearchCard"
          style={{ padding: "16px", margin: 0 }}
        >
          <div style={{ display: "flex", alignItems: "start", gap: "10px" }}>
            <img src={monitor_modal_image} alt="Monitoring case" />
            <div
              style={{ display: "flex", flexDirection: "column", gap: "10px" }}
            >
              <div style={{ fontWeight: "400", fontSize: "16px" }}>
                {modalFields.plantName}
              </div>
              <div style={{ fontWeight: "400", fontSize: "12px" }}>
                {t("studiesActor.date", { date: modalFields.date })}
              </div>
              <div style={{ fontWeight: "400", fontSize: "12px" }}>
                {t("studiesActor.area", { area: modalFields.area })}
              </div>
              <div style={{ fontWeight: "400", fontSize: "12px" }}>
                {t("studiesActor.reserve", { reserve: modalFields.reserve })}
              </div>
            </div>
          </div>
        </div>
        <div>
          <label className="selectLabelLabelStyle">
            {t("studiesActor.observation_type")}
          </label>
          <Input value={modalFields.observationType} readOnly />
        </div>
        <div>
          <label className="selectLabelStyle">
            {t("studiesActor.report_name")}
          </label>
          <Input value={modalFields.reportName} readOnly />
        </div>
        <div>
          <label className="selectLabelStyle">
            {t("studiesActor.observation_description")}
          </label>
          <Input value={modalFields.observationDescription} readOnly />
        </div>
        <div>
          <label className="selectLabelStyle">
            {t("studiesActor.observation_count")}
          </label>
          <Input value={modalFields.observationCount} readOnly />
        </div>
        <div>
          <label className="selectLabelStyle">
            {t("studiesActor.importance")}
          </label>
          <Input value={modalFields.importance} readOnly />
          {/* <Select
            value={modalFields.importance}
            disabled
            suffixIcon={<RiArrowDropDownFill size={30} />}
            options={[
              { value: modalFields.importance, label: modalFields.importance },
            ]}
          /> */}
        </div>
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginBlock: "10px",
            flexWrap: "wrap",
          }}
        >
          {mediaItems.map((media, index) => (
            <div key={index} style={{ position: "relative" }}>
              {media.endsWith(".mp4") ||
              media.endsWith(".webm") ||
              media.endsWith(".ogg") ? (
                <video
                  src={media}
                  controls
                  style={{
                    width: "100px",
                    height: "100px",
                    objectFit: "cover",
                    borderRadius: "8px",
                  }}
                />
              ) : (
                <img
                  src={media || gallery_image}
                  alt={`Media ${index + 1}`}
                  style={{
                    width: "100px",
                    height: "100px",
                    objectFit: "cover",
                    borderRadius: "8px",
                  }}
                />
              )}
              <FaTrashAlt
                style={{
                  position: "absolute",
                  top: "10px",
                  left: "10px",
                  color: "#fff",
                  cursor: "pointer",
                }}
                onClick={() => {
                  const monitorCase = monitoringCases.find(
                    (item) =>
                      item.feature.attributes.OBJECTID ===
                      activeGraphic.attributes.OBJECTID
                  );
                  if (monitorCase) {
                    const mediaType =
                      media.endsWith(".mp4") ||
                      media.endsWith(".webm") ||
                      media.endsWith(".ogg")
                        ? "video"
                        : "image";
                    handleDeleteMedia(monitorCase, media, mediaType);
                  }
                }}
              />
            </div>
          ))}
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              border: "1.5px dashed #B55433",
              borderRadius: "8px",
              padding: "10px",
            }}
          >
            <Upload>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: "5px",
                  flexDirection: "column",
                  cursor: "pointer",
                  color: "#B55433",
                }}
              >
                <CiImageOn size={20} />
                <div style={{ fontSize: "12px", fontWeight: "700" }}>
                  {t("studiesActor.add_more_images")}
                </div>
              </div>
            </Upload>
          </div>
        </div>
        <button className="SearchBtn">{t("studiesActor.edit")}</button>
      </Modal>
      {/* end monitoring case modal */}

      <Modal
        title={t("enterText", { ns: "common" })}
        centered
        visible={textModalVisible}
        onCancel={() => {
          setTextModalVisible(false);
          setWarningMessage(false);
        }}
        okText={t("edit")}
        cancelText={t("cancel")}
      >
        <Input
          name="pointText"
          maxLength={70}
          ref={textPointRef}
          placeholder={t("enterText2", { ns: "common" })}
          onChange={(e) => {
            setWarningMessage(
              e.target.value === "" || e.target.value.trim().length == 0
            );
          }}
        />
        {warningMessage && (
          <span
            style={{
              display: "inline-flex",
              alignItems: "center",
              padding: "4px 8px",
              borderRadius: "4px",
              fontSize: "16px",
              color: "#ff3333",
            }}
          >
            <span style={{ marginRight: "4px", fontSize: "18px" }}>⚠️</span>
            <span style={{ fontWeight: "bold" }}>
              {t("enterText", { ns: "common" })}
            </span>
          </span>
        )}
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            justifyContent: "end",
          }}
        >
          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => {
              drawTextHandle();
            }}
          >
            {t("confirm", { ns: "common" })}
          </Button>
          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => {
              setTextModalVisible(false);
              setWarningMessage(false);
            }}
          >
            {t("close", { ns: "common" })}
          </Button>
        </div>
      </Modal>
    </div>
  );
}
