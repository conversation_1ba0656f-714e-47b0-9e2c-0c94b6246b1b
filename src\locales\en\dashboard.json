{"layers": "Layers", "chooseLayer": "Choose from layers' list", "timeContext": "Time Context", "chooseTimeContext": "Choose the time context", "yearly": "yearly", "monthly": "monthly", "daily": "daily", "chooseFromCalendar": "Choose from the calendar", "incidentsTotal": "Total No. of Incidents", "mainMunicipalitiesForIncident": "No. of Incidents of Municilipilites", "PlanForIncidentPerMun": "No. of Incidents of diggingLicense in", "adminBoundary": " Adminstration Boundary", "mainMunicipalities": "Municipilities", "sub_MUNICIPALITY_NAME": "Choose from Municipilities", "secMunicipalities": "Sub Municipilities", "sub_SUB_MUNICIPALITY_NAME": "Choose from Sub Municipilities", "plans": "Plans", "sub_PLAN_NO": "Choose from Plans", "districts": "Districts", "sub_DISTRICT_NAME": "Choose from Districts", "totalCount": "Total Count", "totalArea": "Total Area (Km2)", "totalLength": "Total Length (Km)", "sumForLayer": "Total Data for Layer", "notFound": "Not available", "countPerTime": "Count based on the time context", "count": "Count", "area": "Area", "areaKm2": "Area km2", "countPerYear": "Count/Year", "countPerMonth": "Count/Month", "countPerDay": "Count/Day", "year": "Year", "month": "Month", "day": "Day", "noDataForEnteredTimeContext": "No Data for the entered time context", "clear": "Clear", "sizHighestValues": "The 6 highest values", "sizLowestValues": "The 6 lowest values", "all": "Total", "name": "Name", "siteActivity": "Site Activity", "siteStatus": "Site Status", "siteSubtype": "Site Subtype", "municiplityType": "Municipility Type", "incidentNuPermuniciplityName": "Major municipilities per Incident No.", "incidentNuPerDistrictName": "Major districts per Incident No.", "incidentNuPerSubMuniciplityName": "Major sub-municipilities per Incident No.", "deformationNuPermuniciplityName": "Deformation per municipility", "licenseNuPermuniciplityName": "License No. per municipility", "parcelIsInvested": "<PERSON><PERSON>el Invested ?", "parcelProperty": "Parcel Property", "deformationType": "Deformation Type", "buildingType": "Building Type", "materialType": "Material Type", "priceStatus": "Building Status", "munClass": "Municipility Class", "planClass": "Plan Class", "incidentStatus": "Incident Status", "eventsNoPermuniciplityName": "Events No. per municipility", "eventsStatusFund": "Status Funding of Events", "contractStatus": "Contract Status of Events", "serviceType": "Service Types", "serviceIsDeveloped": "Is Service developed ?", "parcelIsDeveloped": "Is Parcel developed ?", "serviceOwners": "Service Owners", "gasStationNoPerGovName": "No. Gas Station per Gov.", "gasStationNoPerMunName": "No. Gas Station per Mun.", "stationClasses": "Station Classification", "planLanduse": "Plan Land-Use", "planStatus": "Plan Status", "planType": "Plan Type", "approvalStatus": "Approval Status", "parcelMainLuse": "Parcel Main Land-Use", "actualMainLUse": "Actual Main Land-Use", "ownerType": "Owner Type", "usingSymbol": "Used Symbol", "streetClass": "Street Class", "streetNameClass": "Street Name Class", "oneWay": "Number on Direction (1/more)", "asphaltStatus": "Asphalt Status", "NotDefined": "Not defined", "CountKAndAreaWthKM": "Count (K), Area (Km2)", "CountAndAreaWthKM": "Count, Area (Km2)", "CountK": "Count (K)", "clickForHeatMap": "Click to shown on Map", "classificationType": "Incident Classification", "mostIncidentClassificationType": "Most widely Incidents for visual distor.", "moreWidelyIncidentsPerAdmin": "More Widely Incidients Per Adminstration", "diggingLicense": "Current Digging License", "countsructionLic": "Construction Licenses", "closeSideTbl": "Close Table", "ClickHereForDetails": "Click here to display more details", "exportToSVG": "Export File in SVG", "exportToPNG": "Export File in PNG", "exportToCSV": "Export File in CSV", "menu": "<PERSON><PERSON>", "selection": "Selection", "selectionZoom": "Selection Zoom", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "pan": "Panning", "reset": "Reset Zoom"}