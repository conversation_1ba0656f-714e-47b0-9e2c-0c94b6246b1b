import React, { useState, useEffect } from "react";

import <PERSON>unt<PERSON><PERSON>Comp from "./DountChartComp";
import { Modal } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlusCircle,faExpandArrowsAlt } from "@fortawesome/free-solid-svg-icons";
import MAPLOGO from "../../../assets/images/dashboard/map-marker.png";
import SelectComp from "../DashboardHeader/SelectComp";
import { useTranslation } from "react-i18next";

export default function DashboardCountAreaPart({
  chartsData,
  type,
  queryData,
  title,
  map,
  activeHeatMapFactor,
  setActiveHeatMapFactor,
  hasCount,
  hasArea,
}) {
  const { i18n, t } = useTranslation("dashboard");

  const [isModalOpen, setIsModalOpen] = useState(null);
  const [filterList] = useState([
    {
      name: t("sizHighestValues"),
      value: "max-6",
    },
    {
      name: t("sizLowestValues"),
      value: "min-6",
    },
    {
      name: t("all"),
      value: "all",
    },
  ]);
  const [shownListVal, setShownListVal] = useState({
    count: "max-6",
    areaOrLength: "max-6", // t("sizHighestValues") or "all"
  });

  React.useEffect(() => {
    return () => {
      setShownListVal({
        count: "max-6",
        areaOrLength: "max-6", // t("sizHighestValues") or "all"
      });
    };
  }, []);
  const handleSelectShownListVal = (name) => (value, e) => {
    if (name === "count")
      setShownListVal({
        ...shownListVal,
        count: value,
      });
    else
      setShownListVal({
        ...shownListVal,
        areaOrLength: value,
      });
  };
  const handleEditActiveHeatMapFactor = (name) => {
    if (activeHeatMapFactor !== name) setActiveHeatMapFactor(name);
  };
  const getTitle=()=>{
    
       if(queryData?.selectedBoundaryType?.value === 'DISTRICT_NAME') 
       {
        let title = t('totalCountDistricts');
        let isDistSelected = queryData?.selectedBoundaryType?.preNeededBound?.selectedPreNeededBoundary;
        if(isDistSelected) {
          let munNAme = queryData?.selectedBoundaryType?.preNeededBound?.preNeededBoundariesArr?.find(i=>i.value===isDistSelected)?.label;
          title+=` (${munNAme})`
        } 
        return title
      }
       else if(queryData?.selectedBoundaryType?.value === 'PLAN_NO') return t('totalCountPlans') 
       else if(queryData?.selectedBoundaryType?.value === 'SUB_MUNICIPALITY_NAME') return t('totalCountSubMun') 
       else if(queryData?.selectedBoundaryType?.value === 'MUNICIPALITY_NAME') return t('totalCountMun') 
       else return title;
    
  }
  return (
    <div className="mapSquare">
      <FontAwesomeIcon
        style={{ float: "right", marginRight: "10px", marginTop: "15px" }}
        onClick={() => setIsModalOpen(1)}
        className="faplusChart"
        // icon={faPlusCircle}
        icon={faExpandArrowsAlt}
      />

      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignSelf: "center",
          // justifyContent: "center",
        }}>
        {" "}
        <div style={{ display: "flex", justifyContent: "center" }}>
          <h6
            className={activeHeatMapFactor === type ? "active-title" : ""}
            style={{ display: "flex" }}>
            {getTitle()}
          </h6>
          {activeHeatMapFactor &&
            ((type === "count" && hasArea) ||
              (type === "area" && hasCount)) && (
              <img
                title={t("clickForHeatMap")}
                src={MAPLOGO}
                className="map-pointer"
                style={{ cursor: activeHeatMapFactor ? "pointer" : "auto" }}
                alt="map logo"
                onClick={
                  activeHeatMapFactor
                    ? () => handleEditActiveHeatMapFactor(type)
                    : () => {}
                }
              />
            )}

          {type === "count"
            ? chartsData[type] &&
              typeof chartsData[type] === "object" && (
                <div style={{ width: "max-content" }}>
                  <SelectComp
                    listName={type}
                    onChange={handleSelectShownListVal(type)}
                    value={shownListVal[type]}
                    //  placeholder={t(`chooseTimeContext`)}
                    list={filterList || []}
                    allowClear={true}
                  />
                </div>
              )
            : chartsData[type].value &&
              typeof chartsData[type]?.value === "object" && (
                <div style={{ width: "max-content" }}>
                  <SelectComp
                    listName={type}
                    onChange={handleSelectShownListVal(type)}
                    value={shownListVal[type]}
                    //  placeholder={t(`chooseTimeContext`)}
                    list={filterList || []}
                    allowClear={true}
                  />
                </div>
              )}
        </div>{" "}
        <DountChartComp
          chartsData={chartsData}
          type={type}
          queryData={queryData}
          map={map}
          activeHeatMapFactor={activeHeatMapFactor}
          setActiveHeatMapFactor={setActiveHeatMapFactor}
          shownListVal={shownListVal}
        />
      </div>
      <Modal
        cancelText="إغلاق"
        className="dashModalZoom"
        visible={isModalOpen == 1 ? true : false}
        onCancel={() => setIsModalOpen(null)}
        footer={null}
        width={"50%"}>
        <DountChartComp
          chartsData={chartsData}
          type={type}
          queryData={queryData}
          map={map}
          activeHeatMapFactor={activeHeatMapFactor}
          setActiveHeatMapFactor={setActiveHeatMapFactor}
          shownListVal={shownListVal}
        />
      </Modal>
    </div>
  );
}
