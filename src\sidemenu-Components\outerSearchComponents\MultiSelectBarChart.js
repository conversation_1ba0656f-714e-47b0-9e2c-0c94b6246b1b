import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";
import { getLayerId, queryTask } from "../../helper/common_func";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useTranslation } from "react-i18next";

const MultiSelectBarChart = (props) => {
  const { t } = useTranslation("common");
  const [accordionData, setAccordionData] = useState([]);

  let layerId = getLayerId(props.map.__mapInfo, "TERRESTRIAL_SPECIE");
  let parentlayerId = getLayerId(
    props.map.__mapInfo,
    "TERRESTRIAL_BIODIVERSITY"
  );
  let parentLayer = props.map
    .findLayerById("baseMap")
    .allSublayers.items.find((x) => x.id == parentlayerId);
  let layer = props.map
    .findLayerById("baseMap")
    .allSublayers.items.find((x) => x.id == layerId);
  const [selectedBars, setSelectedBars] = useState([]);
  const fieldName =
    props.languageState == "ar" ? "AR_NCW_CATEGORY" : "EN_NCW_CATEGORY";

  useEffect(() => {
    return () => {
      parentLayer.visible = false;
      layer.definitionExpression = "ObjectId = -1";
      layer.visible = false;
    };
  }, []);

  const handleClick = (entry, index) => {
    console.log("bar clicked entry", entry);
    console.log("bar clicked field name ", fieldName);
    let selectedCategories = selectedBars;
    const isSelected = selectedCategories.some(
      (item) => item.payload[fieldName] === entry.payload[fieldName]
    );
    if (isSelected) {
      selectedCategories = selectedCategories.filter(
        (item) => item.payload[fieldName] !== entry.payload[fieldName]
      );
    } else {
      selectedCategories = [...selectedCategories, entry];
    }
    setSelectedBars(selectedCategories);
    if (selectedCategories.length > 0) {
      console.log("props.fieldName", props.fieldName);
      let whereExpression = [];
      whereExpression.push(
        props.languageState == "ar"
          ? `${props.fieldName}='${props.reserveName}' AND (`
          : `${props.fieldName}='${props.reserveName}' AND (`
      );
      if (selectedCategories.length > 1) {
        whereExpression = [
          ...whereExpression,
          ...selectedCategories.map(
            (cat) => `${fieldName}='${cat.payload[fieldName]}' or `
          ),
        ];
        whereExpression = whereExpression
          .toString()
          .replace(/,/g, "")
          .slice(0, -3);
      } else if (selectedCategories.length == 1) {
        whereExpression.push(
          `${fieldName}='${selectedCategories[0].payload[fieldName]}'`
        );
        whereExpression = whereExpression.toString().replace(/,/g, "");
      }
      whereExpression = whereExpression + ")";
      console.log("whereExpression", whereExpression);
      parentLayer.visible = true;
      layer.visible = true;
      layer.definitionExpression = whereExpression;
    } else {
      layer.visible = false;
    }
  };

  const getBarFill = (entry) => {
    return selectedBars.some(
      (item) => item.payload[fieldName] === entry[fieldName]
    )
      ? "#82ca9d"
      : "#b45333";
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: "white",
            padding: "5px",
            border: "1px solid #ccc",
          }}
        >
          <p>{`${label} : ${payload[0].value}`}</p>
        </div>
      );
    }

    return null;
  };
  const handleAccordionClick = (row) => {
    console.log("accordion data", accordionData);
    if (!accordionData.some((specie) => specie.specieName == row[fieldName])) {
      let layerId = getLayerId(props.map.__mapInfo, "TERRESTRIAL_SPECIE");
      let whereForStatistics =
        props.languageState == "ar"
          ? `${props.fieldName}='${props.reserveName}' AND ${fieldName}='${row[fieldName]}'`
          : `${props.fieldName}='${props.reserveName.replaceAll(
              /'/gi,
              "''"
            )}' AND ${fieldName}='${row[fieldName]}'`;
      queryTask({
        url: window.mapUrl + "/" + layerId,
        where: whereForStatistics,
        groupByFields: ["SPECIE_ALIAS"],
        returnGeometry: true,
        statistics: [
          { type: "count", field: "SPECIE_ALIAS", name: "SPECIE_COUNT" },
        ],
        callbackResult: ({ features }) => {
          console.log("query result", features);
          let specieName = row[fieldName];
          let specieData = {
            specieName,
            data: features.map((feat) => feat.attributes),
          };
          setAccordionData((prevState) => [...prevState, specieData]);
        },
        callbackError: (error) => {
          console.log("error", error);
        },
      });
    }
  };

  return (
    <>
      {props.chartData && props.chartData.length == 0 ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "20vh",
          }}
        >
          <p style={{ color: "white" }}>{t("noDataAvail")}</p>
        </div>
      ) : (
        <>
          <div>
            <BarChart
              style={{ cursor: "pointer", display: "inline-block" }}
              width={400}
              height={300}
              data={props.chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              onClick={(e) => {
                console.log("e", e);
                if (e.activePayload) {
                  handleClick(e.activePayload[0]);
                }
              }}
            >
              <XAxis
                dataKey={fieldName}
                interval={0}
                angle={-30}
                height={80}
                tick={{
                  fill: "white",
                  fontSize: 12,
                  dy: 10,
                  dx: 0,
                  x: 0,
                  y: 0,
                  textAnchor: "middle",
                  transform: "rotate(0)",
                }}
              />
              <YAxis tick={{ fill: "white", fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="COUNT_EXPR0"
                fill="#8884d8"
                // onClick={handleClick}
                fillOpacity={0.8}
                cursor="pointer"
              >
                {props.chartData &&
                  props.chartData.map((entry, index) => (
                    <Cell
                      cursor="pointer"
                      fill={getBarFill(entry)}
                      key={`cell-${index}`}
                    />
                  ))}
              </Bar>
            </BarChart>
          </div>
          <Paper style={{ width: "100%", borderRadius: "0" }}>
            {props.chartData &&
              props.chartData.map((row, index) => (
                <Accordion
                  onChange={() => handleAccordionClick(row)}
                  key={index}
                  style={{
                    width: "100%",
                    backgroundColor: "transparent",
                    boxShadow: "none",
                    borderRadius: "0",
                    borderBottom: "1px solid #fff",
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon sx={{ color: "white" }} />}
                    style={{ color: "white", paddingBlock: 0 }}
                  >
                    <Grid
                      container
                      spacing={2}
                      alignItems="center"
                      sx={{
                        width: "100%",
                        m: 0,
                        p: "8px",
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      {props.languageState === "ar" ? (
                        <>
                          <Grid
                            item
                            style={{ padding: 0, flex: 1, textAlign: "start" }}
                          >
                            <div sx={{ color: "white" }}>{row[fieldName]}</div>
                          </Grid>
                          {row.legend && (
                            <Grid item style={{ padding: 0, flex: 1 }}>
                              <img
                                src={"data:image/jpeg;base64," + row.legend}
                                alt=""
                                style={{ maxHeight: "30px" }}
                              />
                            </Grid>
                          )}
                          <Grid item style={{ padding: 0, flex: 1 }}>
                            <div sx={{ color: "white" }}>{row.COUNT_EXPR0}</div>
                          </Grid>
                        </>
                      ) : (
                        <>
                          <Grid
                            item
                            style={{ padding: 0, flex: 1, textAlign: "start" }}
                          >
                            <div sx={{ color: "white" }}>{row.COUNT_EXPR0}</div>
                          </Grid>
                          {row.legend && (
                            <Grid item style={{ padding: 0, flex: 1 }}>
                              <img
                                src={"data:image/jpeg;base64," + row.legend}
                                alt=""
                                style={{ maxHeight: "30px" }}
                              />
                            </Grid>
                          )}
                          <Grid item style={{ padding: 0, flex: 1 }}>
                            <div sx={{ color: "white" }}>{row[fieldName]}</div>
                          </Grid>
                        </>
                      )}
                    </Grid>
                  </AccordionSummary>
                  <AccordionDetails
                    style={{
                      border: "1px solid #fff",
                      borderRadius: "5px",
                      margin: "10px",
                      padding: "8px",
                    }}
                  >
                    {accordionData.length > 0 &&
                      accordionData.map((item, index) => (
                        <div
                          key={index}
                          style={{
                            // display: "flex",
                            // gap: "10px",
                            color: "white",
                            // justifyContent: "space-around",
                          }}
                        >
                          <Table>
                            {item.specieName == row[fieldName] ? (
                              item.data.map(
                                (item) =>
                                  item.SPECIE_COUNT != 0 && (
                                    <tr>
                                      <td
                                        style={{
                                          padding: "5px",
                                          width: "100%",
                                        }}
                                      >
                                        {item.SPECIE_ALIAS}
                                      </td>
                                      <td style={{ padding: "5px" }}>
                                        {item.SPECIE_COUNT}
                                      </td>
                                    </tr>
                                  )
                              )
                            ) : (
                              <></>
                            )}
                          </Table>
                        </div>
                      ))}
                  </AccordionDetails>
                </Accordion>
              ))}
          </Paper>
        </>
      )}
    </>
  );
};

export default MultiSelectBarChart;
