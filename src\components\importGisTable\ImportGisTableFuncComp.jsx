import React, { useEffect } from "react";
import {
  clearCanvasLine,
  drawLine,
  highlightFeature,
  project,
  zoomToFeatureDefault,
  showLoading,
} from "../../helper/common_func";
import { faSearchPlus } from "@fortawesome/free-solid-svg-icons";
import { Pagination } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconButton } from "@mui/material";
import { useTranslation } from "react-i18next";
import usePersistentState from "../../helper/usePersistentState";

const componentName = "ImportGisTableFuncComp";
function ImportGisTableFuncComp (props) {
  const {t} = useTranslation("map");
  const [currentFileId, setCurrentFileId] = usePersistentState("currentFileId", null, componentName)
  const [displayFeatures, setDisplayFeatures] = usePersistentState("displayFeatures", null, componentName);
  const [totalPage, setTotalPage] = usePersistentState("totalPage", 0, componentName);
  const [current, setCurrent] = usePersistentState("current", 1, componentName);
  const [allCount, setAllCount] = usePersistentState("allCount", 0, componentName);
  const [minIndex, setMinIndex] = usePersistentState("minIndex", 0, componentName);
  const [maxIndex, setMaxIndex] = usePersistentState("maxIndex", 0, componentName);
  useEffect(() => { // This function will be called when `data` changes 
    if (currentFileId ===  props?.data?.fileID) return;
    addFeaturesToTable(props.data); 
  }, [props.data]); 
  function addFeaturesToTable(data) {
    showLoading(true);
    let features = [];

    if (data.uploadFileType == "cad") {
      let displayFeatures = data.data.shapeFeatures;
      if (displayFeatures) {
        Object.keys(displayFeatures).map((key) => {
          displayFeatures[key] = displayFeatures[key].map((f) => {
            return {
              geometry: f,
              attributes: { ...f.attributes, layerName: key, area: f.area },
            };
          });
          features = features.concat(displayFeatures[key]);
        });
      }
    } else if (data.uploadFileType == "kmz") {
      features = data.data.features;
      if (features) {
        features.forEach((f) => {
          f.geometry.spatialReference = data.data.spatialReference;
          delete f.attributes["PopupInfo"];
        });
      }
    }

    if (features) {
      let fistFeat = features[0];
      let featCrs = fistFeat?.geometry?.spatialReference?.wkid;
      if (featCrs  === 102100) return;
      project(features, 102100, (res) => {
        setDisplayFeatures(res);
        setMinIndex(0);
        setTotalPage(res.length / window.paginationCount);
        setMaxIndex(window.paginationCount);
        setAllCount(res.length);
        setCurrentFileId(data?.fileID)
        setTimeout(() => {
          highlightFeature(res, data.map, {
            isHighlighPolygonBorder: true,
            highlightWidth: 3,
            layerName: "highlightGraphicLayer",
            isZoom: true,
            zoomDuration: 1000,
          });
        }, 1000);

        showLoading(false);
      });
    }
  }
  function onMouseMoveOnFeature(feature, e) {
    if (feature.geometry) {
      highlightFeature(feature, props.data.map, {
        layerName: "SelectGraphicLayer",
      });
    }
    drawLine({
      feature: feature,
      map: props.data.map,
      event: e,
      hideFromHeight:
        props.searchTableDisplay == "searchTableShown"
          ? window.innerHeight * 0.6
          : 200,
    });
  }
  function  clearFeatures() {
    props.data.map.findLayerById("SelectGraphicLayer").removeAll();
    clearCanvasLine();
  }
  const handleChange = (page) => {
    setCurrent(page);
    setMinIndex((page - 1) * window.paginationCount);
    setMaxIndex(page * window.paginationCount);
  };
  return (
        <div style={{ height: "calc(100% - 84px)" }}>
          <div className="tableTitle">
            <span className="px-2"> {t("mapTools.resultNum")} </span>
            <span style={{ fontWeight: "bold" }}> {allCount}</span>
          </div>
  
          {displayFeatures && displayFeatures.length > 0 && (
            <div style={{ height: "100%" }}>
              <div style={{ height: "100%", overflow: "auto" }}>
                <table
                  class="table table-hover result-table table-bordered"
                  // style={{ margin: "5px" }}
                >
                  <thead>
                    <th style={{ textAlign: "center" }}>{t("element")}</th>
  
                    {Object.keys(displayFeatures[0].attributes).map((attr) => {
                      return (
                        <th style={{ textAlign: "center" }}>
                          {attr == "layerName"
                            ? t("layerName")
                            : attr == "area"
                            ? t("mapTools.area")
                            : attr}
                        </th>
                      );
                    })}
                  </thead>
                  <tbody>
                    {displayFeatures
                      .slice(minIndex, maxIndex)
                      .map((feature, index) => {
                        return (
                          <tr
                            key={index}
                            onMouseLeave={(e) => clearFeatures(e)}
                            onMouseMove={(e) =>
                              onMouseMoveOnFeature(feature, e)
                            }
                          >
                            <td style={{ textAlign: "center" }}>{index + 1}</td>
                            {Object.keys(displayFeatures[0].attributes).map(
                              (attr) => {
                                return (
                                  <td style={{ textAlign: "center" }}>
                                    {attr.indexOf("area") > -1
                                      ? (feature.attributes[attr])
                                      : feature.attributes[attr]}
                                  </td>
                                );
                              }
                            )}
                            <td>
                              <IconButton
                                style={{ fontSize: "16px" }}
                                onClick={(e) => {
                                  zoomToFeatureDefault(
                                    feature,
                                    props.data.map
                                  );
                                }}
                              >
                                <FontAwesomeIcon icon={faSearchPlus} />
                              </IconButton>
                            </td>
                          </tr>
                        );
                      })}
                  </tbody>
                </table>
              </div>
              <Pagination
                pageSize={window.paginationCount}
                current={current}
                total={displayFeatures.length}
                onChange={handleChange}
                style={{ bottom: "0px", textAlign: "center", marginBlock: "3px" }}
              />
            </div>
          )}
        </div>
      );
}

export default ImportGisTableFuncComp;