import React, { useEffect, useRef, useState } from "react";
import { Select, Input, message, Spin, DatePicker } from "antd";
import locale from "antd/es/date-picker/locale/ar_EG";
import Checkbox from "antd/lib/checkbox/Checkbox";
import { RiArrowDropDownFill } from "react-icons/ri";
import Extent from "@arcgis/core/geometry/Extent";
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine";
import Graphic from "@arcgis/core/Graphic";

import Polygon from "@arcgis/core/geometry/Polygon";
import Loader from "../../containers/Loader";
import {
  clearGraphicLayer,
  getFeatureDomainName,
  getLayerId,
  isLayerExist,
  queryTask,
  showLoading,
  zoomToFeatureByFilter,
  convertToArabic,
  convertToEnglish,
  highlightFeature,
  highlightAreaWithDimming,
  removeHighlightedAreasWithDimming,
} from "../../helper/common_func";

import { useTranslation } from "react-i18next";
import usePersistentState from "../../helper/usePersistentState";

const { RangePicker } = DatePicker;

const componentName = "GeneralSearch";
const FilterFuncComponent = (props) => {
  const searchTimeOut = useRef();
  const { t, i18n } = useTranslation("common", "layers");

  const [searchLayer, setSearchLayer] = usePersistentState(
    "searchLayer",
    null,
    componentName
  );
  const [formValues, setFormValues] = usePersistentState(
    "formValues",
    {},
    componentName
  );
  const [searchFields, setSearchFields] = usePersistentState(
    "searchFields",
    [],
    componentName
  );
  const [isActiveBufferSearch, setIsActiveBufferSearch] = usePersistentState(
    "isActiveBufferSearch",
    false,
    componentName
  );
  const [showInfo, setShowInfo] = usePersistentState(
    "showInfo",
    false,
    componentName
  );
  const [noData, setNoData] = usePersistentState(
    "noData",
    false,
    componentName
  );
  const [searchLayers, setSearchLayers] = usePersistentState(
    "searchLayers",
    props.mainData.layers
      ? Object.keys(props.mainData.layers)
          .filter((key) => !props.mainData.layers[key].hideFromSearchs)
          ?.map((key) => {
            return {
              layerName: key,
              layer: props.mainData.layers[key],
              name: props.mainData.layers[key].name,
            };
          })
          .filter((l) => {
            return (
              l.layer.isPublicSearchable &&
              l.layer.searchFields &&
              isLayerExist(props.map.__mapInfo, l.layerName)
            );
          })
      : [],
    componentName
  );
  const [loading, setLoading] = usePersistentState(
    "loading",
    false,
    componentName
  );
  const [bufferDistance, setBufferDistance] = usePersistentState(
    "bufferDistance",
    undefined,
    componentName
  );
  const [fetchingServerSearch, setFetchingServerSearch] = usePersistentState(
    "fetchingServerSearch",
    false,
    componentName
  );

  // local state like search input text value to filter lists
  const [tempState, setTempState] = useState({});

  useEffect(() => {
    removeHighlightedAreasWithDimming(props.map);
  }, [searchLayer]);

  // handlers
  const handleLayerSelect = () => (layer) => {
    setSearchLayer(layer);
    setShowInfo(false);
    setNoData(false);
    setFormValues({});
    setSearchFields([]);
    setIsActiveBufferSearch(false);
    getListsValue(layer);
  };
  // for gis layers/tables
  const getListsValue = (layer, getListsAfterFieldName, parentFilter) => {
    let layersSetting = props.mainData.layers;

    //get all filters
    let promiseQueries = [],
      fieldsName = [],
      filterQuery = "";
    let layerdId = getLayerId(props.map.__mapInfo, layer);
    layersSetting[layer]?.searchFields
      ?.filter((x) => !x.isSearch)
      .forEach((item, index) => {
        if (!getListsAfterFieldName) {
          // if(item.dontAddToEsriQuery) { return;}
          fieldsName.push(item.field);

          let undefinedQueryString = ` AND UPPER(${item.field}) <> 'UNDEFINED' AND ${item.field} <> 'غير محدد'`;
          filterQuery = parentFilter
            ? parentFilter +
              " and " +
              item.field +
              " is not null" +
              `${item.isDate ? "" : undefinedQueryString}`
            : "1=1" + `${item.isDate ? "" : undefinedQueryString}`;

          promiseQueries.push(
            queryTask({
              url: window.mapUrl + "/" + layerdId,
              where: filterQuery,
              outFields:
                item.zoomLayer &&
                item.zoomLayer.filterField &&
                !item.zoomLayer.isNotSameAttributeNameInLayer
                  ? [item.field, item.zoomLayer.filterField]
                  : [item.field],
              returnGeometry: false,
              returnExecuteObject: true,
              returnDistinctValues: true,
              notShowLoading: true,
            })
          );
        } else {
          if (item.field == getListsAfterFieldName)
            getListsAfterFieldName = null;
        }
      });

    if (promiseQueries.length > 0) {
      setLoading(true);
    } else {
      setSearchFields(
        layersSetting[layer]?.searchFields?.filter((x) => !x.isSearch)
      );
    }

    if (promiseQueries.length)
      Promise.all(promiseQueries)
        .then((resultsData) => {
          mapResultWithDomain(resultsData, fieldsName, layerdId).then(
            (data) => {
              data.forEach((item, index) => {
                let searchField = layersSetting[layer]?.searchFields?.find(
                  (x) => x.field == fieldsName[index]
                );
                if (item.features.length > 0) {
                  searchField.dataList = [...item.features].filter((i) => {
                    if (searchField?.zoomLayer?.filterField) {
                      return i.attributes[searchField.zoomLayer.filterField];
                    } else return i;
                  });
                } else {
                  searchField.dataList = [];
                }
              });

              setSearchFields(
                layersSetting[layer]?.searchFields?.filter((x) => !x.isSearch)
              );
              // setFormValues({ ...formValues });
              setLoading(false);
            }
          );
        })
        .catch((err) => {
          console.log({ err });
          setLoading(false);
          if (err?.response?.status === 401) {
            //logut
            message.error(t("common:sessionFinished"));
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else message.error(t("common:retrievError"));
        });
  };

  const selectChange = (name, item) => (e, option) => {
    console.log("e :", e);
    console.log("form values are :", formValues);
    let layersSetting = props.mainData.layers;
    setShowInfo(false);
    setNoData(false);
    if (!e) {
      clearGraphicLayer("ZoomGraphicLayer", props.map);
      setFormValues({ ...formValues, [name]: undefined });
    }
    let searchField = layersSetting[searchLayer].searchFields.find(
      (i) => [i.field, i?.zoomLayer?.filterField].includes(name) && !i.isSearch
    );
    console.log("search field", searchField);
    if (searchField) {
      let filterQuery = [];

      if (searchField.zoomLayer) {
        let item = searchField.dataList.find(
          (x) =>
            (x.attributes[name + "_Code"] || x.attributes[name]) ==
            formValues[name]
        );
        if (item) {
          let where = "";
          if (searchField.zoomLayer.isNotSameAttributeNameInLayer) {
            where =
              searchField.zoomLayer.filterField +
              "=" +
              "'" +
              (item.attributes[searchField.field + "_Code"] ||
                item.attributes[searchField.zoomLayer.field]) +
              "'";
          } else {
            where = getWhereClauseForZoom(item, formValues, searchField);
          }

          if (e?.length)
            zoomToFeatureByFilter(where, searchField.zoomLayer.name, props.map);
        }
      }

      if (searchField.alias == "Region") {
        props.map.findLayerById("highlightBoundaryGraphicLayer").removeAll();
        if (e.length > 0) {
          const regionLayerId = getLayerId(props.map.__mapInfo, "REGION");
          let where = "";
          const regionNameField =
            props.languageState === "ar" ? "AR_REGION_NAME" : "EN_REGION_NAME";
          e.forEach((value) => {
            where = where.concat(`${regionNameField} = '${value}' or `);
          });
          where = where.slice(0, -3);
          queryTask({
            url: window.mapUrl + "/" + regionLayerId,
            where: where,
            outFields: [[regionNameField]],
            returnGeometry: true,
            callbackResult: (result) => {
              highlightFeature(result.features, props.map, {
                layerName: "highlightRegionsGraphicLayer",
                isZoom: true,
                fillColor: [0, 0, 0, 0],
                strokeColor: [255, 0, 0],
                isDashStyle: true,
                highlightWidth: 3,
                zoomDuration: 1000,
              });

              highlightAreaWithDimming(result.features, props.map);
            },
          });
        } else {
          props.map.findLayerById("highlightRegionsGraphicLayer").removeAll();
          let existingMaskLayer = props.map.findLayerById("maskLayer");
          if (existingMaskLayer) {
            props.map.remove(existingMaskLayer);
          }
        }
      }

      if (searchField.alias == "PROTECTED_AREA_NAME") {
        if (e.length > 0) {
          const isZoom =
            (!formValues.AR_REGION || formValues.AR_REGION.length == 0) &&
            (!formValues.EN_REGION || formValues.EN_REGION.length == 0)
              ? true
              : false;
          const reserveLayerId = getLayerId(
            props.map.__mapInfo,
            "PROTECTED_AREA_BOUNDARY"
          );
          let where = "";
          const reserveNameField =
            props.languageState === "ar"
              ? "AR_PROTECTED_AREA_NAME"
              : "EN_PROTECTED_AREA_NAME";
          e.forEach((value) => {
            where = where.concat(`${reserveNameField} = '${value}' or `);
          });
          where = where.slice(0, -3);
          queryTask({
            url: window.mapUrl + "/" + reserveLayerId,
            where: where,
            outFields: [[reserveNameField]],
            returnGeometry: true,
            callbackResult: (result) => {
              highlightFeature(result.features, props.map, {
                layerName: "highlightBoundaryGraphicLayer",
                isZoom: isZoom,
                fillColor: [0, 0, 0, 0],
                strokeColor: [0, 0, 255],
                isDashStyle: true,
                highlightWidth: 3,
                zoomDuration: 1000,
              });

              if (
                (!formValues.AR_REGION || formValues.AR_REGION.length == 0) &&
                (!formValues.EN_REGION || formValues.EN_REGION.length == 0)
              ) {
                highlightAreaWithDimming(result.features, props.map);
              }
            },
          });
        } else {
          props.map.findLayerById("highlightBoundaryGraphicLayer").removeAll();
          if (
            (!formValues.AR_REGION || formValues.AR_REGION.length == 0) &&
            (!formValues.EN_REGION || formValues.EN_REGION.length == 0)
          ) {
            let existingMaskLayer = props.map.findLayerById("maskLayer");
            if (existingMaskLayer) {
              props.map.remove(existingMaskLayer);
            }
          }
        }
      }

      let updatedFormValuesAfterDelete = deleteChildValues(name);
      let updatedFormValues = { ...updatedFormValuesAfterDelete, [name]: e };
      Object.keys(updatedFormValues).forEach((key) => {
        if (updatedFormValues[key]) {
          if (updatedFormValues[key].length > 0) {
            let searchCriteria = "(";
            updatedFormValues[key].forEach((value) => {
              searchCriteria += `${key}='${value}' OR `;
            });
            searchCriteria = searchCriteria.slice(0, -4);
            searchCriteria += ")";
            filterQuery.push(searchCriteria);
          }
        }
      });

      getListsValue(searchLayer, name, filterQuery.join(" and "));
      //  setFormValues(updatedFormValues);
    }
    //  else {
    // }
    setFormValues({ ...formValues, [name]: e });
  };
  const handleChangeInput = (e) => {
    console.log("changing input ", e);
    setShowInfo(false);
    setNoData(false);
    setFormValues({ ...formValues, [e.target.name]: e.target.value });
  };
  const handleBufferSearch = (e) => {
    setShowInfo(false);
    setNoData(false);
    setBufferDistance(e.target.value);
  };
  const searchForData = (e) => {
    if (isActiveBufferSearch) {
      setShowInfo(true);
      var handler = props.map.view.on("click", (event) => {
        props.map.view.graphics.removeAll();
        const buffer = geometryEngine.buffer(
          event.mapPoint,
          +bufferDistance,
          "meters"
        );
        var bufferGraphic = new Graphic({
          geometry: buffer,
          symbol: {
            type: "simple-fill",
            color: "rgba(255, 0, 0, 0.3)",
          },
        });
        props.map.view.graphics.add(bufferGraphic);

        handler.remove();
        setShowInfo(false);

        getSearchData(event.mapPoint);
      });
    } else {
      getSearchData();
    }
  };
  const getSearchData = (e) => {
    clearGraphicLayer("generalSearchResultGraphicLayer", props.map);
    let selectedLayer = searchLayer;
    let layersSetting = props.mainData.layers;

    let filterQuery = [];
    console.log("formValues", formValues);
    console.log(
      "new Date(dateString).getTime()",
      new Date(formValues.ToDate).getTime()
    );
    if (Object.hasOwn(formValues, "ToDate")) {
      console.log("The object has the key 'ToDate'");
      filterQuery.push(
        "CREATED_DATE" + "<=" + " DATE '" + formValues.ToDate + "'"
      );
    }
    Object.keys(formValues).forEach((key) => {
      if (formValues[key]) {
        let field = layersSetting[searchLayer].searchFields.find(
          (x) => x.field == key
        );
        console.log("field inside get search data :", field);

        if (field) {
          if (field.isSearch) {
            filterQuery.push(key + " like '%" + formValues[key] + "%'");
          } else if (field.isDate) {
            if (formValues[key][0] && formValues[key][1])
              filterQuery.push(
                key +
                  " BETWEEN DATE '" +
                  formValues[key][0] +
                  "' AND DATE '" +
                  formValues[key][1] +
                  "'"
              );
          } else {
            let whObj = formValues[key].map((f) => {
              return key + "='" + f + "'";
            });

            if (whObj.length)
              filterQuery.push("( " + whObj.join(" or ") + " )");
          }
        }
      }
    });

    filterQuery = filterQuery.join(" and ");
    let layerdId = getLayerId(props.map.__mapInfo, selectedLayer);

    let queryObj = {
      url: window.mapUrl + "/" + layerdId,
      where: filterQuery ? filterQuery : "1=1",
      outFields: layersSetting[searchLayer].outFields,
      returnGeometry: true,
      queryWithGemoerty: isActiveBufferSearch,
      distance: bufferDistance,
      geometry: e,
      layerdId: layerdId,
      layerName: searchLayer,
    };

    let paginationObj = {
      ...queryObj,
      start: 0,
      num: window.paginationCount,
    };

    let promiseQueries = [];

    //for query
    promiseQueries.push(
      queryTask({ ...paginationObj, returnExecuteObject: true })
    );

    //for statistics
    promiseQueries.push(
      queryTask({
        ...queryObj,
        returnGeometry: false,
        statistics: layersSetting[searchLayer].statistics || [
          {
            type: "count",
            field: "OBJECTID",
            name: "countResult",
          },
        ],
        returnExecuteObject: true,
      })
    );

    // 0 for features
    // 1 for statistics

    showLoading(true);

    Promise.all(promiseQueries)
      .then((resultsData) => {
        let features = resultsData[0].features;
        if (features.length) {
          getFeatureDomainName(features, layerdId).then((res) => {
            showLoading(false);

            let mappingRes = res?.map((f) => {
              return {
                layerName: searchLayer,
                id: f.attributes["OBJECTID"],
                ...f.attributes,
                geometry: f.geometry,
              };
            });

            resultsData[1].features[0].attributes.COUNT =
              resultsData[1].features[0].attributes.countResult;

            props.setOuterSearchResult({
              layerName: searchLayer,
              list: mappingRes,
              queryObj: paginationObj,
              statisticsInfo: resultsData[1].features[0].attributes,
            });

            if (res.length > 1) {
              props.generalOpenResultMenu();
            } else {
              props.outerOpenResultdetails(mappingRes[0]);
            }
          });
        } else {
          showLoading(false);
          setNoData(true);
          message.warning(t("common:noDataAvail"));
        }
      })
      .catch((err) => {
        showLoading(false);
        if (err?.response?.status === 401) {
          //logut
          message.error(t("common:sessionFinished"));
          localStorage.removeItem("user");
          localStorage.removeItem("token");
          window.open(window.hostURL + "/home/<USER>", "_self");
        } else message.error(t("common:retrievError"));
      });
  };

  const onChange = (e) => {
    setIsActiveBufferSearch(!isActiveBufferSearch);
  };
  const changeDate = (name, dateString) => {
    setFormValues({ ...formValues, [name]: dateString });
  };
  const onSearch = async (item, filterValue) => {
    let { mainData } = props;
    let layersSetting = mainData.layers;

    if (item.isServerSideSearch) {
      if (searchTimeOut.current) clearTimeout(searchTimeOut.current);

      searchTimeOut.current = setTimeout(async () => {
        setFetchingServerSearch(true);
        let filterQuery = [];
        Object.keys(formValues).forEach((key) => {
          if (
            formValues[key] &&
            key !== item.field &&
            key !== item.isServerSideSearch
          ) {
            filterQuery.push(key + "='" + formValues[key] + "'");
          }
        });

        if (filterValue) {
          filterQuery.push(item.field + " like '" + filterValue + "%'");
        }
        // todo: need to review
        //  else if (filterValue) {
        //   filterQuery.push(item.field + "='" + filterValue + "'");
        // }

        let layerdId = getLayerId(props.map.__mapInfo, searchLayer);

        filterQuery = filterQuery.join(" and ");
        queryTask({
          url: window.mapUrl + "/" + layerdId,
          where: filterQuery,
          outFields: [item.field],
          orderByFields: [item.field + " ASC"],
          returnDistinctValues: true,
          returnGeometry: false,
          callbackResult: ({ features }) => {
            let searchField = layersSetting[searchLayer].searchFields.find(
              (x) => x.field == item.field
            );

            if (features.length > 0) searchField.dataList = [...features];
            setSearchFields([
              ...layersSetting[searchLayer].searchFields.filter(
                (x) => !x.isSearch
              ),
            ]);
            setFetchingServerSearch(false);
          },
        });
      }, 500);
    }
  };

  const handleChangeDatePicker = (date, dateString, name) => {
    setShowInfo(false);
    setNoData(false);
    setFormValues({ ...formValues, [name]: dateString });
  };

  // helper func
  const mapResultWithDomain = (results, fieldsName, layerId) => {
    return new Promise((resolve, reject) => {
      let count = fieldsName.length;

      results.forEach((item, index) => {
        getFeatureDomainName(item.features, layerId).then((domainResult) => {
          item.features = domainResult;

          --count;
          if (count < 1) {
            resolve(results);
          }
        });
      });
    });
  };
  function getWhereClauseForZoom(item, formValues, searchField) {
    let whQuery = [];

    if (
      item.attributes[searchField.zoomLayer.filterField + "_Code"] ||
      item.attributes[searchField.zoomLayer.filterField]
    ) {
      return (
        searchField.zoomLayer.filterField +
        "=" +
        "'" +
        (item.attributes[searchField.zoomLayer.filterField + "_Code"] ||
          item.attributes[searchField.zoomLayer.filterField]) +
        "'"
      );
    } else {
      Object.keys(formValues).forEach((key) => {
        if (formValues[key]) whQuery.push(key + "='" + formValues[key] + "'");
      });
      return whQuery.join(" and ");
    }
  }
  const deleteChildValues = (name) => {
    let layersSetting = props.mainData.layers;
    let formValuesClone = { ...formValues };
    console.log("formValuesClone", formValuesClone);

    layersSetting[searchLayer].searchFields.forEach((item) => {
      if (item.isDate) {
        delete formValuesClone[item.field];
      }
    });

    return formValuesClone;
  };
  const showAlias = (item, isNotConfigLayer) => {
    const langAr = props.languageState === "ar";
    const isAliasIncludesArChar = (item?.alias).match(
      "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
    );
    if (isNotConfigLayer) {
      if (langAr && isAliasIncludesArChar) {
        return item.alias;
      } else if (langAr && !isAliasIncludesArChar)
        return t(`layers:${item.alias}`);
      else return t(`layers:${item.alias}`); //item.field;
    } else {
      if (langAr && isAliasIncludesArChar) {
        return item.alias;
      } else return t(`layers:${item.alias}`);
    }
  };

  const getLocaleObj = () => {
    let localeObj = { ...locale };
    localeObj.lang =
      i18n.language === "ar"
        ? {
            ...localeObj.lang,
            shortWeekDays: ["ح", "ن", "ث", "ع", "خ", "ج", "س"],

            shortMonths: [
              "يناير",
              "فبراير",
              "مارس",
              "أبريل",
              "مايو",
              "يونيو",
              "يوليو",
              "أغسطس",
              "سبتمبر",
              "أكتوبر",
              "نوفمبر",
              "ديسمبر",
            ],
          }
        : {
            ...localeObj.lang,
            shortWeekDays: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
            shortMonths: [
              "Jan",
              "Feb",
              "Mar",
              "Apr",
              "May",
              "Jun",
              "Jul",
              "Aug",
              "Sep",
              "Oct",
              "Nov",
              "Dec",
            ],
          };

    return localeObj;
  };

  // render
  let layersSetting = props.mainData.layers;
  let areMoreThenOneSearchInput = layersSetting[
    searchLayer
  ]?.searchFields.filter((x) => x.isSearch);
  const filterText =
    areMoreThenOneSearchInput && areMoreThenOneSearchInput?.length > 1
      ? searchLayer &&
        layersSetting[searchLayer]?.searchFields?.filter((x) => x.isSearch)
      : searchLayer &&
        layersSetting[searchLayer].searchFields.find((x) => x.isSearch);
  return (
    <div style={{ textAlign: "right" }}>
      {loading && <Loader />}
      <div
        style={{
          height: "calc(100vh - 235px)",
          overflow: "auto",
          padding: "5px",
        }}
      >
        <div style={{ display: "grid" }}>
          <label className="selectLabelStyle"> {t("common:searchLayer")}</label>
          <Select
            virtual={false}
            //mode="multiple"
            // suffixIcon={<DownCircleFilled />}
            suffixIcon={<RiArrowDropDownFill size={30} />}
            showSearch
            // allowClear
            className="dont-show"
            onChange={handleLayerSelect()}
            value={searchLayer}
            placeholder={t("common:searchLayerSelect")}
            getPopupContainer={(trigger) => trigger.parentNode}
            optionFilterProp="v"
            filterOption={(input, option) =>
              option.v && option.v.indexOf(input) >= 0
            }
          >
            {searchLayers &&
              searchLayers?.map((s, index) => (
                <Select.Option
                  key={index + "searchLay"}
                  v={s.name}
                  value={s.layerName}
                  id={s.layerName}
                >
                  {props?.languageState === "ar"
                    ? s?.layer?.arname || s?.layer?.name
                    : s?.layer?.englishName}
                </Select.Option>
              ))}
          </Select>

          {searchFields &&
            searchFields.map((item, index) => {
              return (
                <div style={{ display: "grid" }} key={index + "searchF"}>
                  {item.isDate ? (
                    <>
                      <label className="selectLabelStyle">
                        {showAlias(item, searchLayer?.notInConfig)}
                      </label>

                      <RangePicker
                        placeholder={[t("from"), t("to")]}
                        onChange={(dates, dateStrings) =>
                          changeDate(item.field, dateStrings)
                        }
                      />
                    </>
                  ) : item.dataList && item?.dataList?.length ? (
                    <>
                      <label className="selectLabelStyle">
                        {item?.required &&
                        formValues["MUNICIPALITY_NAME"] &&
                        !formValues["PLAN_NO"]
                          ? "* "
                          : ""}
                        {showAlias(
                          item,
                          layersSetting[searchLayer]?.notInConfig
                        )}
                      </label>

                      <Select
                        virtual={false}
                        mode="multiple"
                        // suffixIcon={<DownCircleFilled />}
                        suffixIcon={<RiArrowDropDownFill size={30} />}
                        // disabled={item.dataList && item.dataList.length == 0}
                        showSearch
                        // allowClear
                        notFoundContent={
                          fetchingServerSearch ? <Spin size="small" /> : null
                        }
                        onChange={selectChange(item.field, item)}
                        value={formValues[item.field]}
                        placeholder={showAlias(
                          item,
                          layersSetting[searchLayer]?.notInConfig
                        )}
                        onSearch={(e) => {
                          onSearch(item, e);
                          setTempState({ ...tempState, [item.field]: e });
                        }}
                        getPopupContainer={(trigger) => trigger.parentNode}
                        optionFilterProp="v"
                        onBlur={() => {
                          setTempState({
                            ...tempState,
                            [item.field]: undefined,
                          });
                        }}
                        filterOption={(input, option) => {
                          if (option.v) {
                            return (
                              convertToEnglish(option.v)
                                .toLowerCase()
                                .indexOf(input.toLowerCase()) >= 0
                            );
                          } else {
                            return false;
                          }
                        }}
                      >
                        {item.dataList &&
                          item.dataList
                            .filter((e, i) => {
                              if (tempState[item.field]) {
                                if (
                                  e.attributes[item.field] &&
                                  typeof e.attributes[item.field] === "string"
                                )
                                  return (
                                    e.attributes[item.field] &&
                                    e.attributes[item.field]
                                      .toLowerCase()
                                      .indexOf(
                                        tempState[item.field].toLowerCase()
                                      ) >= 0
                                  );
                                else
                                  return (
                                    e.attributes[item.field] &&
                                    String(e.attributes[item.field])
                                      .toLowerCase()
                                      .indexOf(
                                        String(
                                          tempState[item.field]
                                        ).toLowerCase()
                                      ) >= 0
                                  );
                              } else {
                                return i < 100 && e.attributes[item.field];
                              }
                            })
                            .slice(0, 50)
                            ?.map((m, i) => {
                              return (
                                <Select.Option
                                  key={item.field + i}
                                  v={m.attributes[item.field]}
                                  id={
                                    m.attributes[item.field + "_Code"] ||
                                    m.attributes[item.field]
                                  }
                                  value={
                                    m.attributes[item.field + "_Code"] ||
                                    m.attributes[item.field]
                                  }
                                >
                                  {convertToArabic(m.attributes[item.field])}
                                </Select.Option>
                              );
                            })}
                      </Select>
                    </>
                  ) : null}
                </div>
              );
            })}
        </div>

        {searchLayer && (
          <div>
            {filterText && !filterText?.length ? (
              <div style={{ display: "grid" }}>
                <label className="selectLabelStyle">
                  {filterText?.required &&
                  formValues["MUNICIPALITY_NAME"] &&
                  !formValues["PLAN_NO"]
                    ? "* "
                    : ""}{" "}
                  {showAlias(
                    filterText,
                    layersSetting[searchLayer]?.notInConfig
                  )}
                </label>

                <Input
                  name={filterText.field}
                  onChange={handleChangeInput}
                  value={formValues[filterText.field]}
                  placeholder={showAlias(
                    filterText,
                    layersSetting[searchLayer]?.notInConfig
                  )}
                />
              </div>
            ) : (
              filterText?.map((fText) => {
                return (
                  <div key={fText?.field} style={{ display: "grid" }}>
                    <label className="selectLabelStyle">
                      {filterText?.required ? "* " : ""}{" "}
                      {showAlias(
                        fText,
                        layersSetting[searchLayer]?.notInConfig
                      )}
                    </label>

                    {!fText.field?.includes("Date") ? (
                      <Input
                        name={fText.field}
                        onChange={handleChangeInput}
                        value={formValues[fText.field]}
                        placeholder={showAlias(
                          fText,
                          layersSetting[searchLayer]?.notInConfig
                        )}
                      />
                    ) : (
                      <DatePicker
                        onChange={(date, dateString) =>
                          handleChangeDatePicker(date, dateString, fText.field)
                        }
                        allowClear
                        name={fText.field}
                        placeholder={showAlias(
                          fText,
                          layersSetting[searchLayer]?.notInConfig
                        )}
                        format={"DD-MM-YYYY"}
                        locale={getLocaleObj()}
                      />
                    )}
                  </div>
                );
              })
            )}

            <div style={{ display: "grid" }}>
              <Checkbox
                style={{ marginTop: "20px", color: "#fff" }}
                checked={isActiveBufferSearch}
                onChange={onChange}
              >
                {t("common:geoSearch")}
              </Checkbox>

              {isActiveBufferSearch && (
                <div style={{ display: "grid" }}>
                  <label className="selectLabelStyle">
                    {t("common:distance")}
                  </label>

                  <Input
                    name="bufferDistance"
                    onChange={handleBufferSearch}
                    value={bufferDistance}
                    placeholder={t("common:distance2")}
                  />
                </div>
              )}

              <div className="searchInfoStyle">
                {showInfo && <p>{t("common:clickMapToSearch")}</p>}
                {noData && <p>{t("common:noDataAvail")}</p>}
              </div>
            </div>
          </div>
        )}
      </div>
      {searchLayer && (
        <div style={{ textAlign: "center" }}>
          <button
            onClick={searchForData}
            className="SearchBtn mt-3 w-25"
            size="large"
            htmlType="submit"
          >
            {t("common:search")}
          </button>
        </div>
      )}
    </div>
  );
};

export default FilterFuncComponent;
