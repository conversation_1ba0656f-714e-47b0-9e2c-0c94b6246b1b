// components/MapPreview.js
import React, { useEffect, useRef } from "react";
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";

const MapPreview = ({ featureServiceUrl }) => {
  const mapDivRef = useRef(null);

  useEffect(() => {
    if (!featureServiceUrl || !mapDivRef.current) return;

    const layer = new FeatureLayer({
      url: featureServiceUrl,
    });

    const map = new Map({
      basemap: "topo-vector",
      layers: [layer],
    });

    const view = new MapView({
      container: mapDivRef.current,
      map: map,
      zoom: 4,
      center: [30, 30],
      constraints: {
        rotationEnabled: false,
        minZoom: 4,
        maxZoom: 4,
      },
      ui: {
        components: [],
      },
      popup: null,
      highlightOptions: {
        fillOpacity: 0,
        color: [0, 0, 0, 0],
      },
    });

    view.when(() => {
      view.navigation.mouseWheelZoomEnabled = false;
      view.navigation.dragPanEnabled = false;
      view.navigation.keyboardNavigationEnabled = false;
      view.on("drag", (event) => event.stopPropagation());
      view.on("mouse-wheel", (event) => event.stopPropagation());
      view.on("double-click", (event) => event.stopPropagation());
      view.on("key-down", (event) => event.stopPropagation());
    });

    const attribution = view.ui.find("attribution");
    if (attribution) {
      view.ui.remove(attribution);
    }

    return () => {
      if (view) view.destroy();
    };
  }, [featureServiceUrl]);

  return (
    <div
      ref={mapDivRef}
      style={{
        height: "100px",
        borderRadius: "8px",
        width: "100%",
        overflow: "hidden",
      }}
    />
  );
};

export default MapPreview;
