import React from "react";

import FilterFuncComp from "./FilterComponent/FilterFuncComp";
// import FilterComponent from "./FilterComponent";
import GeneralResultDetails from "./generalSearchComponents/GeneralResultDetails";
import GeneralSearchResultMenu from "./generalSearchComponents/GeneralSearchResultMenu";
import eventBus from "../helper/EventBus";

export default function GeneralSearch(props) {
  React.useEffect(() => {
    console.log("mount general search");
    console.log({ props });

    eventBus.dispatch("openIdentify", { message: { show: true } });

    return () => {
      console.log("umnount general search", props.resultDetailsDataRef);
      const existedFeatureLayer = props.map.findLayerById("eco_system");
      if (existedFeatureLayer) {
        props.map.remove(existedFeatureLayer);
      }
      eventBus.dispatch("openIdentify", { message: { show: false } });
    };
  }, []);

  const generalOpenResultdetails = (data) => {
    props.resultDetailsDataRef.current.detailsData = data;
    props.generalOpenResultdetails();
  };

  return (
    <div className="coordinates mb-4 mt-2">
      <div style={{ padding: "5px" }}>
        <div
          style={{ display: props.generalSearchInputsShown ? "block" : "none" }}
        >
          <FilterFuncComp
            languageState={props.languageState}
            map={props.map}
            mainData={props.mainData}
            outerOpenResultMenu={props.outerOpenResultMenu}
            generalOpenResultMenu={props.generalOpenResultMenu}
            setNavRouteName={props.setNavRouteName}
            outerOpenResultdetails={generalOpenResultdetails}
            setOuterSearchResult={props.setOuterSearchResult}
          />
        </div>

        {props.generalResultMenuShown ? (
          <GeneralSearchResultMenu
            mainData={props.mainData}
            map={props.map}
            outerSearchResult={props.outerSearchResult}
            generalOpenSearchInputs={props.generalOpenSearchInputs}
            outerOpenResultdetails={generalOpenResultdetails}
            languageState={props.languageState}
            landBaseParcelData={
              props.resultDetailsDataRef.current.landBaseParcelData
            }
            // setLandBaseParcelData={props.setLandBaseParcelData}
          />
        ) : props.generalResultDetailsShown ? (
          <GeneralResultDetails
            mainData={props.mainData}
            map={props.map}
            data={props.resultDetailsDataRef.current.detailsData}
            outerSearchResult={props.outerSearchResult}
            generalOpenSearchInputs={props.generalOpenSearchInputs}
            generalOpenResultMenu={props.generalOpenResultMenu}
            outerOpenResultdetails={generalOpenResultdetails}
            setOuterSearchResult={props.setOuterSearchResult}
            languageState={props.languageState}
            resultDetailsDataRef={props.resultDetailsDataRef}
            // setLandBaseParcelData={props.setLandBaseParcelData}
          />
        ) : null}
      </div>
    </div>
  );
}
