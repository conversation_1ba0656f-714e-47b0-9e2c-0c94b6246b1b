import React, { useState, useEffect, useContext, useRef } from "react";
import { Input, Form, Tooltip } from "antd";
import nearbyIcon from "../assets/images/outerSearchIcon.svg";
import activeNearByIcon from "../assets/images/outerSearchActive.svg";
import { IoSearch } from "react-icons/io5";
import {
  addPictureSymbol,
  getFeatureDomainName,
  getLayerId,
  makeIdentify,
  queryTask,
  showLoading,
} from "../helper/common_func";
// import { layersSetting } from "../helper/layers";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Fade from "react-reveal";
let displayed = [];
export default function OuterSearchForm(props) {
  const { t, i18n } = useTranslation("common");
  const timeoutObj = {};
  const location = useLocation();
  const [displayedOuterSearch, setDisplayedOuterSearch] = useState([]);
  const [outerAutoComplete, setOuterComplete] = useState(true);
  const [outerSearchText, setFormValues] = useState("");
  const [isActiveBufferSearch, setActiveBufferSearch] = useState(false);

  const componentRef = useRef({});
  const formRef = useRef();
  const { current: timeout } = componentRef;
  // const selectedTabsIndex = ["PROTECTED_AREA_BOUNDARY"];
  const selectedTabsIndex = ["PROTECTED_AREA_BOUNDARY"];
  const searchfields =
    i18n.language === "ar"
      ? ["AR_PROTECTED_AREA_NAME", "AR_REGION"]
      : ["EN_PROTECTED_AREA_NAME", "EN_REGION"];

  let navigate = useNavigate();

  const handleOuterSearchText = (e) => {
    e.preventDefault();
    setOuterComplete(true);
    props.map.__selectedItem = null;
    let searchQuery = e.target.value.toLowerCase();
    //close the side menu, empty the search list, navigate to home
    if (searchQuery === "") {
      props.closeResultMenu();
      setDisplayedOuterSearch([]);
      navigate("/");
      setFormValues("");
    } else {
      setFormValues(e.target.value.toLowerCase());

      if (timeout.current) clearTimeout(timeout.current);
      timeout.current = setTimeout(() => {
        if (searchQuery.length >= 3) {
          getFilterUserSearchInput(searchQuery);
        }

        props.outerOpenResultMenu();
      }, 500);

      props.outerOpenResultMenu();
    }
  };

  const activeBufferSearch = () => {
    setActiveBufferSearch(!isActiveBufferSearch);
  };

  const getFilterUserSearchInput = (e) => {
    let layersId = [];
    let promiseQueries = [];
    selectedTabsIndex.forEach((layer, index) => {
      let layerdId = getLayerId(props.map.__mapInfo, layer);
      layersId.push(layerdId);
      let layersSetting = props.mainData.layers;
      promiseQueries.push(
        queryTask({
          url: window.mapUrl + "/" + layerdId,
          where: searchfields[index] + " Like '%" + e + "%'",
          outFields: layersSetting[layer].outFields,
          returnGeometry: true,
          start: 0,
          num: window.paginationCount,
          geometry: isActiveBufferSearch ? props.map.view.extent : null,
          returnExecuteObject: true,
        })
      );
    });

    showLoading(true);
    Promise.all(promiseQueries).then((resultsData) => {
      var allResult = [];
      let filteredResultLength = resultsData.filter(
        (f) => f.features.length
      ).length;

      if (filteredResultLength > 0) {
        resultsData.forEach((result, index) => {
          if (result.features.length > 0) {
            getFeatureDomainName(result.features, layersId[index]).then(
              (res) => {
                let mappingRes = res.map((f) => {
                  return {
                    layerName: selectedTabsIndex[index],
                    __filterDisplayField: searchfields[index],
                    id: f.attributes["OBJECTID"],
                    ...f.attributes,
                    geometry: f.geometry,
                  };
                });
                allResult = allResult.concat(mappingRes);
                filteredResultLength -= 1;
                if (filteredResultLength == 0) {
                  showLoading(false);
                  let searchQuery = e;
                  displayed = allResult.filter(function (el) {
                    var searchValue =
                      el[el["__filterDisplayField"]].toLowerCase();
                    return searchValue.indexOf(searchQuery) !== -1;
                  });
                  setDisplayedOuterSearch(displayed);
                  props.setFilteredResult(displayed);
                }
              }
            );
          }
        });
      } else {
        showLoading(false);
        setDisplayedOuterSearch([]);
        props.setFilteredResult([]);
        displayed = [];
      }
    });
  };

  useEffect(() => {
    setOuterComplete(false);
  }, [props.routeName]);

  const onEnterOuterSearch = (value) => {
    if (value) {
      if (value.layerName) {
        props.setFilteredResult([value]);
        //setFormValues(value["SRVC_NAME"]);
        formRef.current.setFieldsValue({ searchText: value["SRVC_NAME"] });
      } else if (value.target.value.trim().length > 2) {
        props.setFilteredResult(displayed);
      }
      navigate("/search");
      props.handleDrawerOpen();
      props.outerOpenResultMenu();
      setOuterComplete(false);
      setFormValues("");
    }
  };

  return (
    <div
      className="mapOuterSearch "
      style={{
        position: "absolute",
        right:
          i18n.language === "ar"
            ? props.openDrawer
              ? location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "generalSearch" ||
                location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "search" ||
                location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "coordinateSearch" ||
                location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "marsed"
                ? "370px"
                : location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) == "" ||
                  location.pathname.substring(
                    location.pathname.lastIndexOf("/")
                  ) == process.env.PUBLIC_URL ||
                  location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) === "metaDataSearch"
                ? "220px"
                : "390px"
              : "95px"
            : "unset",
        left:
          // i18n.language === "en"
          //   ? props.openDrawer
          //     ? displayedOuterSearch.length > 0 &&
          //       outerSearchText !== "" &&
          //       (location.pathname.substring(
          //         location.pathname.lastIndexOf("/") + 1
          //       ) === "generalSearch" ||
          //         location.pathname.substring(
          //           location.pathname.lastIndexOf("/") + 1
          //         ) === "search" ||
          //         location.pathname.substring(
          //           location.pathname.lastIndexOf("/") + 1
          //         ) === "coordinateSearch" ||
          //         location.pathname.substring(
          //           location.pathname.lastIndexOf("/") + 1
          //         ) === "marsed")
          //       ? "370px"
          //       : displayedOuterSearch.length == 0 &&
          //         outerSearchText == "" &&
          //         (location.pathname.substring(
          //           location.pathname.lastIndexOf("/") + 1
          //         ) === "generalSearch" ||
          //           location.pathname.substring(
          //             location.pathname.lastIndexOf("/") + 1
          //           ) === "search" ||
          //           location.pathname.substring(
          //             location.pathname.lastIndexOf("/") + 1
          //           ) === "coordinateSearch" ||
          //           location.pathname.substring(
          //             location.pathname.lastIndexOf("/") + 1
          //           ) === "marsed")
          //       ? "370px"
          //       : (displayedOuterSearch.length > 0 &&
          //           outerSearchText !== "" &&
          //           (location.pathname.substring(
          //             location.pathname.lastIndexOf("/") + 1
          //           ) == "" ||
          //             location.pathname == process.env.PUBLIC_URL)) ||
          //         location.pathname.substring(
          //           location.pathname.lastIndexOf("/") + 1
          //         ) === "metaDataSearch"
          //       ? "220px"
          //       : displayedOuterSearch.length == 0 &&
          //         outerSearchText == "" &&
          //         (location.pathname.substring(
          //           location.pathname.lastIndexOf("/") + 1
          //         ) || location.pathname == process.env.PUBLIC_URL) == ""
          //       ? "220px"
          //       : // :displayedOuterSearch.length > 0 &&outerSearchText !== ""? "400px"
          //         // :displayedOuterSearch.length == 0 &&outerSearchText == ""?"400px":"460px"
          //         "390px"
          //     : !props.openDrawer
          //     ? displayedOuterSearch.length > 0 && outerSearchText !== ""
          //       ? "150px"
          //       : "95px"
          //     : ""
          //   : "unset",
          i18n.language === "en"
            ? props.openDrawer
              ? location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "generalSearch" ||
                location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "search" ||
                location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "coordinateSearch" ||
                location.pathname.substring(
                  location.pathname.lastIndexOf("/") + 1
                ) === "marsed"
                ? "370px"
                : location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) == "" ||
                  location.pathname.substring(
                    location.pathname.lastIndexOf("/")
                  ) == process.env.PUBLIC_URL ||
                  location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) === "metaDataSearch"
                ? "220px"
                : "390px"
              : "95px"
            : "unset",
      }}
    >
      <Fade right delay={1000}>
        <div
          className="outerSearchForm"
          id={
            displayedOuterSearch.length > 0 && outerSearchText !== ""
              ? "outerFormWidthFit"
              : "outerFormWidth"
          }
        >
          <Form
            ref={formRef}
            className="GeneralForm"
            layout="vertical"
            name="validate_other"
          >
            <Form.Item
              name="searchText"
              className={`outerSearchInput ${
                outerSearchText ? "activeOuterSearchInput" : ""
              }`}
            >
              <Input
                placeholder={t("SearchForService")}
                allowClear
                value={outerSearchText}
                onPressEnter={onEnterOuterSearch}
                onChange={handleOuterSearchText}
                size="large"
                suffix={
                  i18n.language === "en" ? (
                    <Tooltip placement="bottom" title={t("outerActiveSearch")}>
                      <IoSearch
                        // color="#9D4223"
                        color="#fff"
                        // onClick={activeBufferSearch}
                        className="nearbyIcon outerSearchHelp"
                      />
                    </Tooltip>
                  ) : (
                    <Tooltip placement="bottom" title={t("outerActiveSearch")}>
                      <img
                        alt="nearbyIcon"
                        src={
                          isActiveBufferSearch ? activeNearByIcon : nearbyIcon
                        }
                        onClick={activeBufferSearch}
                        className="nearbyIcon"
                      />
                    </Tooltip>
                  )
                }
                prefix={
                  i18n.language === "ar" ? (
                    <IoSearch
                      color="#fff"
                      className="nearbyIcon outerSearchHelp"
                    />
                  ) : (
                    <img
                      alt="nearbyIcon"
                      src={isActiveBufferSearch ? activeNearByIcon : nearbyIcon}
                      className="nearbyIcon"
                      onClick={activeBufferSearch}
                    />
                  )
                }
              />
            </Form.Item>
            {outerSearchText !== "" &&
              displayedOuterSearch.length > 0 &&
              outerAutoComplete && (
                <div className="outerSearchAutoComplete">
                  {displayedOuterSearch
                    .slice(0, window.paginationCount)
                    .map((x) => (
                      <div
                        onClick={() => onEnterOuterSearch(x)}
                        style={{ display: "flex", overflowX: "hidden" }}
                      >
                        <label style={{ whiteSpace: "pre" }}>
                          {x[searchfields[0]] || x[searchfields[1]]}
                        </label>
                        <label className="searchLocationInfo">
                          , {x[searchfields[2]]} , {x[searchfields[3]]}
                        </label>
                      </div>
                    ))}
                </div>
              )}

            {outerSearchText.length >= 3 && 
              displayedOuterSearch.length === 0 &&
              outerAutoComplete && (
                <div
                  className="outerSearchAutoComplete"
                  style={{
                    color: "#fff",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "20px",
                  }}
                >
                  {t("no-results")}
                </div>
              )}
          </Form>
        </div>
      </Fade>
    </div>
  );
}
