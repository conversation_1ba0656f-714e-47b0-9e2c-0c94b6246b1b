export default function Legend({ legendList }) {
  return (
    <div className="legend">
      {legendList.map(({ image, label, icon }) => {
        return (
          <div
            style={{
              display: "flex",
              gap: "5px",
              alignItems: "center",
              marginBlock: "10px",
            }}
          >
            {icon ? <img src={icon} style={{ width: "20px" }} alt="" /> : <img src={"data:image/jpeg;base64," + image} alt="" />}
            <div style={{ textWrap: "wrap", textAlign: "right" }}>{label}</div>
          </div>
        );
      })}
    </div>
  );
}
