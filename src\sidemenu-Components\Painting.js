import React, { useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import { Button, Col, Form, Input, Row, message, Modal } from "antd";
import { Tooltip } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowsAltH,
  faBan,
  faChartLine,
  faCircle,
  faEraser,
  faInfo,
  faSquare,
  faTextWidth,
  faEdit,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";

import { CiBookmark } from "react-icons/ci";

import paint_saved from "../assets/images/paint_saved.svg";
import paint_tools from "../assets/images/paint_tools.svg";
import paint_text from "../assets/images/sidemenu/paint_text.svg";
import paint_point from "../assets/images/sidemenu/paint_point.svg";
import paint_arrow_range from "../assets/images/sidemenu/paint_arrow_range.svg";
import paint_stylus_note from "../assets/images/sidemenu/paint_stylus_note.svg";
import paint_circle from "../assets/images/sidemenu/paint_circle.svg";
import paint_hexagon from "../assets/images/sidemenu/paint_hexagon.svg";
import paint_pentagon from "../assets/images/sidemenu/paint_pentagon.svg";
import paint_crop_square from "../assets/images/sidemenu/paint_crop_square.svg";
import delete_icon from "../assets/images/sidemenu/delete.svg";

import Sketch from "@arcgis/core/widgets/Sketch";
import { drawText, zoomToFeatures } from "../helper/common_func";
import { useTranslation } from "react-i18next";
import Graphic from "@arcgis/core/Graphic";
import Point from "@arcgis/core/geometry/Point";
import Polyline from "@arcgis/core/geometry/Polyline";
import Polygon from "@arcgis/core/geometry/Polygon";
import SimpleMarkerSymbol from "@arcgis/core/symbols/SimpleMarkerSymbol";
import SimpleLineSymbol from "@arcgis/core/symbols/SimpleLineSymbol";
import SimpleFillSymbol from "@arcgis/core/symbols/SimpleFillSymbol";
import TextSymbol from "@arcgis/core/symbols/TextSymbol";
import axios from "axios";

export default function Painting(props) {
  const { t } = useTranslation("common");

  const componentRef = useRef({});
  const { current: sketch } = componentRef;
  const activeSketchName = useRef({});
  const textPointRef = useRef(null);
  const [drawingsObject, setDrawingsObject] = useState(null);
  const [drawingNames, setDrawingNames] = useState([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [deleteModalVisible, setdeleteModalVisible] = useState(false);
  const [activeEditName, setActiveEditName] = useState();
  const [activeDeleteName, setActiveDeleteName] = useState();
  const initialFormState = { paintName: "", editDrawingName: "" };
  const [formValues, setFormValues] = useState(initialFormState);
  const [isConfirmSaveEnabled, setIsConfirmSaveEnabled] = useState(false);
  const token = localStorage.getItem("token");

  useEffect(() => {
    if (!token) {
      const savedDrawings =
        JSON.parse(localStorage.getItem("drawingsObject")) || [];
      setDrawingsObject(savedDrawings);
    }

    sketch.current = new Sketch({
      layer: props.map.findLayerById("drawingGraphicLayer"),
      view: props.map.view,
    });

    sketch.current.on("create", (event) => {
      if (event.state === "complete") {
        if (activeSketchName.current == "pointText") {
          props.map.findLayerById("drawingGraphicLayer").remove(event.graphic);
          if (
            textPointRef.current.input.value &&
            textPointRef.current.input.value != ""
          ) {
            if (
              textPointRef.current.input.value.length >= 3 &&
              textPointRef.current.input.value.length <= 50
            ) {
              drawText(
                event.graphic,
                textPointRef.current.input.value,
                props.map,
                "drawingGraphicLayer"
              );
            } else {
              message.warning(t("textLengthValidationError"));
            }
          } else {
            message.warning(t("enterText"));
          }
        }
      }
    });

    return () => {
      // props.map.findLayerById("drawingGraphicLayer").graphics.removeAll();
      // props.map.view.goTo(window.__fullExtent);
      cancelDraw();
      sketch.current.destroy();
    };
  }, []);

  useEffect(() => {
    if (drawingsObject !== null) {
      if (typeof drawingsObject === "object") {
        setDrawingNames(Object.keys(drawingsObject));
      }
    }
    if (Array.isArray(drawingsObject)) {
      const drawingsNames = drawingsObject.map((drawing) => {
        return drawing.name;
      });
      setDrawingNames(drawingsNames);
    }
  }, [drawingsObject]);

  const addExtentToGraphicsData = (drawingData) => {
    const drawingDataInJson = JSON.parse(JSON.stringify(drawingData));
    for (let index = 0; index < drawingData.graphics.length; index++) {
      if (drawingData.graphics[index].geometry.extent) {
        drawingDataInJson.graphics[index].geometry.extent =
          drawingData.graphics[index].geometry.extent.clone();
      }
    }

    return drawingDataInJson;
  };

  const getAllDrawings = async () => {
    await axios
      .get(`${window.ApiUrl}Drawing/GetAll?pageSize=100`)
      .then((response) => {
        setDrawingsObject(response.data.results);
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  const postDrawing = async (drawingData) => {
    try {
      const drawingDataInJson = addExtentToGraphicsData(drawingData);
      const postUrl = `${window.ApiUrl}Drawing`;
      const data = {
        name: drawingDataInJson.name,
        graphics: JSON.stringify(drawingDataInJson.graphics),
      };
      const response = await axios.post(postUrl, data);
      const addedDrawing = response.data;
      setDrawingsObject((prevState) => {
        return [{ ...addedDrawing }, ...prevState];
      });

      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const deleteDrawing = async (drawingId) => {
    try {
      const url = `${window.ApiUrl}Drawing/${drawingId}`;
      axios.delete(url);
      return { success: true };
    } catch (error) {
      // message.warning(t("ErrorRequest"));
      return { success: false };
    }
  };

  const editDrawingName = async (drawingToEdit) => {
    try {
      const putUrl = `${window.ApiUrl}Drawing/${drawingToEdit.id}`;
      await axios.put(putUrl, drawingToEdit);
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const drawPolygon = () => {
    activeSketchName.current = "polygon";
    sketch.current.create("polygon");
  };

  const drawPolygonFreeHand = () => {
    activeSketchName.current = "polygon";
    sketch.current.create("polygon", { mode: "freehand" });
  };

  const drawPolyLineFreeHand = () => {
    activeSketchName.current = "polyline";
    sketch.current.create("polyline", { mode: "freehand" });
  };

  const drawPolyLine = () => {
    activeSketchName.current = "polyline";
    sketch.current.create("polyline");
  };

  const drawRectangle = () => {
    activeSketchName.current = "rectangle";
    sketch.current.create("rectangle");
  };

  const drawCircle = () => {
    activeSketchName.current = "circle";
    sketch.current.create("circle");
  };

  const drawPoint = () => {
    activeSketchName.current = "point";
    sketch.current.create("point");
  };

  const cancelDraw = () => {
    sketch.current.cancel();
  };

  const deleteFeature = () => {
    cancelDraw();
    sketch.current.delete();
  };

  const drawTextHandle = () => {
    sketch.current.create("point");
    activeSketchName.current = "pointText";
  };

  const saveDrawnGraphicsHandler = () => {
    // first save button
    if (props.map.findLayerById("drawingGraphicLayer").graphics.length > 0) {
      cancelDraw();
      setTabName("saved");
      setIsConfirmSaveEnabled(true);
    } else {
      message.warning(t("drawingIsRequired"));
    }
  };

  const saveDrawnGraphicsInLocalStorage = (drawingData) => {
    const dataWithExtent = addExtentToGraphicsData(drawingData);
    setDrawingsObject((prevDrawingsObject) => {
      let newDrawingsObject = {
        [dataWithExtent.name]: dataWithExtent.graphics,
        ...prevDrawingsObject,
      };
      localStorage.setItem("drawingsObject", JSON.stringify(newDrawingsObject));
      // setDrawingNames(Object.keys(newDrawingsObject));
      return newDrawingsObject;
    });
  };

  const saveDrawingHandler = async () => {
    if (formValues.paintName && formValues.paintName != "") {
      if (
        formValues.paintName &&
        (formValues.paintName.length < 3 || formValues.paintName.length > 50)
      ) {
        message.warning(t("textLengthValidationError"));
        return;
      }
      if (
        (typeof drawingsObject === "object" &&
          drawingsObject[formValues.paintName]) ||
        (Array.isArray(drawingsObject) &&
          drawingsObject.find(
            (drawing) => drawing.name == formValues.paintName
          ))
      ) {
        message.warning(t("NewNameRequired"));
      } else {
        if (
          props.map.findLayerById("drawingGraphicLayer").graphics.length > 0
        ) {
          let graphicItems = props.map.findLayerById("drawingGraphicLayer")
            .graphics.items;
          let drawingData = {
            name: formValues.paintName,
            graphics: graphicItems,
          };
          if (token) {
            const result = await postDrawing(drawingData);
            if (!result.success) {
              message.warning(t("ErrorRequest"));
            }
          } else {
            saveDrawnGraphicsInLocalStorage(drawingData);
          }
          setFormValues(initialFormState);
          setIsConfirmSaveEnabled(false);
          message.warning(t("drawingSavedSuccessfully"));
        } else {
          message.warning(t("drawingIsRequired"));
        }
      }
    } else {
      message.warning(t("drawNameRequired"));
    }
  };

  const createSymbol = (data) => {
    let symbol;
    switch (data.symbol.type) {
      case "esriSMS":
        symbol = new SimpleMarkerSymbol({
          color: data.symbol.color == null ? null : [...data.symbol.color],
          size: data.symbol.size,
          style: data.symbol.style.replace("esriSMS", "").toLowerCase(),
          outline: {
            color:
              data.symbol.outline.color == null
                ? null
                : [...data.symbol.outline.color],
            width: data.symbol.outline.width,
          },
        });
        break;
      case "esriSLS":
        symbol = new SimpleLineSymbol({
          color: data.symbol.color == null ? null : [...data.symbol.color],
          width: data.symbol.width,
          style: data.symbol.style.replace("esriSLS", "").toLowerCase(),
        });
        break;
      case "esriSFS":
        symbol = new SimpleFillSymbol({
          color: {
            r: data.symbol.color[0],
            g: data.symbol.color[1],
            b: data.symbol.color[2],
            a: data.symbol.color[3] / 255,
            isBright: true,
          },
          style: data.symbol.style.replace("esriSFS", "").toLowerCase(),
          outline: {
            color:
              data.symbol.outline.color == null
                ? null
                : [...data.symbol.outline.color],
            width: data.symbol.outline.width,
          },
        });
        break;
      case "esriTS":
        symbol = new TextSymbol({
          color: data.symbol.color == null ? null : [...data.symbol.color],
          text: data.symbol.text,
          font: {
            family: data.symbol.font.family,
            size: data.symbol.font.size,
            weight: data.symbol.font.weight,
          },
          haloColor:
            data.symbol.haloColor == null ? null : [...data.symbol.haloColor],
          haloSize: data.symbol.haloSize,
          horizontalAlignment: data.symbol.horizontalAlignment,
          xoffset: data.symbol.xoffset,
          yoffset: data.symbol.yoffset,
          angle: data.symbol.angle,
          rotated: data.symbol.rotated,
        });
        break;
      default:
        return null;
    }
    return symbol;
  };

  const createGeometry = (data) => {
    let geometry;
    if (data.geometry.x !== undefined && data.geometry.y !== undefined) {
      // Create a Point
      geometry = new Point(data.geometry);
    } else if (data.geometry.paths) {
      // Create a Polyline
      geometry = new Polyline(data.geometry);
    } else if (data.geometry.rings) {
      // Create a Polygon
      geometry = new Polygon(data.geometry);
    }
    return geometry;
  };

  const loadDrawingHandler = (e) => {
    let drawingName = e.target.innerText;
    props.map.findLayerById("drawingGraphicLayer").graphics.removeAll();

    if (token) {
      const drawing = drawingsObject.find(
        (drawing) => drawing.name == drawingName
      );
      const drawingGraphics = JSON.parse(drawing.graphics);
      drawingGraphics.forEach((data) => {
        let geometry = createGeometry(data);
        let symbol = createSymbol(data);

        let graphic = new Graphic({
          geometry: geometry,
          symbol: symbol,
          attributes: data.attributes,
          popupTemplate: data.popupTemplate,
        });
        props.map.findLayerById("drawingGraphicLayer").graphics.add(graphic);
      });
    } else {
      drawingsObject[drawingName].forEach((data) => {
        let geometry = createGeometry(data);
        let symbol = createSymbol(data);

        let graphic = new Graphic({
          geometry: geometry,
          symbol: symbol,
          attributes: data.attributes,
          popupTemplate: data.popupTemplate,
        });
        props.map.findLayerById("drawingGraphicLayer").graphics.add(graphic);
      });
    }
    zoomToFeatures(
      props.map.findLayerById("drawingGraphicLayer").graphics.items,
      props.map
    );
  };

  const showEdit = (drawingName) => {
    setActiveEditName(drawingName);
    setEditModalVisible(true);
  };

  const SubmitEditName = async () => {
    let otherDrawingsNames = drawingNames.filter(
      (name) => name != activeEditName
    );
    if (
      formValues.editDrawingName == undefined ||
      formValues.editDrawingName == ""
    ) {
      message.warning(t("NewNameEmpty"));
    } else if (otherDrawingsNames.includes(formValues.editDrawingName)) {
      message.warning(t("NewNameRequired"));
    } else if (formValues.editDrawingName == activeEditName) {
      message.warning(t("SameOldNewName"));
    } else {
      if (token) {
        const drawingToEdit = drawingsObject.find(
          (drawing) => drawing.name == activeEditName
        );
        drawingToEdit.name = formValues.editDrawingName;
        const result = await editDrawingName(drawingToEdit);
        if (result.success) {
          const otherDrawings = drawingsObject.filter(
            (drawing) => drawing.name != activeEditName
          );
          setDrawingsObject([...otherDrawings]);
          drawingEditedSuccessfully();
        } else {
          message.warning(t("ErrorRequest"));
        }
      } else {
        const updatedDrawings = JSON.parse(JSON.stringify(drawingsObject));
        updatedDrawings[formValues.editDrawingName] = JSON.parse(
          JSON.stringify(updatedDrawings[activeEditName])
        );
        delete updatedDrawings[activeEditName];
        localStorage.setItem("drawingsObject", JSON.stringify(updatedDrawings));
        setDrawingsObject(updatedDrawings);
        drawingEditedSuccessfully();
      }
      // setDrawingNames(Object.keys(drawingsObject));
    }
  };

  const handleChangeInput = (e) => {
    //setNewDrawingName(e.target.value);
    setFormValues({ ...formValues, [e.target.name]: e.target.value });
  };

  const deleteDrawingHandler = (drawingName) => {
    setActiveDeleteName(drawingName);
    setdeleteModalVisible(true);
  };

  const submitDeleteDrawing = async () => {
    if (token) {
      let drawingToDelete = drawingsObject.find(
        (drawing) => drawing.name == activeDeleteName
      );
      const result = await deleteDrawing(drawingToDelete.id);
      if (result.success) {
        const otherDrawings = drawingsObject.filter(
          (drawing) => drawing.id != drawingToDelete.id
        );
        setDrawingsObject(otherDrawings);
        drawingDeletedSuccessfully();
      } else {
        message.warning(t("ErrorRequest"));
        return;
      }
    } else {
      const updatedDrawings = JSON.parse(JSON.stringify(drawingsObject));
      delete updatedDrawings[activeDeleteName];
      localStorage.setItem("drawingsObject", JSON.stringify(updatedDrawings));
      setDrawingsObject(updatedDrawings);
      drawingDeletedSuccessfully();
    }
  };

  const drawingDeletedSuccessfully = () => {
    props.map.findLayerById("drawingGraphicLayer").graphics.removeAll();
    setActiveDeleteName();
    setdeleteModalVisible(false);
    message.warning(t("drawingDeletedSuccessfully"));
  };

  const drawingEditedSuccessfully = () => {
    setEditModalVisible(false);
    setFormValues(initialFormState);
    message.warning(t("drawingNamedEditSuccessfully"));
  };

  const [tabName, setTabName] = useState("tools");

  useEffect(() => {
    if (tabName == "saved" && !drawingsObject) {
      getAllDrawings();
    }
  }, [tabName]);

  return (
    // <div className="coordinates mb-4 mt-2 painting">
    <div className="mb-4 mt-2 painting">
      <Container>
        <div style={{ display: "flex", gap: "10px", marginBlock: "10px" }}>
          <Tooltip placement="top" title={t("paintTools")}>
            <Button
              className="paintBtn"
              onClick={() => {
                props.map
                  .findLayerById("drawingGraphicLayer")
                  .graphics.removeAll();
                setTabName("tools");
              }}
              id={tabName === "tools" ? "activePaintBtn" : ""}
            >
              <img src={paint_tools} alt="paint_tools" />
              <span>{t("paintTools")}</span>
            </Button>
          </Tooltip>

          <Tooltip placement="top" title={t("savedPaint")}>
            <Button
              className="paintBtn"
              onClick={() => {
                cancelDraw();
                setTabName("saved");
              }}
              id={tabName === "saved" ? "activePaintBtn" : ""}
            >
              <img src={paint_saved} alt="paint_saved" />
              <span>{t("savedPaint")}</span>
            </Button>
          </Tooltip>
        </div>

        {tabName === "tools" && (
          <div>
            <ul>
              <Tooltip placement="left" title={t("clickWordText")}>
                <li onClick={drawTextHandle}>
                  {/* <FontAwesomeIcon icon={faTextWidth} className="ml-2" /> */}
                  <img src={paint_text} alt="paint_text" />
                  {t("text")}
                </li>
              </Tooltip>
              <Form.Item name="streetName">
                <Input
                  name="streetName"
                  // onChange={handleChangeInput}
                  //value={formValues.streetName}
                  placeholder={t("enterText2")}
                  ref={textPointRef}
                />
              </Form.Item>

              <Tooltip placement="left" title={t("clickMapDrawPoint")}>
                <li onClick={drawPoint}>
                  {/* <FontAwesomeIcon icon={faInfo} className="ml-2" /> */}
                  <img src={paint_point} alt="paint_point" />
                  {t("point")}
                </li>
              </Tooltip>

              <Tooltip placement="left" title={t("clickMapSolidLines")}>
                <li onClick={drawPolyLine}>
                  {/* <FontAwesomeIcon icon={faArrowsAltH} className="ml-2" /> */}
                  <img src={paint_arrow_range} alt="paint_arrow_range" />
                  {t("solidLines")}
                </li>
              </Tooltip>
              <Tooltip placement="left" title={t("clickDrawPoly")}>
                <li onClick={drawPolyLineFreeHand}>
                  {/* <FontAwesomeIcon icon={faChartLine} className="ml-2" /> */}
                  <img src={paint_stylus_note} alt="paint_stylus_note" />
                  {t("disConnectLines")}{" "}
                </li>
              </Tooltip>

              <Tooltip placement="left" title={t("clickMapRec")}>
                <li onClick={drawRectangle}>
                  {/* <FontAwesomeIcon icon={faSquare} className="ml-2" /> */}
                  <img src={paint_crop_square} alt="paint_crop_square" />
                  {t("rectangle")}
                </li>
              </Tooltip>
              <Tooltip placement="left" title={t("clickMapCirc")}>
                <li onClick={drawCircle}>
                  {/* <FontAwesomeIcon icon={faCircle} className="ml-2" /> */}
                  <img src={paint_circle} alt="paint_circle" />
                  {t("circle")}
                </li>
              </Tooltip>

              <Tooltip placement="left" title={t("clickDrawPoly")}>
                <li onClick={drawPolygon}>
                  {/* <FontAwesomeIcon icon={faSquare} className="ml-2" /> */}
                  <img src={paint_pentagon} alt="paint_pentagon" />
                  {t("polygon")}
                </li>
              </Tooltip>
              <Tooltip placement="left" title={t("clickMapDrag")}>
                <li onClick={drawPolygonFreeHand}>
                  {/* <FontAwesomeIcon icon={faSquare} className="ml-2" /> */}
                  <img src={paint_hexagon} alt="paint_hexagon" />
                  {t("freePolyg")}
                </li>
              </Tooltip>
              <Tooltip
                placement="left"
                title={t("clickDrawToDelete")}
                // {t("enteronElementToDelete")}
              >
                <li onClick={deleteFeature}>
                  {/* <FontAwesomeIcon icon={faEraser} className="ml-2" /> */}
                  <img src={delete_icon} alt="delete_icon" />
                  {t("deletePaintElemnt")}
                </li>
              </Tooltip>

              {
                <Tooltip placement="left" title={t("clickToStopPaint")}>
                  <li onClick={cancelDraw}>
                    <FontAwesomeIcon icon={faBan} className="ml-2" />
                    {t("stopPaint")}
                  </li>
                </Tooltip>
              }
            </ul>

            <button
              className="SearchBtn"
              size="large"
              htmlType="submit"
              style={{ marginBlock: "10px" }}
              onClick={saveDrawnGraphicsHandler}
            >
              {t("save")}
            </button>
          </div>
        )}

        {tabName === "saved" && (
          <div>
            <Form.Item>
              <Input
                name="paintName"
                value={formValues.paintName}
                onChange={handleChangeInput}
                // ref={drawNameRef}
                placeholder={t("paintName")}
              />
            </Form.Item>

            <button
              className="SearchBtn"
              size="large"
              htmlType="submit"
              style={{ marginBlock: "10px" }}
              onClick={saveDrawingHandler}
              disabled={!isConfirmSaveEnabled}
            >
              {t("ConfirmSave")}
            </button>

            {drawingNames &&
              drawingNames.map((drawingName, indx) => {
                return (
                  <div
                    className="generalSearchCard"
                    style={{ padding: "15px 5px", marginInline: "0" }}
                    key={indx}
                  >
                    <Row
                      className="bookmarkRowEnglish"
                      style={{ alignItems: "center" }}
                    >
                      <Col span={3}>
                        <CiBookmark
                          className="starMark"
                          // style={
                          //   activeBookMark == index
                          //     ? { color: "#0a8eb9" }
                          //     : { color: "#707070" }
                          // }
                        />
                      </Col>
                      <Col
                        span={14}
                        style={{
                          whiteSpace: "break-spaces",
                          wordBreak: "break-word",
                          paddingRight: "5px",
                        }}
                        onClick={loadDrawingHandler}
                      >
                        <p style={{ margin: "10px" }}>{drawingName}</p>
                      </Col>
                      <Col span={7} className="bookmarkColRight">
                        <FontAwesomeIcon
                          icon={faEdit}
                          className="mx-2"
                          style={{ color: "#fff" }}
                          onClick={() => {
                            showEdit(drawingName);
                          }}
                        />
                        <FontAwesomeIcon
                          style={{ color: "#fff" }}
                          icon={faTrash}
                          className="mx-2"
                          onClick={() => deleteDrawingHandler(drawingName)}
                        />
                      </Col>
                    </Row>
                  </div>
                );
              })}
          </div>
        )}
      </Container>
      <Modal
        title={t("deletionConfirmation") + activeDeleteName}
        centered
        visible={deleteModalVisible}
        //onOk={() => afterEditModal()}
        onCancel={() => setdeleteModalVisible(false)}
        okText={t("yes")}
        cancelText={t("no")}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            justifyContent: "end",
          }}
        >
          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => {
              submitDeleteDrawing();
            }}
          >
            {t("yes")}
          </Button>

          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => setdeleteModalVisible(false)}
          >
            {t("no")}
          </Button>
        </div>
      </Modal>
      <Modal
        title={t("editDrawingName") + ": " + activeEditName}
        centered
        visible={editModalVisible}
        //onOk={() => afterEditModal()}
        onCancel={() => setEditModalVisible(false)}
        okText={t("edit")}
        cancelText={t("cancel")}
      >
        <Input
          name="editDrawingName"
          onChange={handleChangeInput}
          value={formValues.editDrawingName}
          placeholder={t("DrawingName")}
        />

        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            justifyContent: "end",
          }}
        >
          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => {
              SubmitEditName();
            }}
          >
            {t("edit")}
          </Button>

          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => setEditModalVisible(false)}
          >
            {t("close")}
          </Button>
        </div>
      </Modal>
    </div>
  );
}
