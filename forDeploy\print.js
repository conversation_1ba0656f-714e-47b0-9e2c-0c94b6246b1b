/*
All material copyright ESRI, All Rights Reserved, unless otherwise specified.
See https://js.arcgis.com/4.21/esri/copyright.txt for details.
*/
import e from "../config.js";
import {
    id as t
} from "../kernel.js";
import i from "../request.js";
import r from "../core/Error.js";
import {
    JSONMap as a
} from "../core/jsonMap.js";
import {
    isSome as n
} from "../core/maybe.js";
import {
    createScreenPoint as s,
    pt2px as o,
    px2pt as l
} from "../core/screenUtils.js";
import {
    normalize as c,
    dataComponents as u
} from "../core/urlUtils.js";
import y from "../geometry/Polygon.js";
import {
    getSizeRangeAtScale as f
} from "../renderers/visualVariables/support/visualVariableUtils.js";
import {
    execute as m
} from "./geoprocessor/execute.js";
import {
    submitJob as p
} from "./geoprocessor/submitJob.js";
import {
    toJSON as d
} from "./support/fileFormat.js";
import {
    toJSON as g
} from "./support/layoutTemplate.js";
import {
    getVisibleLayerViews as h,
    isGroupLayer as b,
    isBingMapsLayer as w,
    isCSVLayer as S,
    isFeatureLayer as x,
    isGeoJSONLayer as v,
    isGraphicsLayer as D,
    isImageryLayer as I,
    isKMLLayer as L,
    isMapImageLayer as T,
    isMapNotesLayer as V,
    isOpenStreetMapLayer as M,
    isStreamLayer as O,
    isTileLayer as E,
    isVectorTileLayer as P,
    isWebTileLayer as F,
    isWMSLayer as R,
    isWMTSLayer as k,
    applyVisualVariables as N,
    getContextBoundingBox as J,
    createPolygonLayer as U,
    createPolylineLayer as C,
    createPointLayer as j,
    createMultipointLayer as A
} from "./support/printTaskUtils.js";
import z from "./support/PrintTemplate.js";
import {
    supportsApiKey as $
} from "../support/apiKeyUtils.js";
import {
    getFloorFilterClause as K
} from "../views/support/floorFilterUtils.js";
const _ = {
        Feet: "ft",
        Kilometers: "km",
        Meters: "m",
        Miles: "mi"
    },
    q = new a({
        esriFeet: "Feet",
        esriKilometers: "Kilometers",
        esriMeters: "Meters",
        esriMiles: "Miles"
    }),
    W = new a({
        esriExecutionTypeSynchronous: "sync",
        esriExecutionTypeAsynchronous: "async"
    }),
    B = new Map;
async function Q(e, t, r) {
    const a = Y(e);
    let n = B.get(a);
    return Promise.resolve().then((() => n ? {
        data: n.gpMetadata
    } : (n = {
        gpServerUrl: a,
        is11xService: !1,
        legendLayerNameMap: [],
        legendLayers: []
    }, i(a, {
        query: {
            f: "json"
        }
    })))).then((e => (n.gpMetadata = e.data, n.cimVersion = n.gpMetadata.cimVersion, n.is11xService = !!n.cimVersion, B.set(a, n), H(t, n)))).then((i => {
        const a = ve(n);
        let s;
        const o = e => "sync" === a ? e.results && e.results[0] && e.results[0].value : s.fetchResultData("Output_File", null, r).then((e => e.value));
        return "async" === a ? p(e, i, null, r).then((e => (s = e, e.waitForJobCompletion({
            interval: t.updateDelay
        }).then(o)))) : m(e, i, null, r).then(o)
    }))
}
async function G(e) {
    const t = Y(e);
    return ve(B.get(t))
}
async function H(e, t) {
    t = t || {
        is11xService: !1,
        legendLayerNameMap: [],
        legendLayers: []
    };
    const i = e.template || new z;
    null == i.showLabels && (i.showLabels = !0);
    const a = i.exportOptions;
    let n;
    const s = g(i.layout);
    if (a) {
        if (n = {
                dpi: a.dpi
            }, "map_only" === s.toLowerCase() || "" === s) {
            const e = a.width,
                t = a.height;
            n.outputSize = [e, t]
        }
    }
    const o = i.layoutOptions;
    let l;
    if (o) {
        let e, t;
        "Miles" === o.scalebarUnit || "Kilometers" === o.scalebarUnit ? (e = "Kilometers", t = "Miles") : "Meters" !== o.scalebarUnit && "Feet" !== o.scalebarUnit || (e = "Meters", t = "Feet"), l = {
            titleText: o.titleText,
            authorText: o.authorText,
            copyrightText: o.copyrightText,
            customTextElements: o.customTextElements,
            scaleBarOptions: {
                metricUnit: q.toJSON(e),
                metricLabel: _[e],
                nonMetricUnit: q.toJSON(t),
                nonMetricLabel: _[t]
            }
        }
    }
    let c = null;
    o && o.legendLayers && (c = o.legendLayers.map((e => {
        t.legendLayerNameMap[e.layerId] = e.title;
        const i = {
            id: e.layerId
        };
        return e.subLayerIds && (i.subLayerIds = e.subLayerIds), i
    })));
    const u = await X(e, i, t);
    if (u.operationalLayers) {
        const e = new RegExp("[\\u4E00-\\u9FFF\\u0E00-\\u0E7F\\u0900-\\u097F\\u3040-\\u309F\\u30A0-\\u30FF\\u31F0-\\u31FF]"),
            i = /[\u0600-\u06FF]/,
            a = t => {
                const r = t.text,
                    a = t.font,
                    n = a && a.family && a.family.toLowerCase();
                r && a && ("arial" === n || "arial unicode ms" === n) && (a.family = e.test(r) ? "Arial Unicode MS" : "Arial", "normal" !== a.style && i.test(r) && (a.family = "Arial Unicode MS"))
            },
            n = () => {
                throw new r("print-task:cim-symbol-unsupported", "CIMSymbol is not supported by a print service published from ArcMap")
            };
        u.operationalLayers.forEach((e => {
            var i, r, s;
            null != (i = e.featureCollection) && i.layers ? e.featureCollection.layers.forEach((e => {
                var i, r, s, o;
                if (null != (i = e.layerDefinition) && null != (r = i.drawingInfo) && null != (s = r.renderer) && s.symbol) {
                    const i = e.layerDefinition.drawingInfo.renderer;
                    "esriTS" === i.symbol.type ? a(i.symbol) : "CIMSymbolReference" !== i.symbol.type || t.is11xService || n()
                }
                null != (o = e.featureSet) && o.features && e.featureSet.features.forEach((e => {
                    e.symbol && ("esriTS" === e.symbol.type ? a(e.symbol) : "CIMSymbolReference" !== e.symbol.type || t.is11xService || n())
                }))
            })) : !t.is11xService && null != (r = e.layerDefinition) && null != (s = r.drawingInfo) && s.renderer && JSON.stringify(e.layerDefinition.drawingInfo.renderer).includes('"type":"CIMSymbolReference"') && n()
        }))
    }
    debugger
    e.outSpatialReference && (u.mapOptions.spatialReference = e.outSpatialReference.toJSON()), Object.assign(u, {
        exportOptions: n,
        layoutOptions: l || {}
    }), Object.assign(u.layoutOptions, {
        legendOptions: {
            operationalLayers: null != c ? c : t.legendLayers.slice()
        }
    }), t.legendLayers.length = 0, B.set(t.gpServerUrl, t);

    if(u.layoutOptions && u.layoutOptions.legendOptions && u.layoutOptions.legendOptions.operationalLayers.length && u.layoutOptions.legendOptions.operationalLayers[0].subLayerIds && window.__neglectLegendLayers)
    {
        u.layoutOptions.legendOptions.operationalLayers[0].subLayerIds = u.layoutOptions.legendOptions.operationalLayers[0].subLayerIds.filter(item => !window.__neglectLegendLayers.includes(item));
    }

    const y = {
        Web_Map_as_JSON: JSON.stringify(u),
        Format: d(i.format),
        Layout_Template: s
    };
    return e.extraParameters && Object.assign(y, e.extraParameters), y
}
async function X(e, t, i) {
    const r = e.view;
    let a = r.spatialReference;
    const s = {
        operationalLayers: await Z(r, t, i)
    };
    let o = i.ssExtent || e.extent || r.extent;
    if (a && a.isWrappable && (o = o.clone()._normalize(!0), a = o.spatialReference), s.mapOptions = {
            extent: o && o.toJSON(),
            spatialReference: a && a.toJSON(),
            showAttribution: t.attributionVisible
        }, i.ssExtent = null, r.background && (s.background = r.background.toJSON()), r.rotation && (s.mapOptions.rotation = -r.rotation), t.scalePreserved && (s.mapOptions.scale = t.outScale || r.scale), r.timeExtent) {
        const e = n(r.timeExtent.start) ? r.timeExtent.start.getTime() : null,
            t = n(r.timeExtent.end) ? r.timeExtent.end.getTime() : null;
        s.mapOptions.time = [e, t]
    }
    return s
}

function Y(e) {
    let t = e;
    const i = t.lastIndexOf("/GPServer/");
    return i > 0 && (t = t.slice(0, i + 9)), t
}
async function Z(e, t, i) {
    const r = [],
        a = {
            layerView: null,
            printTemplate: t,
            view: e
        };
    let n = 0;
    t.scalePreserved && (n = t.outScale || e.scale);
    const s = h(e, n);
    for (const o of s) {
        const e = o.layer;
        if (!e.loaded || b(e)) continue;
        let t;
        a.layerView = o, t = w(e) ? ee(e) : S(e) ? await te(e, a, i) : x(e) ? await re(e, a, i) : v(e) ? await ae(e, a, i) : D(e) ? await ne(e, a, i) : I(e) ? se(e, i) : L(e) ? await oe(e, a, i) : T(e) ? le(e, a, i) : V(e) ? await ce(a, i) : M(e) ? ue() : O(e) ? await fe(e, a, i) : E(e) ? me(e) : P(e) ? await pe(e, a, i) : F(e) ? de(e) : R(e) ? ge(e) : k(e) ? he(e) : await ye(e, a, i), t && (Array.isArray(t) ? r.push(...t) : (t.id = e.id, t.title = i.legendLayerNameMap[e.id] || e.title, t.opacity = e.opacity, t.minScale = e.minScale || 0, t.maxScale = e.maxScale || 0, r.push(t)))
    }
    if (n && r.forEach((e => {
            e.minScale = 0, e.maxScale = 0
        })), e.graphics && e.graphics.length) {
        const a = await ie(null, e.graphics, t, i);
        a && r.push(a)
    }
    return r
}

function ee(e) {
    return {
        culture: e.culture,
        key: e.key,
        type: "BingMaps" + ("aerial" === e.style ? "Aerial" : "hybrid" === e.style ? "Hybrid" : "Road")
    }
}
async function te(e, t, i) {
    i.legendLayers && i.legendLayers.push({
        id: e.id
    });
    const r = t.layerView,
        a = t.printTemplate;
    let n;
    if (!i.is11xService || r.filter) {
        return ie(e, await xe(r), a, i)
    }
    return n = {
        type: "CSV"
    }, e.write(n, {
        origin: "web-map"
    }), delete n.popupInfo, delete n.layerType, n.showLabels = a.showLabels && e.labelsVisible, n
}
async function ie(e, t, i, r) {
    let a;
    const n = U(),
        s = C(),
        o = j(),
        l = A(),
        c = j();
    if (c.layerDefinition.name = "textLayer", delete c.layerDefinition.drawingInfo, e) {
        if ("esri.layers.FeatureLayer" === e.declaredClass || "esri.layers.StreamLayer" === e.declaredClass ? n.layerDefinition.name = s.layerDefinition.name = o.layerDefinition.name = l.layerDefinition.name = r.legendLayerNameMap[e.id] || e.get("arcgisProps.title") || e.title : "esri.layers.GraphicsLayer" === e.declaredClass && (t = e.graphics.items), e.renderer) {
            const t = e.renderer.toJSON();
            n.layerDefinition.drawingInfo.renderer = t, s.layerDefinition.drawingInfo.renderer = t, o.layerDefinition.drawingInfo.renderer = t, l.layerDefinition.drawingInfo.renderer = t
        }
        if (i.showLabels && e.labelsVisible && "function" == typeof e.write) {
            var u, f;
            const t = null == (u = e.write({}, {
                origin: "web-map"
            }).layerDefinition) || null == (f = u.drawingInfo) ? void 0 : f.labelingInfo;
            t && (a = !0, n.layerDefinition.drawingInfo.labelingInfo = t, s.layerDefinition.drawingInfo.labelingInfo = t, o.layerDefinition.drawingInfo.labelingInfo = t, l.layerDefinition.drawingInfo.labelingInfo = t)
        }
    }
    let m;
    null != e && e.renderer || a || (delete n.layerDefinition.drawingInfo, delete s.layerDefinition.drawingInfo, delete o.layerDefinition.drawingInfo, delete l.layerDefinition.drawingInfo);
    const p = null == e ? void 0 : e.fieldsIndex,
        d = null == e ? void 0 : e.renderer;
    if (p) {
        if (d && "function" == typeof d.collectRequiredFields) {
            const e = new Set;
            await d.collectRequiredFields(e, p), m = Array.from(e)
        }
        const e = p.fields.map((e => e.toJSON()));
        n.layerDefinition.fields = e, s.layerDefinition.fields = e, o.layerDefinition.fields = e, l.layerDefinition.fields = e
    }
    const g = t && t.length;
    let h;
    for (let w = 0; w < g; w++) {
        const a = t[w] || t.getItemAt(w);
        if (!1 === a.visible || !a.geometry) continue;
        if (h = a.toJSON(), h.hasOwnProperty("popupTemplate") && delete h.popupTemplate, h.geometry && h.geometry.z && delete h.geometry.z, h.symbol && h.symbol.outline && "esriCLS" === h.symbol.outline.type && !r.is11xService) continue;
        !r.is11xService && h.symbol && h.symbol.outline && h.symbol.outline.color && h.symbol.outline.color[3] && (h.symbol.outline.color[3] = 255);
        const u = e && e.renderer && ("valueExpression" in e.renderer && e.renderer.valueExpression || "hasVisualVariables" in e.renderer && e.renderer.hasVisualVariables());
        if (!h.symbol && e && e.renderer && u && !r.is11xService) {
            const t = e.renderer,
                i = await t.getSymbolAsync(a);
            if (!i) continue;
            h.symbol = i.toJSON(), "hasVisualVariables" in t && t.hasVisualVariables() && N(h.symbol, {
                renderer: t,
                graphic: a,
                symbol: i
            })
        }
        if (h.symbol && (h.symbol.angle || delete h.symbol.angle, De(h.symbol) ? h.symbol = await we(h.symbol, r) : h.symbol.text && delete h.attributes), (!i || !i.forceFeatureAttributes) && m && m.length) {
            const e = {};
            m.forEach((t => {
                h.attributes && h.attributes.hasOwnProperty(t) && (e[t] = h.attributes[t])
            })), h.attributes = e
        }
        "polygon" === a.geometry.type ? n.featureSet.features.push(h) : "polyline" === a.geometry.type ? s.featureSet.features.push(h) : "point" === a.geometry.type ? h.symbol && h.symbol.text ? c.featureSet.features.push(h) : o.featureSet.features.push(h) : "multipoint" === a.geometry.type ? l.featureSet.features.push(h) : "extent" === a.geometry.type && (h.geometry = y.fromExtent(a.geometry).toJSON(), n.featureSet.features.push(h))
    }
    const b = [n, s, l, o, c].filter((e => e.featureSet.features.length > 0));
    for (const y of b) {
        const e = y.featureSet.features.every((e => e.symbol));
        !e || i && i.forceFeatureAttributes || y.featureSet.features.forEach((e => {
            delete e.attributes
        })), e && delete y.layerDefinition.drawingInfo, y.layerDefinition.drawingInfo && y.layerDefinition.drawingInfo.renderer && await Se(y.layerDefinition.drawingInfo.renderer, r)
    }
    return b.length ? {
        featureCollection: {
            layers: b
        },
        showLabels: a
    } : null
}
async function re(e, t, i) {
    var r, a;
    let n;
    i.legendLayers && i.legendLayers.push({
        id: e.id
    });
    const s = e.renderer;
    if (e.featureReduction || s && "dot-density" === s.type && (!i.is11xService || parseFloat(i.cimVersion) < 2.6)) return ye(e, t, i);
    const o = t.layerView,
        {
            printTemplate: l,
            view: c
        } = t,
        u = s && ("valueExpression" in s && s.valueExpression || "hasVisualVariables" in s && s.hasVisualVariables()),
        y = "feature-layer" !== (null == (r = e.source) ? void 0 : r.type) && "ogc-feature" !== (null == (a = e.source) ? void 0 : a.type);
    if (!i.is11xService && u || e.featureReduction || o.filter || y || !s || "field" in s && null != s.field && ("string" != typeof s.field || !e.getField(s.field))) {
        const t = await xe(o);
        n = await ie(e, t, l, i)
    } else {
        var m, p;
        if (n = {
                id: (d = e.write()).id,
                title: d.title,
                opacity: d.opacity,
                minScale: d.minScale,
                maxScale: d.maxScale,
                url: d.url,
                layerType: d.layerType,
                customParameters: d.customParameters,
                layerDefinition: d.layerDefinition
            }, n.showLabels = l.showLabels && e.labelsVisible, be(n, e), null != (m = n.layerDefinition) && null != (p = m.drawingInfo) && p.renderer && (delete n.layerDefinition.minScale, delete n.layerDefinition.maxScale, await Se(n.layerDefinition.drawingInfo.renderer, i), "visualVariables" in s && s.visualVariables && s.visualVariables[0])) {
            const e = s.visualVariables[0];
            if ("size" === e.type && e.maxSize && "number" != typeof e.maxSize && e.minSize && "number" != typeof e.minSize) {
                const t = f(e, c.scale);
                n.layerDefinition.drawingInfo.renderer.visualVariables[0].minSize = t.minSize, n.layerDefinition.drawingInfo.renderer.visualVariables[0].maxSize = t.maxSize
            }
        }
        const t = K(o);
        t && (n.layerDefinition || (n.layerDefinition = {}), n.layerDefinition.definitionExpression = n.layerDefinition.definitionExpression ? `(${n.layerDefinition.definitionExpression}) AND (${t})` : t)
    }
    var d;
    return n
}
async function ae(e, {
    layerView: t,
    printTemplate: i
}, r) {
    r.legendLayers && r.legendLayers.push({
        id: e.id
    });
    return ie(e, await xe(t), i, r)
}
async function ne(e, {
    printTemplate: t
}, i) {
    return ie(e, null, t, i)
}

function se(e, t) {
    t.legendLayers && t.legendLayers.push({
        id: e.id
    });
    const i = {
        layerType: (r = e.write()).layerType,
        customParameters: r.customParameters
    };
    var r;
    if (i.bandIds = e.bandIds, i.compressionQuality = e.compressionQuality, i.format = e.format, i.interpolation = e.interpolation, (e.mosaicRule || e.definitionExpression) && (i.mosaicRule = e.exportImageServiceParameters.mosaicRule.toJSON()), e.renderingRule || e.renderer)
        if (t.is11xService) e.renderingRule && (i.renderingRule = e.renderingRule.toJSON()), e.renderer && (i.layerDefinition = i.layerDefinition || {}, i.layerDefinition.drawingInfo = i.layerDefinition.drawingInfo || {}, i.layerDefinition.drawingInfo.renderer = e.renderer.toJSON());
        else {
            const t = e.exportImageServiceParameters.combineRendererWithRenderingRule();
            t && (i.renderingRule = t.toJSON())
        } return be(i, e), i
}
async function oe(e, t, i) {
    const r = t.printTemplate;
    if (i.is11xService) {
        const t = {
            type: "kml"
        };
        return e.write(t, {
            origin: "web-map"
        }), delete t.layerType, t.url = c(e.url), t
    } {
        const a = [],
            n = t.layerView;
        n.allVisibleMapImages.forEach(((t, i) => {
            const r = {
                id: `${e.id}_image${i}`,
                type: "image",
                title: e.id,
                minScale: e.minScale || 0,
                maxScale: e.maxScale || 0,
                opacity: e.opacity,
                extent: t.extent
            };
            "data:image/png;base64," === t.href.substr(0, 22) ? r.imageData = t.href.substr(22) : r.url = t.href, a.push(r)
        }));
        const s = [...n.allVisiblePoints.items, ...n.allVisiblePolylines.items, ...n.allVisiblePolygons.items],
            o = {
                id: e.id,
                ...await ie(null, s, r, i)
            };
        return a.push(o), a
    }
}

function le(e, {
    view: t
}, i) {
    let r;
    const a = {
        id: e.id,
        subLayerIds: []
    };
    let n = [];
    const s = t.scale,
        o = e => {
            const t = 0 === s,
                i = 0 === e.minScale || s <= e.minScale,
                r = 0 === e.maxScale || s >= e.maxScale;
            if (e.visible && (t || i && r))
                if (e.sublayers) e.sublayers.forEach(o);
                else {
                    const t = e.toExportImageJSON(),
                        i = {
                            id: e.id,
                            name: e.title,
                            layerDefinition: {
                                drawingInfo: t.drawingInfo,
                                definitionExpression: t.definitionExpression,
                                source: t.source
                            }
                        };
                    n.unshift(i), a.subLayerIds.push(e.id)
                }
        };
    var l;
    return e.sublayers && e.sublayers.forEach(o), n.length && (n = n.map((({
        id: e,
        name: t,
        layerDefinition: i
    }) => ({
        id: e,
        name: t,
        layerDefinition: i
    }))), r = {
        layerType: (l = e.write()).layerType,
        customParameters: l.customParameters
    }, r.layers = n, r.visibleLayers = e.capabilities.exportMap.supportsDynamicLayers ? void 0 : a.subLayerIds, be(r, e), i.legendLayers.push(a)), r
}
async function ce({
    layerView: e,
    printTemplate: t
}, i) {
    const r = [],
        a = e.layer;
    if (n(a.featureCollections))
        for (const n of a.featureCollections) {
            const e = await ie(n, n.source, t, i);
            e && r.push(...e.featureCollection.layers)
        } else if (n(a.sublayers))
            for (const n of a.sublayers) {
                const e = await ie(null, n.graphics, t, i);
                e && r.push(...e.featureCollection.layers)
            }
    return {
        featureCollection: {
            layers: r
        }
    }
}

function ue() {
    return {
        type: "OpenStreetMap"
    }
}
async function ye(e, {
    printTemplate: t,
    view: i
}, r) {
    const a = {
            type: "image"
        },
        n = {
            format: "png",
            ignoreBackground: !0,
            layers: [e],
            rotation: 0
        },
        o = r.ssExtent || i.extent.clone();
    let l = 96,
        c = !0,
        y = !0;
    if (t.exportOptions) {
        const e = t.exportOptions;
        e.dpi > 0 && (l = e.dpi), e.width > 0 && (c = e.width % 2 == i.width % 2), e.height > 0 && (y = e.height % 2 == i.height % 2)
    }
    if ("map-only" === t.layout && t.scalePreserved && (!t.outScale || t.outScale === i.scale) && 96 === l && (!c || !y) && (n.area = {
            x: 0,
            y: 0,
            width: i.width,
            height: i.height
        }, c || (n.area.width -= 1), y || (n.area.height -= 1), !r.ssExtent)) {
        const e = i.toMap(s(n.area.width, n.area.height));
        o.ymin = e.y, o.xmax = e.x, r.ssExtent = o
    }
    a.extent = o.clone()._normalize(!0).toJSON();
    const f = await i.takeScreenshot(n),
        {
            data: m
        } = u(f.dataUrl);
    return a.imageData = m, a
}
async function fe(e, {
    layerView: t,
    printTemplate: i
}, r) {
    r.legendLayers && r.legendLayers.push({
        id: e.id
    });
    return ie(e, await xe(t), i, r)
}

function me(e) {
    const t = {
        layerType: (i = e.write()).layerType,
        customParameters: i.customParameters
    };
    var i;
    return be(t, e), t
}
async function pe(e, i, r) {
    if (r.is11xService && e.serviceUrl && e.styleUrl) {
        const i = t && t.findCredential(e.styleUrl),
            a = t && t.findCredential(e.serviceUrl);
        if (!i && !a || "2.1.0" !== r.cimVersion) {
            const t = {
                type: "VectorTileLayer"
            };
            return t.styleUrl = c(e.styleUrl), i && (t.token = i.token), a && a.token !== t.token && (t.additionalTokens = [{
                url: e.serviceUrl,
                token: a.token
            }]), t
        }
    }
    return ye(e, i, r)
}

function de(e) {
    const t = {
        type: "WebTiledLayer",
        urlTemplate: e.urlTemplate.replace(/\${/g, "{"),
        credits: e.copyright
    };
    return e.subDomains && e.subDomains.length > 0 && (t.subDomains = e.subDomains), t
}

function ge(e) {
    let t;
    const i = [],
        r = e => {
            e.visible && (e.sublayers ? e.sublayers.forEach(r) : e.name && i.unshift(e.name))
        };
    return e.sublayers && e.sublayers.forEach(r), i.length && (t = {
        type: "wms",
        customLayerParameters: e.customLayerParameters,
        customParameters: e.customParameters,
        transparentBackground: e.imageTransparency,
        visibleLayers: i,
        url: c(e.url),
        version: e.version
    }), t
}

function he(e) {
    const t = e.activeLayer;
    return {
        type: "wmts",
        customLayerParameters: e.customLayerParameters,
        customParameters: e.customParameters,
        format: t.imageFormat,
        layer: t.id,
        style: t.styleId,
        tileMatrixSet: t.tileMatrixSetId,
        url: c(e.url)
    }
}

function be(i, r) {
    if (r.url)
        if (i.url = c(i.url || r.url), "apiKey" in r && r.apiKey) i.token = r.apiKey;
        else if (e.apiKey && $(r.url)) i.token = e.apiKey;
    else {
        var a, n;
        i.token = null == (a = t) || null == (n = a.findCredential(r.url)) ? void 0 : n.token
    }
}
async function we(e, t) {
    t.canvas || (t.canvas = document.createElement("canvas"));
    const r = 1024;
    t.canvas.width = r, t.canvas.height = r;
    const a = t.canvas.getContext("2d");
    let n, s;
    if (e.path) {
        var c;
        const t = new Path2D(e.path);
        t.closePath(), a.fillStyle = Array.isArray(e.color) ? `rgba(${e.color[0]},${e.color[1]},${e.color[2]},${e.color[3]/255})` : "rgb(0,0,0)", a.fill(t);
        const i = J(a);
        if (!i) return null;
        a.clearRect(0, 0, r, r);
        const l = o(e.size) / Math.max(i.width, i.height);
        a.scale(l, l);
        const u = r / l,
            y = u / 2 - i.width / 2 - i.x,
            f = u / 2 - i.height / 2 - i.y;
        if (a.translate(y, f), Array.isArray(e.color) && a.fill(t), null != (c = e.outline) && c.width && Array.isArray(e.outline.color)) {
            const r = e.outline;
            a.lineWidth = o(r.width) / l, a.lineJoin = "round", a.strokeStyle = `rgba(${r.color[0]},${r.color[1]},${r.color[2]},${r.color[3]/255})`, a.stroke(t), i.width += a.lineWidth, i.height += a.lineWidth
        }
        i.width *= l, i.height *= l;
        const m = a.getImageData(r / 2 - i.width / 2, r / 2 - i.height / 2, Math.ceil(i.width), Math.ceil(i.height));
        n = m.width, s = m.height, a.canvas.width = n, a.canvas.height = s, a.putImageData(m, 0, 0)
    } else {
        const t = "image/svg+xml" === e.contentType ? "data:image/svg+xml;base64," + e.imageData : e.url,
            r = (await i(t, {
                responseType: "image"
            })).data;
        n = o(e.width), s = o(e.height), a.canvas.width = n, a.canvas.height = s, a.drawImage(r, 0, 0, a.canvas.width, a.canvas.height)
    }
    return {
        type: "esriPMS",
        imageData: a.canvas.toDataURL("image/png").substr(22),
        angle: e.angle,
        contentType: "image/png",
        height: l(s),
        width: l(n),
        xoffset: e.xoffset,
        yoffset: e.yoffset
    }
}
async function Se(e, t) {
    const i = e.type;
    if ("simple" === i && De(e.symbol)) e.symbol = await we(e.symbol, t);
    else if ("uniqueValue" === i || "classBreaks" === i) {
        De(e.defaultSymbol) && (e.defaultSymbol = await we(e.defaultSymbol, t));
        const r = e["uniqueValue" === i ? "uniqueValueInfos" : "classBreakInfos"];
        if (r)
            for (const e of r) De(e.symbol) && (e.symbol = await we(e.symbol, t))
    }
}
async function xe(e) {
    return e.queryFeatures(e.createQuery()).then((e => e.features))
}

function ve(e) {
    return e.gpMetadata && e.gpMetadata.executionType ? W.fromJSON(e.gpMetadata.executionType) : "sync"
}

function De(e) {
    return e && (e.path || "image/svg+xml" === e.contentType || e.url && e.url.endsWith(".svg"))
}
export {
    Q as execute, H as getGpPrintParams, Y as getGpServerUrl, G as getMode, B as printCacheMap
};