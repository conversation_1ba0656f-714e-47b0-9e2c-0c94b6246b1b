// export const getFileSize = (fileSize)=>{
//     if(fileSize<=1024){
//         return {
//             size:fileSize,
//             type:'byte'
//         }
//     }else if(fileSize<1024*1024){
//         return {
//             size:(fileSize/(1024)),
//             type:'kilo-byte'
//         }
//     }else if(fileSize<(1024*1024*1024)){
//         return {
//             size:(fileSize/(1024*1024*1024)),
//             type:'mega-byte'
//         }
//     }else {
//         return {
//             size:(fileSize/(1024*1024*1024*1024)),
//             type:'giga-byte'
//         }
//     }
// }


export const getFileSize = (fileSize)=>{
    let kiloBtype = fileSize/1024;
    if(kiloBtype<1){
        return {
            size:fileSize,
            type:'byte'
        }
    }else if(1<kiloBtype&&kiloBtype<=1024){
        return {
            size:kiloBtype,
            type:'kiloByte'
        }
    }else if(1024<kiloBtype&&kiloBtype<(1024*1024)){
        return {
            size:(kiloBtype/(1024)),
            type:'megaByte'
        }
    }else {
        return {
            size:(kiloBtype/(1024*1024*1024)),
            type:'gigaByte'
        }
    }
}