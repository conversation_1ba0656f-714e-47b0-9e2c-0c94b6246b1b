/* eslint-disable */
import React from "react";
import ReactJ<PERSON><PERSON>, {
  STATUS,
  ACTIONS,
  EVENTS,
  LIFECYCLE,
} from "react-joyride";
import { useTranslation } from "react-i18next";
export default function MainPageHelp(props) {
  const { t,i18n } = useTranslation("help");

  const handleJoyrideCallback = (data) => {
    const { status, type, action } = data;
    if ([EVENTS.TOUR_END].includes(type)) {
      props.helpName === "main"
        ? localStorage.setItem("showHelp", true)
        : props.helpName === "openSideMenu"
          ? localStorage.setItem("showOpenSideHelp", true)
          : null;
    }
    if ([LIFECYCLE.COMPLETE].includes(type)) {
      props.helpName === "main"
        ? localStorage.setItem("showHelp", true)
        : props.helpName === "openSideMenu"
          ? localStorage.setItem("showOpenSideHelp", true)
          : null;
    }
    if ([STATUS.SKIPPED].includes(status)) {
      props.helpName === "main"
        ? localStorage.setItem("showHelp", true)
        : props.helpName === "openSideMenu"
          ? localStorage.setItem("showOpenSideHelp", true)
          : null;
    }
    if ([ACTIONS.CLOSE].includes(action)) {
      props.helpName === "main"
        ? localStorage.setItem("showHelp", true)
        : props.helpName === "openSideMenu"
          ? localStorage.setItem("showOpenSideHelp", true)
          : null;
          
      props.setHelpShow(false);
    }
  };

  let tooltipPlacement = i18n.language === "ar"?"right-start":"left";

  let helpSteps =
    props.helpName === "main"
      ? [
        {
          target: ".outerSearchHelp",
          content: t("outerSearchHelp"),
        },
        {
          target: "#layersMenu",
          content: t("layerInfo"),
          placement: "bottom-start",
        }, 
        {
          target: "#fullScreen",
          content: t("fullscreenServHelp"),
          placement: "bottom-start",
        }, 
        {
          target: "#compareLayers",
          content: t("CompareLayers"),
          placement: "bottom-start",
        },
        {
          target: "#inquiry",
          content: t("inquiryInfo"),
          placement: tooltipPlacement,
        }, 
        {
          target: "#Basemap",
          content: t("BaseMap"),
          placement: tooltipPlacement
        },
        {
          target: "#googleMaps",
          content: t("GoogleMaps"),
          placement: tooltipPlacement
        },
        {
          target: "#smallMap",
          content: t("SmallMap"),
          placement: tooltipPlacement
        },
        {
          target: "#myLocation",
          content: t("myLocation"),
          placement: tooltipPlacement
        },
        {
          target: "#generalSiteMap",
          content: t("generalSiteMap"),
          placement: tooltipPlacement
        },
        {
          target: "#fullMap",
          content: t("fullMap"),
          placement: tooltipPlacement
        },
        {
          target: "#zoomIn",
          content: t("zoomIn"),
          placement: tooltipPlacement
        },
        {
          target: "#zoomOut",
          content: t("zoomOut"),
          placement: tooltipPlacement
        },
        {
          target: "#next",
          content: t("nextInfo"),
          placement: tooltipPlacement
        },
        {
          target: "#prev",
          content: t("prev"),
          placement: tooltipPlacement
        },
        {
          target: "#move",
          content: t("move"),
          placement: tooltipPlacement
        },{
          target: "#removeAll",
          content: t("removeAll"),
          placement: tooltipPlacement
        }
        /*{
          target: ".testHeeelp",
          content: t("mapToolsHelp"),
        },*/
      ]
      : props.helpName === "openSideMenu"
        ? [
          {
            target: ".openSideHelp",
            disableBeacon: true,
            content: t("openSideHelp"),
          },
        ]
        : null;
  return (
    <>
      {console.log(props.helpName)}
      {console.log(helpSteps)}
      {props.helpName === "main" || props.helpName === "openSideMenu" ? (
        <ReactJoyride
          locale={{
            back: t("back"),
            close: t("close"),
            last: t("last"),
            next: t("next"),
          }}
          callback={handleJoyrideCallback}
          steps={helpSteps}
          disableCloseOnEsc
          run={true}
          continuous
          showProgress
          disableOverlayClose
          showSkipButton
          styles={{
            options: {
              arrowColor: "rgba(255, 255, 255, .9)",
              backgroundColor: "rgba(255, 255, 255, .9)",
              overlayColor: "rgba(31, 75, 89, .4)",
              primaryColor: "#000",
              textColor: "#1f4b59",
              zIndex: 9000000,
            },
          }}
        />
      ) : (
        <></>
      )}
    </>
  );
}
