import { Fa<PERSON><PERSON>, FaTrashAlt } from "react-icons/fa";
import { Input, Modal, Select, message, Popconfirm, Upload } from "antd";
import gallery_image from "../../assets/images/sidemenu/ncw.jpeg";
import gallery_icon from "../../assets/images/study-actor/gallery_thumbnail.svg";
import { useEffect, useRef, useState } from "react";
import ReactImageGallery from "react-image-gallery";
import { RiArrowDropDownFill } from "react-icons/ri";
import monitor_details from "../../assets/images/study-actor/monitor-details.svg";
import monitor_data from "../../assets/images/study-actor/monitor_data.svg";
import pen_icon from "../../assets/images/study-actor/Pen.svg";
import { useTranslation } from "react-i18next";
import {
  getLayerId,
  getMapInfo,
  showLoading,
  zoomToFeatures,
} from "../../helper/common_func";
import FeatureLayer from "@arcgis/core/layers/FeatureLayer";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import Graphic from "@arcgis/core/Graphic";
import ReactPlayer from "react-player";

const { Option } = Select;

export default function MonitoringCaseDetails(props) {
  console.log("MonitoringCaseDetails props", props);
  const { t } = useTranslation("sidemenu");
  const { data, language, montioringLayersNames } = props;

  const attributes = data?.feature?.attributes || {};
  const currentLayerName = data?.ownedLayerName;
  const [domainValues, setDomainValues] = useState({});
  const [formData, setFormData] = useState({});
  const [originalFormData, setOriginalFormData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isEditingData, setIsEditingData] = useState(false);
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [media, setMedia] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUploadingMedia, setIsUploadingMedia] = useState(false);
  const [selectedDataType, setSelectedDataType] = useState(null);

  const getLocalizedText = (arField, enField) => {
    if (language === "ar") {
      return attributes[arField] || attributes[enField] || "";
    }
    return attributes[enField] || attributes[arField] || "";
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return "";
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return "";
      return date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "";
    }
  };

  const getDataType = () => {
    if (isEditingData && selectedDataType) {
      return selectedDataType;
    }
    if (attributes.INCIDENT_ID !== undefined || attributes.INCIDENT_SUBJECT) {
      return "incident";
    } else if (
      attributes.SIGHTING_ID !== undefined ||
      attributes.AR_SPECIE ||
      attributes.EN_SPECIE
    ) {
      return "sighting";
    } else if (
      attributes.IMPORTANT_SITE_ID !== undefined ||
      attributes.AR_SITE_NAME
    ) {
      return "site";
    }
    return "incident";
  };

  const dataType = getDataType();

  const getFieldConfig = () => {
    const baseConfig = {
      dropdownFields: [
        "AR_INCIDENT_TYPE",
        "EN_INCIDENT_TYPE",
        "AR_PROTECTED_AREA_CATEGORY",
        "EN_PROTECTED_AREA_CATEGORY",
        "AR_INCIDENT_PRIORITY",
        "EN_INCIDENT_PRIORITY",
        "AR_GOVERNORATE",
        "EN_GOVERNORATE",
        "AR_REGION",
        "EN_REGION",
        "AR_PROTECTED_AREA_NAME",
        "EN_PROTECTED_AREA_NAME",
        "AR_NCW_CATEGORY",
        "EN_NCW_CATEGORY",
        "AR_SPECIE",
        "EN_SPECIE",
        "AR_SPECIE_STATUS",
        "EN_SPECIE_STATUS",
        "AR_THREATS_TYPE",
        "EN_THREATS_TYPE",
        "AR_SITE_TYPE",
        "EN_SITE_TYPE",
        "AR_PLACE_TYPE",
        "EN_PLACE_TYPE",
        "AR_IMPORTANCE_LEVEL",
        "EN_IMPORTANCE_LEVEL",
        // "AR_ECOLOGY_CATEGORY",
        // "EN_ECOLOGY_CATEGORY",
        // "AR_INCIDENT_RELATE",
        // "EN_INCIDENT_RELATE",
      ],
      textFields: [
        "INCIDENT_SUBJECT",
        "INCIDENT_DESCRIPTION",
        "REPORTER_NOTES",
        "AR_NOTES",
        "EN_NOTES",
        "AR_SITE_DESCRIPTION",
        "EN_SITE_DESCRIPTION",
        "AR_SITE_NAME",
        "EN_SITE_NAME",
      ],
    };
    return baseConfig;
  };

  const fieldConfig = getFieldConfig();

  const fetchDomainValues = async () => {
    if (!montioringLayersNames || montioringLayersNames.length === 0) {
      console.warn("No monitoring layer names provided");
      return;
    }

    try {
      showLoading(true);
      const mapInfo = await getMapInfo(window.mobileAppUrl);
      const allDomains = {};

      for (const layerName of montioringLayersNames) {
        try {
          const layerId = getLayerId(mapInfo, layerName);
          const featureLayer = new FeatureLayer({
            url: `${window.mobileAppUrl}/${layerId}`,
          });

          await featureLayer.load();

          const layerDomains = {};
          if (featureLayer.fields) {
            featureLayer.fields.forEach((field) => {
              if (field.domain && field.domain.codedValues) {
                layerDomains[field.name] = field.domain.codedValues.map(
                  (cv) => ({
                    code: cv.code,
                    name: cv.name,
                  })
                );
              }
            });
          }

          allDomains[layerName] = layerDomains;
        } catch (error) {
          console.error(
            `Error fetching domains for layer ${layerName}:`,
            error
          );
          allDomains[layerName] = {};
        }
      }

      setDomainValues(allDomains);
      console.log("Fetched domain values:", allDomains);
    } catch (error) {
      console.error("Error fetching domain values:", error);
      setDomainValues({});
    } finally {
      showLoading(false);
    }
  };

  const parseMedia = (field) => {
    if (!formData[field] && !attributes[field]) return [];
    const mediaData = formData[field] || attributes[field];

    try {
      if (Array.isArray(mediaData)) {
        return mediaData
          .filter((url) => url && url.trim())
          .map((url, index) => ({
            original: url.startsWith("http") ? url : `${window.filesURL}${url}`,
            thumbnail:
              field === "VIDEOS"
                ? gallery_icon
                : url.startsWith("http")
                ? url
                : `${window.filesURL}${url}`,
            originalUrl: url,
            type: field === "PHOTOS" ? "image" : "video",
          }));
      }
      if (typeof mediaData === "string") {
        const mediaUrls = mediaData.split(",").map((url) => url.trim());
        return mediaUrls
          .filter((url) => url)
          .map((url, index) => ({
            original: url.startsWith("http") ? url : `${window.filesURL}${url}`,
            thumbnail:
              field === "VIDEOS"
                ? gallery_icon
                : url.startsWith("http")
                ? url
                : `${window.filesURL}${url}`,
            originalUrl: url,
            type: field === "PHOTOS" ? "image" : "video",
          }));
      }
    } catch (error) {
      console.error(`Error parsing ${field}:`, error);
    }

    return [];
  };

  useEffect(() => {
    if (attributes && Object.keys(attributes).length > 0) {
      const initialFormData = {};
      Object.keys(attributes).forEach((key) => {
        initialFormData[key] = attributes[key];
      });

      if (!initialFormData.INCIDENT_DESCRIPTION)
        initialFormData.INCIDENT_DESCRIPTION = "";
      if (!initialFormData.REPORTER_NOTES) initialFormData.REPORTER_NOTES = "";
      if (!initialFormData.AR_NOTES) initialFormData.AR_NOTES = "";
      if (!initialFormData.EN_NOTES) initialFormData.EN_NOTES = "";
      if (!initialFormData.AR_SITE_DESCRIPTION)
        initialFormData.AR_SITE_DESCRIPTION = "";
      if (!initialFormData.EN_SITE_DESCRIPTION)
        initialFormData.EN_SITE_DESCRIPTION = "";
      if (!initialFormData.PHOTOS) initialFormData.PHOTOS = "";
      if (!initialFormData.VIDEOS) initialFormData.VIDEOS = "";

      setFormData(initialFormData);
      setOriginalFormData(initialFormData);
      setSelectedDataType(getDataType());
    }
  }, [attributes]);

  useEffect(() => {
    try {
      const photos = parseMedia("PHOTOS");
      const videos = parseMedia("VIDEOS");
      const combinedMedia = [...photos, ...videos];
      setMedia(combinedMedia);
    } catch (error) {
      console.error("Error setting media:", error);
      setMedia([]);
    }
  }, [formData.PHOTOS, attributes.PHOTOS, formData.VIDEOS, attributes.VIDEOS]);

  useEffect(() => {
    if (montioringLayersNames && montioringLayersNames.length > 0) {
      fetchDomainValues();
    }
  }, [montioringLayersNames]);

  const [selectedFiles, setSelectedFiles] = useState([]);
  const fileInputRef = useRef(null);

  const applyMediaEditToFeature = async (field, updatedMedia) => {
    if (!currentLayerName) {
      console.error("No current layer name available");
      return;
    }

    try {
      const mapInfo = await getMapInfo(window.mobileAppEditUrl);
      const layerId = getLayerId(mapInfo, currentLayerName);

      const featureLayer = new FeatureLayer({
        url: `${window.mobileAppEditUrl}/${layerId}`,
      });

      await featureLayer.load();

      const objectId = attributes.OBJECTID;

      let mediaValue;
      if (Array.isArray(updatedMedia)) {
        mediaValue = updatedMedia.join(",");
      } else {
        mediaValue = updatedMedia || "";
      }
      const updatedAttributes = {
        OBJECTID: objectId,
        [field]: mediaValue,
        LAST_MODIFIED: Date.now(),
      };

      console.log(`Updating ${field} with attributes:`, updatedAttributes);

      const updatedFeature = new Graphic({
        attributes: updatedAttributes,
      });

      const updateResult = await featureLayer.applyEdits({
        updateFeatures: [updatedFeature],
      });

      console.log(`${field} update results:`, updateResult);

      if (
        updateResult.updateFeatureResults &&
        updateResult.updateFeatureResults.length > 0 &&
        !updateResult.updateFeatureResults[0].error
      ) {
        setOriginalFormData((prev) => ({
          ...prev,
          [field]: updatedMedia,
        }));

        const monitoringCasesLayer = props.map?.findLayerById(
          "MonitoringCasesGraphicLayer"
        );

        if (
          monitoringCasesLayer &&
          monitoringCasesLayer instanceof GraphicsLayer
        ) {
          const existingGraphic = monitoringCasesLayer.graphics.find(
            (graphic) => graphic.attributes.OBJECTID === objectId
          );

          if (existingGraphic) {
            existingGraphic.attributes[field] = mediaValue;
            existingGraphic.attributes.LAST_MODIFIED =
              updatedAttributes.LAST_MODIFIED;
          }
        }
      } else {
        message.warning(t("studiesActor.media_upload_failed"));
      }
    } catch (error) {
      console.error(`Error updating ${field} in feature layer:`, error);
      message.warning(t("studiesActor.media_upload_failed"));
    }
  };

  const handleMediaUpload = async (fileList) => {
    if (!fileList || fileList.length === 0) return false;

    const files = Array.isArray(fileList) ? fileList : [fileList];

    const newImages = files.filter((file) => file.type.startsWith("image/"));
    const newVideos = files.filter((file) => file.type.startsWith("video/"));

    const currentImagesCount = media.filter((m) => m.type === "image").length;
    const currentVideosCount = media.filter((m) => m.type === "video").length;

    if (currentImagesCount + newImages.length > 10) {
      message.warning(
        t("studiesActor.files_count_validation", {
          default: "لا يمكن رفع أكثر من 10 صور",
        })
      );
      return false;
    }

    if (currentVideosCount + newVideos.length > 10) {
      message.warning(
        t("studiesActor.files_count_validation", {
          default: "لا يمكن رفع أكثر من 10 فيديوهات",
        })
      );
      return false;
    }

    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/bmp",
      "image/webp",
      "video/mp4",
      "video/quicktime",
      "video/x-msvideo",
      "video/x-ms-wmv",
    ];
    const maxSizeInBytes = 10 * 1024 * 1024;
    const invalidFiles = [];
    const oversizedFiles = [];

    for (let file of files) {
      if (file.size > maxSizeInBytes) {
        oversizedFiles.push(file.name);
        continue;
      }

      const fileExtension = "." + file.name.split(".").pop().toLowerCase();
      const isValidType =
        allowedTypes.includes(file.type) ||
        [
          ".jpg",
          ".jpeg",
          ".png",
          ".gif",
          ".bmp",
          ".webp",
          ".mp4",
          ".mov",
          ".avi",
          ".wmv",
        ].includes(fileExtension);

      if (!isValidType) {
        invalidFiles.push(file.name);
      }
    }

    if (oversizedFiles.length > 0) {
      message.warning(
        t("studiesActor.files_size_validation", {
          default: "حجم الملفات كبير جداً. الحد الأقصى 10 ميجابايت",
        })
      );
      return false;
    }

    if (invalidFiles.length > 0) {
      message.warning(
        t("studiesActor.files_format_validation", {
          default: "نوع الملفات غير مدعوم. يرجى رفع صور أو فيديوهات فقط",
        })
      );
      return false;
    }

    try {
      setIsUploadingMedia(true);
      showLoading(true);

      const uploadFormData = new FormData();
      for (let index = 0; index < files.length; index++) {
        uploadFormData.append(`file[${files[index].name}]`, files[index]);
      }

      const response = await fetch(`${window.ApiUrl}uploadMultifiles`, {
        method: "POST",
        body: uploadFormData,
      });

      const results = await response.json();
      console.log("Upload results:", results);

      let uploadedUrls = [];
      if (results && Array.isArray(results)) {
        uploadedUrls = results.map((result) => result.data).filter(Boolean);
      }

      const currentPhotos = formData.PHOTOS || [];
      let updatedPhotos;

      if (Array.isArray(currentPhotos)) {
        updatedPhotos = [...currentPhotos];
      } else if (typeof currentPhotos === "string" && currentPhotos.trim()) {
        updatedPhotos = currentPhotos.split(",").map((url) => url.trim());
      } else {
        updatedPhotos = [];
      }

      const currentVideos = formData.VIDEOS || [];
      let updatedVideos;

      if (Array.isArray(currentVideos)) {
        updatedVideos = [...currentVideos];
      } else if (typeof currentVideos === "string" && currentVideos.trim()) {
        updatedVideos = currentVideos.split(",").map((url) => url.trim());
      } else {
        updatedVideos = [];
      }

      let urlIndex = 0;
      files.forEach((file) => {
        const uploadedUrl = uploadedUrls[urlIndex];
        if (uploadedUrl) {
          if (file.type.startsWith("image/")) {
            updatedPhotos.push(uploadedUrl);
          } else if (file.type.startsWith("video/")) {
            updatedVideos.push(uploadedUrl);
          }
        }
        urlIndex++;
      });

      setFormData((prev) => ({
        ...prev,
        PHOTOS: updatedPhotos,
        VIDEOS: updatedVideos,
      }));

      const newFilesData = files.map((file, index) => ({
        name: file.name,
        type: file.type.startsWith("image/") ? "image" : "video",
        url: uploadedUrls[index] || null,
      }));

      setSelectedFiles((prev) => [...prev, ...newFilesData]);

      if (newImages.length > 0) {
        await applyMediaEditToFeature("PHOTOS", updatedPhotos);
      }
      if (newVideos.length > 0) {
        await applyMediaEditToFeature("VIDEOS", updatedVideos);
      }

      message.success(t("studiesActor.media_uploaded"));
    } catch (error) {
      console.error("Error uploading media:", error);
      message.error(
        t("studiesActor.upload_error", {
          default: "حدث خطأ أثناء رفع الملفات",
        })
      );
    } finally {
      setIsUploadingMedia(false);
      showLoading(false);
    }

    return false;
  };

  const handleFileUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleMediaDelete = async (index) => {
    try {
      setIsLoading(true);
      showLoading(true);

      const currentMedia = media;
      const itemToDelete = currentMedia[index];
      const urlToDelete = itemToDelete.originalUrl || itemToDelete.original;
      const field = itemToDelete.type === "image" ? "PHOTOS" : "VIDEOS";

      const currentFieldData = formData[field] || [];
      let updatedFieldData;

      if (Array.isArray(currentFieldData)) {
        updatedFieldData = currentFieldData.filter((url) => {
          const cleanUrl = url.startsWith("http")
            ? url
            : `${window.filesURL}${url}`;
          const deleteUrl = urlToDelete.startsWith("http")
            ? urlToDelete
            : `${window.filesURL}${urlToDelete}`;
          return cleanUrl !== deleteUrl;
        });
      } else if (typeof currentFieldData === "string") {
        const urls = currentFieldData.split(",").map((url) => url.trim());
        updatedFieldData = urls.filter((url) => {
          const cleanUrl = url.startsWith("http")
            ? url
            : `${window.filesURL}${url}`;
          const deleteUrl = urlToDelete.startsWith("http")
            ? urlToDelete
            : `${window.filesURL}${urlToDelete}`;
          return cleanUrl !== deleteUrl;
        });
      } else {
        updatedFieldData = [];
      }

      if (!updatedFieldData || updatedFieldData.length === 0) {
        updatedFieldData = [];
      }
      setFormData((prev) => ({
        ...prev,
        [field]: updatedFieldData,
      }));

      await applyMediaEditToFeature(field, updatedFieldData);

      setSelectedFiles((prev) => {
        const updatedFiles = [...prev];
        const fileIndex = updatedFiles.findIndex(
          (file) => file.url === urlToDelete || file.name === itemToDelete.name
        );
        if (fileIndex !== -1) {
          updatedFiles.splice(fileIndex, 1);
        }
        return updatedFiles;
      });

      message.success(t("studiesActor.image_deleted"));
    } catch (error) {
      console.error("Error deleting media:", error);
      message.error(
        t("studiesActor.delete_error", {
          default: "حدث خطأ أثناء حذف الملف",
        }) +
          ": " +
          error.message
      );
    } finally {
      setIsLoading(false);
      showLoading(false);
    }
  };

  const getDomainOptions = (fieldName) => {
    if (!currentLayerName || !domainValues[currentLayerName]) {
      return [];
    }
    return domainValues[currentLayerName][fieldName] || [];
  };

  const isDropdownField = (fieldName) => {
    return (
      fieldConfig.dropdownFields.includes(fieldName) &&
      getDomainOptions(fieldName).length > 0
    );
  };

  const renderInputField = (
    fieldName,
    value,
    placeholder,
    readOnly = false,
    type = "text"
  ) => {
    if (isDropdownField(fieldName)) {
      const options = getDomainOptions(fieldName);
      return (
        <Select
          value={value || undefined}
          placeholder={placeholder}
          disabled={readOnly}
          style={{ width: "100%" }}
          onChange={(newValue) => handleFieldChange(fieldName, newValue)}
          allowClear
        >
          {options.map((option) => (
            <Option key={option.code} value={option.code}>
              {option.name}
            </Option>
          ))}
        </Select>
      );
    }

    return (
      <Input
        type={type}
        value={value || ""}
        placeholder={placeholder}
        readOnly={readOnly}
        onChange={(e) => handleFieldChange(fieldName, e.target.value)}
      />
    );
  };

  const handleFieldChange = (fieldName, value) => {
    setFormData((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  const updateFeatureInLayer = async () => {
    try {
      setIsLoading(true);
      showLoading(true);

      const mapInfo = await getMapInfo(window.mobileAppEditUrl);
      const layerId = getLayerId(mapInfo, currentLayerName);

      const featureLayer = new FeatureLayer({
        url: `${window.mobileAppEditUrl}/${layerId}`,
      });

      await featureLayer.load();

      const objectId = attributes.OBJECTID || attributes.FID;

      const updatedAttributes = {
        OBJECTID: objectId,
      };

      Object.keys(formData).forEach((key) => {
        if (formData[key] !== originalFormData[key]) {
          let value = formData[key];

          if (key === "PHOTOS" || key === "VIDEOS") {
            if (Array.isArray(value)) {
              value = value.join(",");
            }
          }

          updatedAttributes[key] = value;

          if (value === "" || value === null) {
            updatedAttributes[key] = null;
          }
        }
      });

      updatedAttributes.LAST_MODIFIED = Date.now();

      console.log("Updating feature with attributes:", updatedAttributes);

      const updatedFeature = new Graphic({
        attributes: updatedAttributes,
      });

      const updateResult = await featureLayer.applyEdits({
        updateFeatures: [updatedFeature],
      });

      console.log("update results ", updateResult);

      if (
        updateResult.updateFeatureResults &&
        updateResult.updateFeatureResults.length > 0 &&
        !updateResult.updateFeatureResults[0].error
      ) {
        message.success(
          t("studiesActor.update_success", {
            default: "تم تحديث البيانات بنجاح",
          })
        );
        setOriginalFormData({ ...formData });

        setIsEditingData(false);
        setIsEditingNotes(false);

        const monitoringCasesLayer = props.map?.findLayerById(
          "MonitoringCasesGraphicLayer"
        );

        if (
          monitoringCasesLayer &&
          monitoringCasesLayer instanceof GraphicsLayer
        ) {
          const existingGraphic = monitoringCasesLayer.graphics.find(
            (graphic) => graphic.attributes.OBJECTID === objectId
          );

          if (existingGraphic) {
            Object.keys(updatedAttributes).forEach((key) => {
              existingGraphic.attributes[key] = updatedAttributes[key];
            });
          }
        }
      } else {
        message.error(t("studiesActor.update_error"));
      }
    } catch (error) {
      console.error("Error updating feature:", error);
      message.error(t("studiesActor.update_error"));
    } finally {
      setIsLoading(false);
      showLoading(false);
    }
  };

  const validateFormData = () => {
    console.log("form data", formData);
    const errors = [];

    if (dataType === "sighting" && formData.SAME_SPECIES_COUNT) {
      const count = parseInt(formData.SAME_SPECIES_COUNT);
      if (isNaN(count) || count < 0) {
        errors.push(
          t("studiesActor.invalid_count", {
            default: "العدد غير صحيح",
          })
        );
      }
    }

    const requiredFields = getRequiredFields();
    console.log("required fields", requiredFields);
    requiredFields.forEach((field) => {
      if (!formData[field] || formData[field].toString().trim() === "") {
        errors.push(t("studiesActor.fields_required"));
      }
    });

    return errors;
  };

  const getRequiredFields = () => {
    switch (dataType) {
      case "incident":
        return language == "ar" ? ["AR_INCIDENT_TYPE"] : ["EN_INCIDENT_TYPE"];
      case "sighting":
        return language == "ar"
          ? [
              "AR_SPECIE",
              // "AR_SPECIE_STATUS",
              // "AR_THREATS_TYPE",
              // "AR_SITE_TYPE",
              // "AR_PLACE_TYPE",
              "AR_NCW_CATEGORY",
              "SAME_SPECIES_COUNT",
            ]
          : [
              "EN_SPECIE",
              // "EN_SPECIE_STATUS",
              // "EN_THREATS_TYPE",
              // "EN_SITE_TYPE",
              // "EN_PLACE_TYPE",
              "EN_NCW_CATEGORY",
              "SAME_SPECIES_COUNT",
            ];
      case "site":
        return language == "ar"
          ? ["AR_SITE_TYPE", "AR_IMPORTANCE_LEVEL", "AR_SITE_NAME"]
          : ["EN_SITE_TYPE", "EN_IMPORTANCE_LEVEL", "EN_SITE_NAME"];
      default:
        return [];
    }
  };

  const handleSave = async () => {
    const validationErrors = validateFormData();

    if (validationErrors.length > 0) {
      message.error(validationErrors.join(", "));
      return;
    }

    await updateFeatureInLayer();
  };

  const handleCancelDataEdit = () => {
    if (hasDataChanges()) {
      Modal.confirm({
        title: t("studiesActor.unsaved_changes_title", {
          default: "تغييرات غير محفوظة",
        }),
        content: t("studiesActor.unsaved_changes_message", {
          default: "لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟",
        }),
        okText: t("studiesActor.discard_changes", {
          default: "تجاهل التغييرات",
        }),
        cancelText: t("studiesActor.continue_editing", {
          default: "متابعة التعديل",
        }),
        onOk: () => {
          resetForm();
          setIsEditingData(false);
          setSelectedDataType(getDataType());
        },
      });
    } else {
      setIsEditingData(false);
      setSelectedDataType(getDataType());
    }
  };

  const handleCancelNotesEdit = () => {
    if (hasNotesChanges()) {
      Modal.confirm({
        title: t("studiesActor.unsaved_changes_title", {
          default: "تغييرات غير محفوظة",
        }),
        content: t("studiesActor.unsaved_changes_message", {
          default: "لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟",
        }),
        okText: t("studiesActor.discard_changes", {
          default: "تجاهل التغييرات",
        }),
        cancelText: t("studiesActor.continue_editing", {
          default: "متابعة التعديل",
        }),
        onOk: () => {
          resetForm();
          setIsEditingNotes(false);
        },
      });
    } else {
      setIsEditingNotes(false);
    }
  };

  const hasDataChanges = () => {
    try {
      const dataFields = [
        "AR_INCIDENT_TYPE",
        "EN_INCIDENT_TYPE",
        // "AR_INCIDENT_PRIORITY",
        // "EN_INCIDENT_PRIORITY",
        // "AR_INCIDENT_RELATE",
        // "EN_INCIDENT_RELATE",
        "AR_NCW_CATEGORY",
        "EN_NCW_CATEGORY",
        "AR_SPECIE",
        "EN_SPECIE",
        "AR_SPECIE_STATUS",
        "EN_SPECIE_STATUS",
        "AR_THREATS_TYPE",
        "EN_THREATS_TYPE",
        "AR_PLACE_TYPE",
        "EN_PLACE_TYPE",
        "SAME_SPECIES_COUNT",
        "AR_SITE_TYPE",
        "EN_SITE_TYPE",
        "AR_IMPORTANCE_LEVEL",
        "EN_IMPORTANCE_LEVEL",
        "AR_SITE_NAME",
        "EN_SITE_NAME",
        "PHOTOS",
        "VIDEOS",
      ].filter(Boolean);

      return dataFields.some((key) => formData[key] !== originalFormData[key]);
    } catch (error) {
      console.error("Error checking data changes:", error);
      return false;
    }
  };

  const hasNotesChanges = () => {
    try {
      const notesFields = [
        "INCIDENT_DESCRIPTION",
        "REPORTER_NOTES",
        "AR_NOTES",
        "EN_NOTES",
        "AR_SITE_DESCRIPTION",
        "EN_SITE_DESCRIPTION",
      ];

      return notesFields.some((key) => formData[key] !== originalFormData[key]);
    } catch (error) {
      console.error("Error checking notes changes:", error);
      return false;
    }
  };

  const hasFormChanges = () => {
    try {
      return Object.keys(formData).some(
        (key) => formData[key] !== originalFormData[key]
      );
    } catch (error) {
      console.error("Error checking form changes:", error);
      return false;
    }
  };

  const resetForm = () => {
    setFormData({ ...originalFormData });
  };

  const getTitle = () => {
    switch (dataType) {
      case "incident":
        return t("studiesActor.incident_title");
      case "sighting":
        return t("studiesActor.sighting_title");
      case "site":
        return t("studiesActor.site_title");
      default:
        return t("studiesActor.details_title", { default: "التفاصيل" });
    }
  };

  const getDate = () => {
    switch (dataType) {
      case "incident":
        return formatDate(attributes.INCIDENT_DATE);
      case "sighting":
        return formatDate(attributes.SIGHTING_DATE);
      case "site":
        return formatDate(attributes.SIGHTING_DATE || attributes.CREATED_DATE);
      default:
        return formatDate(attributes.CREATED_DATE);
    }
  };

  const getNotes = () => {
    switch (dataType) {
      case "incident":
        return formData.INCIDENT_DESCRIPTION || formData.REPORTER_NOTES || "";
      case "sighting":
        if (language === "ar") {
          return formData.AR_NOTES || formData.EN_NOTES || "";
        }
        return formData.EN_NOTES || formData.AR_NOTES || "";
      case "site":
        if (language === "ar") {
          return (
            formData.AR_SITE_DESCRIPTION || formData.EN_SITE_DESCRIPTION || ""
          );
        }
        return (
          formData.EN_SITE_DESCRIPTION || formData.AR_SITE_DESCRIPTION || ""
        );
      default:
        return "";
    }
  };

  const getNotesFieldName = () => {
    switch (dataType) {
      case "incident":
        return "INCIDENT_DESCRIPTION";
      case "sighting":
        return language === "ar" ? "AR_NOTES" : "EN_NOTES";
      case "site":
        return language === "ar"
          ? "AR_SITE_DESCRIPTION"
          : "EN_SITE_DESCRIPTION";
      default:
        return "INCIDENT_DESCRIPTION";
    }
  };

  const displayMedia = media;

  const customRenderItem = (item) => {
    return (
      <div className="image-gallery-image">
        {item.type === "video" ? (
          // <video
          //   controls
          //   src={item.original}
          //   style={{ maxWidth: "100%", maxHeight: "100%" }}
          // />
          // <ReactPlayer url={item.original} controls />
          <video controls style={{ maxWidth: "100%", maxHeight: "100%" }}>
            <source src={item.original} />
          </video>
        ) : (
          <img
            src={item.original}
            alt={item.originalAlt}
            srcSet={item.srcSet}
            sizes={item.sizes}
            title={item.originalTitle}
            style={{ maxWidth: "100%", maxHeight: "100%" }}
          />
        )}
        {item.description && (
          <span className="image-gallery-description">{item.description}</span>
        )}
      </div>
    );
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleModalCancel = () => {
    setIsModalOpen(false);
  };

  const drawMonitoringCase = async () => {
    try {
      if (!props.data?.ownedLayerName) {
        console.error("No owned layer name in props data");
        return;
      }

      const mapInfo = await getMapInfo(window.mobileAppUrl);
      let pathId = getLayerId(mapInfo, props.data.ownedLayerName);

      const sourceFeatureLayer = new FeatureLayer({
        url: `${window.mobileAppUrl}/${pathId}`,
      });

      const monitoringCasesLayer = props.map?.findLayerById(
        "MonitoringCasesGraphicLayer"
      );

      if (
        !monitoringCasesLayer ||
        !(monitoringCasesLayer instanceof GraphicsLayer)
      ) {
        console.error(
          `GraphicsLayer with id "MonitoringCasesGraphicLayer" not found or is not a GraphicsLayer.`
        );
        return;
      }

      await sourceFeatureLayer.load();
      console.log("source feature layer", sourceFeatureLayer);
      const renderer = sourceFeatureLayer.renderer;

      if (props.data.feature) {
        props.data.feature.symbol = renderer.symbol;
        monitoringCasesLayer.add(props.data.feature);
        zoomToFeatures([props.data.feature], props.map);
      }
    } catch (error) {
      console.error("Error drawing monitoring case:", error);
    }
  };

  useEffect(() => {
    const drawMonitor = async () => {
      await drawMonitoringCase();
    };

    if (props.data?.feature && props.map) {
      drawMonitor();
    }

    return () => {
      try {
        const layer = props.map?.findLayerById("MonitoringCasesGraphicLayer");
        if (layer && layer.graphics) {
          layer.graphics.removeAll();
        }
      } catch (error) {
        console.error("Error cleaning up graphics layer:", error);
      }
    };
  }, []);

  if (!data || !data.feature || !data.feature.attributes) {
    return (
      <div
        style={{
          padding: "16px",
          fontSize: "14px",
          color: "#fff",
          textAlign: "center",
        }}
      >
        {t("studiesActor.no_data", { default: "لا توجد بيانات للعرض" })}
      </div>
    );
  }

  const dataTypeOptions = [
    {
      value: "sighting",
      label: t("studiesActor.sighting_monitor", { default: "تنوع أحيائي" }),
    },
    {
      value: "incident",
      label: t("studiesActor.incident_monitor", { default: "بلاغات" }),
    },
    {
      value: "site",
      label: t("studiesActor.site_monitor", { default: "مواقع هامة" }),
    },
  ];

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "10px",
      }}
    >
      {/* Case Details Section */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "start",
          }}
        >
          <img src={monitor_details} alt="" />
          <div
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}
          >
            <div style={{ fontWeight: "400", fontSize: "16px" }}>
              {getTitle()}
            </div>
            <div style={{ fontWeight: "400", fontSize: "12px" }}>
              {t("studiesActor.date", { date: getDate() })}
            </div>
            <div style={{ fontWeight: "400", fontSize: "12px" }}>
              {t("studiesActor.area", {
                area: getLocalizedText("AR_REGION", "EN_REGION") || "غير محدد",
              })}
            </div>
            <div style={{ fontWeight: "400", fontSize: "12px" }}>
              {t("studiesActor.reserve", {
                reserve:
                  getLocalizedText(
                    "AR_PROTECTED_AREA_NAME",
                    "EN_PROTECTED_AREA_NAME"
                  ) || "غير محدد",
              })}
            </div>
            {dataType === "sighting" && attributes.SAME_SPECIES_COUNT && (
              <div style={{ fontWeight: "400", fontSize: "12px" }}>
                {`${t("studiesActor.count")}: ${attributes.SAME_SPECIES_COUNT}`}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Monitoring Data Section */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              alignItems: "center",
            }}
          >
            <img src={monitor_data} alt="" />
            <div style={{ fontWeight: "400", fontSize: "16px" }}>
              {getTitle()}
            </div>
          </div>
          <img
            src={pen_icon}
            alt=""
            style={{
              cursor: "pointer",
              opacity: isEditingData ? 0.5 : 1,
              filter: isEditingData ? "brightness(0.7)" : "none",
            }}
            onClick={() => setIsEditingData(!isEditingData)}
          />
        </div>
        <div>
          {/* Data Type Dropdown */}
          <div>
            <label className="selectLabelStyle">
              {t("studiesActor.observation_type", { default: "نوع الرصد" })}
            </label>
            <Input
              value={t(`studiesActor.${dataType}`) || ""}
              placeholder={t("studiesActor.observation_type_placeholder", {
                default: "اختر نوع الرصد",
              })}
              readOnly={true}
            />
            {/* <Select
              value={dataType}
              placeholder={t("studiesActor.observation_type_placeholder", {
                default: "اختر نوع الرصد",
              })}
              disabled={!isEditingData}
              style={{ width: "100%" }}
              onChange={(value) => {
                setSelectedDataType(value);
                // Reset relevant fields when data type changes
                setFormData((prev) => ({
                  ...prev,
                  AR_INCIDENT_TYPE: null,
                  EN_INCIDENT_TYPE: null,
                  AR_INCIDENT_PRIORITY: null,
                  EN_INCIDENT_PRIORITY: null,
                  AR_INCIDENT_RELATE: null,
                  EN_INCIDENT_RELATE: null,
                  AR_ECOLOGY_CATEGORY: null,
                  EN_ECOLOGY_CATEGORY: null,
                  SAME_SPECIES_COUNT: null,
                  AR_SITE_TYPE: null,
                  EN_SITE_TYPE: null,
                  AR_IMPORTANCE_LEVEL: null,
                  EN_IMPORTANCE_LEVEL: null,
                  AR_SITE_NAME: null,
                  EN_SITE_NAME: null,
                }));
              }}
              options={dataTypeOptions}
            /> */}
          </div>

          {/* Conditional Inputs Based on Data Type */}
          {dataType === "incident" && (
            <>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.incident_type", { default: "نوع البلاغ" })}
                </label>
                {renderInputField(
                  language === "ar" ? "AR_INCIDENT_TYPE" : "EN_INCIDENT_TYPE",
                  formData[
                    language === "ar" ? "AR_INCIDENT_TYPE" : "EN_INCIDENT_TYPE"
                  ],
                  t("studiesActor.incident_type_placeholder", {
                    default: "اختر نوع البلاغ",
                  }),
                  !isEditingData
                )}
              </div>
              {/* <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.priority", { default: "الأولوية" })}
                </label>
                {renderInputField(
                  language === "ar"
                    ? "AR_INCIDENT_PRIORITY"
                    : "EN_INCIDENT_PRIORITY",
                  formData[
                    language === "ar"
                      ? "AR_INCIDENT_PRIORITY"
                      : "EN_INCIDENT_PRIORITY"
                  ],
                  t("studiesActor.priority_placeholder", {
                    default: "اختر الأولوية",
                  }),
                  !isEditingData
                )}
              </div>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.incident_relate", {
                    default: "العلاقة بالبلاغ",
                  })}
                </label>
                {renderInputField(
                  language === "ar"
                    ? "AR_INCIDENT_RELATE"
                    : "EN_INCIDENT_RELATE",
                  formData[
                    language === "ar"
                      ? "AR_INCIDENT_RELATE"
                      : "EN_INCIDENT_RELATE"
                  ],
                  t("studiesActor.incident_relate_placeholder", {
                    default: "اختر العلاقة",
                  }),
                  !isEditingData
                )}
              </div> */}
            </>
          )}

          {dataType === "sighting" && (
            <>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.ecology_category")}
                </label>
                {renderInputField(
                  language === "ar" ? "AR_NCW_CATEGORY" : "EN_NCW_CATEGORY",
                  formData[
                    language === "ar" ? "AR_NCW_CATEGORY" : "EN_NCW_CATEGORY"
                  ],
                  t("studiesActor.ecology_category_placeholder"),
                  !isEditingData
                )}
              </div>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.specie_category")}
                </label>
                {renderInputField(
                  language === "ar" ? "AR_SPECIE" : "EN_SPECIE",
                  formData[language === "ar" ? "AR_SPECIE" : "EN_SPECIE"],
                  t("studiesActor.specie_placeholder"),
                  !isEditingData
                )}
              </div>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.threats_type")}
                </label>
                {renderInputField(
                  language === "ar" ? "AR_THREATS_TYPE" : "EN_THREATS_TYPE",
                  formData[
                    language === "ar" ? "AR_THREATS_TYPE" : "EN_THREATS_TYPE"
                  ],
                  t("studiesActor.threats_type_placeholder"),
                  !isEditingData
                )}
              </div>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.place_type")}
                </label>
                {renderInputField(
                  language === "ar" ? "AR_PLACE_TYPE" : "EN_PLACE_TYPE",
                  formData[
                    language === "ar" ? "AR_PLACE_TYPE" : "EN_PLACE_TYPE"
                  ],
                  t("studiesActor.place_type_placeholder"),
                  !isEditingData
                )}
              </div>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.specie_status")}
                </label>
                {renderInputField(
                  language === "ar" ? "AR_SPECIE_STATUS" : "EN_SPECIE_STATUS",
                  formData[
                    language === "ar" ? "AR_SPECIE_STATUS" : "EN_SPECIE_STATUS"
                  ],
                  t("studiesActor.specie_status_placeholder"),
                  !isEditingData
                )}
              </div>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.sightings_count")}
                </label>
                {renderInputField(
                  "SAME_SPECIES_COUNT",
                  formData.SAME_SPECIES_COUNT,
                  t("studiesActor.count_placeholder"),
                  !isEditingData,
                  "number"
                )}
              </div>
            </>
          )}

          {dataType === "site" && (
            <>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.site_type", { default: "نوع الموقع" })}
                </label>
                {renderInputField(
                  language === "ar" ? "AR_SITE_TYPE" : "EN_SITE_TYPE",
                  formData[language === "ar" ? "AR_SITE_TYPE" : "EN_SITE_TYPE"],
                  t("studiesActor.site_type_placeholder", {
                    default: "اختر نوع الموقع",
                  }),
                  !isEditingData
                )}
              </div>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.importance_level", {
                    default: "درجة الأهمية",
                  })}
                </label>
                {renderInputField(
                  language === "ar"
                    ? "AR_IMPORTANCE_LEVEL"
                    : "EN_IMPORTANCE_LEVEL",
                  formData[
                    language === "ar"
                      ? "AR_IMPORTANCE_LEVEL"
                      : "EN_IMPORTANCE_LEVEL"
                  ],
                  t("studiesActor.importance_level_placeholder", {
                    default: "اختر درجة الأهمية",
                  }),
                  !isEditingData
                )}
              </div>
              <div>
                <label className="selectLabelStyle">
                  {t("studiesActor.site_name", { default: "اسم الموقع" })}
                </label>
                {renderInputField(
                  language === "ar" ? "AR_SITE_NAME" : "EN_SITE_NAME",
                  formData[language === "ar" ? "AR_SITE_NAME" : "EN_SITE_NAME"],
                  t("studiesActor.site_name_placeholder", {
                    default: "أدخل اسم الموقع",
                  }),
                  !isEditingData,
                  "text"
                )}
              </div>
            </>
          )}
        </div>

        {isEditingData && (
          <div style={{ display: "flex", gap: "10px", marginTop: "15px" }}>
            <button
              className="SearchBtn"
              onClick={handleSave}
              disabled={isLoading || !hasDataChanges()}
              style={{
                backgroundColor: hasDataChanges() ? "#b45333" : "#6c757d",
                cursor: hasDataChanges() ? "pointer" : "not-allowed",
              }}
            >
              {t("studiesActor.save", { default: "حفظ" })}
            </button>
            <button
              className="SearchBtn"
              onClick={handleCancelDataEdit}
              disabled={isLoading}
              style={{
                backgroundColor: "#b45333",
              }}
            >
              {t("studiesActor.cancel", { default: "إلغاء" })}
            </button>
          </div>
        )}

        {isEditingData && hasDataChanges() && (
          <div
            style={{
              marginTop: "10px",
              fontSize: "12px",
              color: "#ffc107",
              fontStyle: "italic",
            }}
          >
            {t("studiesActor.unsaved_changes", {
              default: "يوجد تغييرات غير محفوظة",
            })}
          </div>
        )}
      </div>

      {/* Gallery Section */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
          display: "flex",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              alignItems: "start",
            }}
          >
            <img src={gallery_icon} alt="" />
            <div style={{ fontWeight: "700", fontSize: "15px" }}>
              {t("studiesActor.gallery")}
            </div>
          </div>

          <div className="upload-file">
            <input
              type="file"
              style={{ display: "none" }}
              multiple
              ref={fileInputRef}
              accept="image/*,video/*"
              onChange={(e) => handleMediaUpload(Array.from(e.target.files))}
            />
            <div
              onClick={handleFileUploadClick}
              style={{
                borderRadius: "50%",
                width: "24px",
                height: "24px",
                backgroundColor: isUploadingMedia ? "#ccc" : "#F4DFD9",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: isUploadingMedia ? "not-allowed" : "pointer",
              }}
            >
              <FaPlus size={16} color={isUploadingMedia ? "#999" : "#B55433"} />
            </div>
          </div>
        </div>
        <div
          style={{
            display: "flex",
            gap: "10px",
            flexWrap: "wrap",
          }}
        >
          {displayMedia.slice(0, 8).map((item, index) => (
            <div
              key={index}
              style={{
                position: "relative",
                flex: "0 0 calc(25% - 7.5px)",
              }}
            >
              <img
                src={item.thumbnail}
                alt=""
                style={{
                  width: "100%",
                  height: "50px",
                  objectFit: "cover",
                  borderRadius: "8px",
                  cursor: "pointer",
                }}
                onClick={showModal}
              />
              <FaTrashAlt
                style={{
                  position: "absolute",
                  top: "5px",
                  left: "5px",
                  color: "#fff",
                  cursor: "pointer",
                  backgroundColor: "rgba(0,0,0,0.5)",
                  padding: "2px",
                  borderRadius: "3px",
                }}
                onClick={() => handleMediaDelete(index)}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Notes Section */}
      <div
        className="generalSearchCardWithoutHover"
        style={{
          padding: "16px",
        }}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              alignItems: "center",
            }}
          >
            <img src={monitor_data} alt="" />
            <div style={{ fontWeight: "400", fontSize: "16px" }}>
              {dataType == "incident"
                ? t("studiesActor.description")
                : t("studiesActor.notes")}
            </div>
          </div>
          <img
            src={pen_icon}
            alt=""
            style={{
              cursor: "pointer",
              opacity: isEditingNotes ? 0.5 : 1,
              filter: isEditingNotes ? "brightness(0.7)" : "none",
            }}
            onClick={() => setIsEditingNotes(!isEditingNotes)}
          />
        </div>
        {isEditingNotes ? (
          <div>
            <Input.TextArea
              value={formData[getNotesFieldName()] || ""}
              placeholder={t("studiesActor.notes_placeholder", {
                default: "أدخل الملاحظات",
              })}
              rows={3}
              maxLength={1000}
              style={{
                color: "#000000 !important",
                backgroundColor: "#ffffff !important",
                border: "1px solid #d9d9d9",
                borderRadius: "6px",
              }}
              onChange={(e) => {
                const fieldName = getNotesFieldName();
                handleFieldChange(fieldName, e.target.value);
              }}
            />

            <div style={{ display: "flex", gap: "10px", marginTop: "10px" }}>
              <button
                className="SearchBtn"
                onClick={handleSave}
                disabled={isLoading || !hasNotesChanges()}
                style={{
                  backgroundColor: hasNotesChanges() ? "#b45333" : "#6c757d",
                  cursor: hasNotesChanges() ? "pointer" : "not-allowed",
                }}
              >
                {t("studiesActor.save", { default: "حفظ" })}
              </button>
              <button
                className="SearchBtn"
                onClick={handleCancelNotesEdit}
                disabled={isLoading}
                style={{
                  backgroundColor: "#b45333",
                }}
              >
                {t("studiesActor.cancel", { default: "إلغاء" })}
              </button>
            </div>

            {hasNotesChanges() && (
              <div
                style={{
                  marginTop: "10px",
                  fontSize: "12px",
                  color: "#ffc107",
                  fontStyle: "italic",
                }}
              >
                {t("studiesActor.unsaved_changes", {
                  default: "يوجد تغييرات غير محفوظة",
                })}
              </div>
            )}
          </div>
        ) : (
          <div
            style={{ fontWeight: "400", fontSize: "12px", textAlign: "start" }}
          >
            {getNotes() ||
              t("studiesActor.no_notes", { default: "لا توجد ملاحظات" })}
          </div>
        )}
      </div>

      {(dataType === "sighting" || dataType === "site") &&
        (attributes.TEMPERATURE ||
          attributes.HUMIDITY ||
          attributes.WIND_SPEED) && (
          <div
            className="generalSearchCardWithoutHover"
            style={{
              padding: "16px",
            }}
          >
            <div
              style={{
                display: "flex",
                gap: "10px",
                alignItems: "center",
                marginBottom: "10px",
              }}
            >
              <img src={monitor_data} alt="" />
              <div style={{ fontWeight: "400", fontSize: "16px" }}>
                {t("studiesActor.environmental_data", {
                  default: "البيانات البيئية",
                })}
              </div>
            </div>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "10px",
                fontSize: "12px",
              }}
            >
              {attributes.TEMPERATURE && (
                <div>
                  {t("studiesActor.temperature", { default: "درجة الحرارة" })}:{" "}
                  {attributes.TEMPERATURE}°C
                </div>
              )}
              {attributes.HUMIDITY && (
                <div>
                  {t("studiesActor.humidity", { default: "الرطوبة" })}:{" "}
                  {attributes.HUMIDITY}%
                </div>
              )}
              {attributes.WIND_SPEED && (
                <div>
                  {t("studiesActor.wind_speed", { default: "سرعة الرياح" })}:{" "}
                  {attributes.WIND_SPEED} كم/س
                </div>
              )}
              {attributes.VISIBILITY && (
                <div>
                  {t("studiesActor.visibility", { default: "الرؤية" })}:{" "}
                  {attributes.VISIBILITY} م
                </div>
              )}
            </div>
          </div>
        )}

      <div
        className="generalSearchCardWithoutHover"
        style={{ padding: "16px" }}
      >
        <button className="SearchBtn" style={{ marginTop: "10px" }}>
          {t("studiesActor.share")}
        </button>

        <div style={{ display: "flex", gap: "10px", marginTop: "10px" }}>
          {!isEditingData && !isEditingNotes ? (
            <button
              className="SearchBtn"
              onClick={() => {
                setIsEditingData(true);
                setIsEditingNotes(true);
              }}
              disabled={isLoading}
            >
              {t("studiesActor.edit_all", { default: "تعديل الكل" })}
            </button>
          ) : (
            <>
              <button
                className="SearchBtn"
                onClick={handleSave}
                disabled={isLoading || !hasFormChanges()}
                style={{
                  backgroundColor: hasFormChanges() ? "#b45333" : "#6c757d",
                  cursor: hasFormChanges() ? "pointer" : "not-allowed",
                }}
              >
                {t("studiesActor.save_all", { default: "حفظ الكل" })}
              </button>
              <button
                className="SearchBtn"
                onClick={() => {
                  if (hasFormChanges()) {
                    Modal.confirm({
                      title: t("studiesActor.unsaved_changes_title", {
                        default: "تغييرات غير محفوظة",
                      }),
                      content: t("studiesActor.unsaved_changes_message", {
                        default:
                          "لديك تغييرات غير محفوظة. هل تريد المتابعة بدون حفظ؟",
                      }),
                      okText: t("studiesActor.discard_changes", {
                        default: "تجاهل التغييرات",
                      }),
                      cancelText: t("studiesActor.continue_editing", {
                        default: "متابعة التعديل",
                      }),
                      onOk: () => {
                        resetForm();
                        setIsEditingData(false);
                        setIsEditingNotes(false);
                        setSelectedDataType(getDataType());
                      },
                    });
                  } else {
                    setIsEditingData(false);
                    setIsEditingNotes(false);
                    setSelectedDataType(getDataType());
                  }
                }}
                disabled={isLoading}
                style={{
                  backgroundColor: "#b45333",
                }}
              >
                {t("studiesActor.cancel_all", { default: "إلغاء الكل" })}
              </button>
            </>
          )}
        </div>

        {(isEditingData || isEditingNotes) && hasFormChanges() && (
          <div
            style={{
              marginTop: "10px",
              fontSize: "12px",
              color: "#ffc107",
              fontStyle: "italic",
            }}
          >
            {t("studiesActor.unsaved_changes", {
              default: "يوجد تغييرات غير محفوظة",
            })}
          </div>
        )}
      </div>

      <Modal
        className="study-actor-modal"
        title={t("studiesActor.gallery_modal_title")}
        closable={{ "aria-label": "Custom Close Button" }}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleModalCancel}
        width={"50%"}
      >
        <ReactImageGallery items={displayMedia} renderItem={customRenderItem} />
      </Modal>
    </div>
  );
}
