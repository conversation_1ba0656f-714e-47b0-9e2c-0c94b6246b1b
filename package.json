{"name": "map-explorer", "homepage": "/wildlifeexplorer", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^4.7.0", "@arcgis/core": "4.21", "@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@fortawesome/fontawesome-free": "^5.15.4", "@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-brands-svg-icons": "^5.15.4", "@fortawesome/free-regular-svg-icons": "^5.15.4", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fortawesome/react-fontawesome": "^0.1.16", "@material-ui/icons": "^4.11.3", "@mui/icons-material": "^5.2.1", "@mui/material": "^5.2.3", "@mui/styled-engine-sc": "^5.1.0", "@reduxjs/toolkit": "^1.8.3", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "antd": "^5.18.3", "apexcharts": "^3.35.4", "arabic-digits": "^1.1.0", "axios": "^0.24.0", "fast-deep-equal": "^3.1.3", "file-saver": "^2.0.5", "hijri-date-picker": "^1.2.1", "i18next": "^21.8.14", "i18next-browser-languagedetector": "^6.1.4", "i18next-http-backend": "^1.4.0", "moment-hijri": "^2.1.2", "re-resizable": "^6.9.9", "react": "^17.0.2", "react-apexcharts": "^1.4.0", "react-bootstrap": "^2.0.3", "react-burger-menu": "^3.0.6", "react-color": "^2.19.3", "react-csv": "^2.2.2", "react-dom": "^17.0.2", "react-draggable": "^4.4.6", "react-export-excel": "^0.5.3", "react-full-screen": "^1.1.0", "react-i18next": "^11.18.1", "react-icons": "^5.1.0", "react-image-gallery": "^1.3.0", "react-joyride": "2.5.5 --save", "react-media": "^1.10.0", "react-multi-date-picker": "^4.1.2", "react-onclickoutside": "^6.12.1", "react-player": "^2.16.0", "react-qr-code": "^2.0.12", "react-redux": "^8.0.2", "react-resizable": "^3.0.5", "react-reveal": "^1.2.2", "react-router-dom": "^6.0.2", "react-scripts": "4.0.3", "react-tabs": "^3.2.3", "react-to-print": "^2.14.7", "recharts": "^2.1.10", "state-pool": "^0.7.1", "styled-components": "^5.3.3", "sunburst-chart": "^1.16.2", "web-vitals": "^1.1.2", "xlsx": "^0.18.5"}, "overrides": {"react-export-excel": {"xlsx": "0.18.5"}}, "scripts": {"start": "react-scripts start", "build": "node --max-old-space-size=8192 node_modules/react-scripts/scripts/build.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-video-thumbnail": "^0.1.3", "tailwindcss": "^3.4.3"}}