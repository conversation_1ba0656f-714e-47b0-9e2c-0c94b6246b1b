import React, { useEffect, useRef } from "react";
import ReactExport from "react-export-excel";
import { useTranslation } from "react-i18next";

const ExcelFile = ReactExport.ExcelFile;
const ExcelSheet = ReactExport.ExcelFile.ExcelSheet;
const ExcelColumn = ReactExport.ExcelFile.ExcelColumn;

function ExportCSV({
  // isActive,
  dataSet,
  columns,
  labels,
  layerName,
  isForceClick,
  setExportedData,
}) {
  const [t] = useTranslation("common");
  const downloadExcelRef = useRef(null);
  // useEffect(() => {
  //   console.log("csv data set :", dataSet);
  //   console.log("set exported data", setExportedData);
  //   if (dataSet?.length && isForceClick) {
  //     // if (setExportedData)
  //     //   setExportedData({
  //     //     dataSet: [],
  //     //     columns: [],
  //     //     labels: [],
  //     //     layerName: "",
  //     //     whereClause: "",
  //     //   });
  //     // if (downloadExcelRef?.current) {
  //     //   downloadExcelRef?.current?.click();
  //     // }
  //   }
  // }, [setExportedData]);

  // useEffect(() => {
  //   columns.sort();
  // }, []);

  const handleDataSet = () => {
    // console.log("data set inside export CSV", dataSet);
    // console.log("columns inside export CSV", columns);
    // console.log("labels inside export CSV", labels);
    return dataSet;
  };
  return (
    <>
      {
        <ExcelFile
          filename={layerName + "CSV"}
          element={
            <span ref={downloadExcelRef}>
              {/* <Tooltip placement="topLeft" title={` استخراج ملف CSV `}> */}

              {t("extractExcelFile")}
              {/* </Tooltip> */}
            </span>
          }
        >
          <ExcelSheet data={handleDataSet()} name="AttributeTable">
            {labels.map((head, index) => (
              <ExcelColumn
                label={head}
                value={(col) => {
                  return col[columns[index]]
                    ? col[columns[index]]
                    : t("without");
                }}
              />
            ))}
          </ExcelSheet>
        </ExcelFile>
      }
    </>
  );
}

export default ExportCSV;
