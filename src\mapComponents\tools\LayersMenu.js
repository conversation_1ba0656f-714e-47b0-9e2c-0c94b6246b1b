import React from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import TocComponent from "./TocComponent";
import { useTranslation } from "react-i18next";
import Draggable from "react-draggable";

export default function LayersMenu(props) {
  const { t, i18n } = useTranslation("dashboard");

  const [x, setX] = React.useState(0);
  const [y, setY] = React.useState(0);

  const handleDrag = (e, position) => {
    setX(position.x);
    setY(position.y);
  };

  return (
    // <Fade left collapse>
    <Draggable
      position={{ x, y }}
      onDrag={handleDrag}
      bounds={{
        left:
          i18n.language === "ar"
            ? -(window.innerWidth - 400)
            : -window.innerWidth,
        top: -300,
        right: i18n.language === "ar" ? window.innerWidth - 400 : 0, // Subtract component width
        bottom: window.innerHeight - 450, // Subtract component height
      }}
    >
      <div
        className="leftToolMenu toolsMenu inquiryTool layersMenu"
        style={{ top: "60px", insetInlineEnd: "12px" }}
      >
        <Fade left>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              color: "#000",
              padding: "8px",
            }}
          >
            <div style={{ fontWeight: "bold", color: "#fff" }}>
              {t("layers")}
            </div>

            <span
            // style={{
            //   width: "100%",
            //   float: "left",
            //   textAlign: "left",
            //   marginLeft: "5px",
            // }}
            >
              {" "}
              <FontAwesomeIcon
                icon={faTimes}
                style={{
                  marginTop: "5px",
                  marginRight: "5px",
                  marginLeft: "5px",
                  cursor: "pointer",
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  props.closeToolsData();
                }}
              />
            </span>
          </div>
          <TocComponent
            languageState={props.languageState}
            mainData={props.mainData}
            map={props.map}
          />
        </Fade>
      </div>
    </Draggable>

    // </Fade>
  );
}
