import React, { useState, useEffect } from "react";

function usePersistentState(key, defaultValue, componentName = "Default") {
  // Load the initial value from sessionStorage
  const uniqueKey = `${componentName}:${key}`;

  const storedValue = sessionStorage.getItem(uniqueKey);
  const [value, setValue] = useState(
    storedValue ? JSON.parse(storedValue) : defaultValue
  );

  useEffect(() => {
    let serializedValue = JSON.stringify(value);
    if (new Blob([serializedValue]).size < 5 * 1024 * 1024) {
      // Rough 5MB limit
      console.warn("data saved", value);
    } else {
      console.warn("Data too large for localStorage, skipping save.", value);
    }
    try {
      sessionStorage.setItem(uniqueKey, value ? serializedValue : "");
    } catch (error) {
      console.warn("error save data", error);
    }
  }, [uniqueKey, value]);

  return [value, setValue];
}

export default usePersistentState;
