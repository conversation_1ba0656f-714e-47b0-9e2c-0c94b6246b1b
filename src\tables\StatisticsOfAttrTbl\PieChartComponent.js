import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip } from "recharts";
import { getLayerId, queryTask } from "../../helper/common_func";

const PieChartComponent = (props) => {
  const handleOnHover = (data) => {
    props.handleOnHoverTable(data);
  }

  return (
    <>
      <div id="chartDiv">
        <PieChart width={400} height={400}>
          <Pie
            data={props.chartData}
            cx={200}
            cy={200}
            labelLine={false}
            outerRadius={150}
            fill="#8884d8"
            dataKey="value"
            onMouseEnter={(data, index) => {
              console.log("mouse hovered", data);
              handleOnHover(data);
            }}
          >
            {props.chartData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={props.chartColors[index % props.chartColors.length]}
              />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </div>
    </>
  );
};

export default PieChartComponent;
