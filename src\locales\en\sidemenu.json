{"sideLinks": {"coordinateSearch": "Search By Coordinates", "generalSearch": "General Search", "interactiveMap": "Interactive Map", "MetaSearch": "Search By Attributes", "measurementTools": "Measurement Tools", "contact": "Contact Us", "paint": "Drawing", "layersMenu": "Layers Menu", "language": "العربية", "changeLangTooltip": "Change Language", "print": "Print", "Espeicia": "eSpecia (Fulk)", "bookmark": "Add Bookmark", "importFiles": "Import Files", "exportGoogle": "Export To Google (K-M-L)", "marsad": "Marsad", "incidentsDashboard": "Incidents Dashboard ", "wireless_devices": "Wireless devices", "studies-actor": "Studies Actor", "add-studies-actor": "Add New Trip", "update-studies-actor": "Update Trip"}, "profile": "Profile", "import": "Import", "file": "File", "link": "Link", "the_link": "Link", "enter_link": "Enter Link", "enter_token": "<PERSON>ter <PERSON>", "add_token": "Add <PERSON>", "logout": "Logout", "login": "<PERSON><PERSON>", "language": "Language", "help": "Help", "mainTitle": "GIS Map Explorer", "titleEdition": "Version No. ", "upload": "Upload", "add": "Add", "sideMsg": "We would like to point out that the measurements, areas and uses of plots of land and plans are indicative and there may be some differences in the data, which are taken into account with the periodic update, and you can contact via e-mail", "addFileToMapError": "Error during adding file to map. Please try again", "uploadFilesError": "Error occured during upload the file. Please try again", "mainBaseMap": "Base Map", "mapName": "Map Name", "enter_mapName": "Enter Map Name", "area": "Area", "tools": "Tools", "drawingShape": "Drawing shapes", "my_maps": "My Maps", "farasan_islands_map": "Farasan Islands map", "removeAll": "Remove All", "people_vehicles": "People and Vehicles", "violations": "Violations", "general": "General", "save": "Save", "special": "Special", "shared": "Shared", "study_areas": "Study Areas", "choose_study_area": "Choose Study Area", "areas": "Areas", "study_area_name": "Study Area Name", "area_name": "Area Name", "clearAll": "Clear All", "stopDraw": "Stop draw", "add-study-validation": "All inputs are required", "trip-name-validation": "The trip name must be between 3 and 100 characters.", "description-validation": "The trip description must be between 3 and 1000 characters.", "team-members-validation": "There are no specific members.", "team-leader-validation": "There is no team leader", "pick-date-validation": "Please select a date", "date-pick-validation": "The start date must be before or equal to the end date", "graphics-study-validation": "Please complete the required fields.", "files-count-validation": "You can upload a maximum of 3 files", "maps-validation": "Please attach a map", "files-upload-validation": "Please upload at least one attachment to proceed", "files-size-validation": "File exceeds the 10MB limit. Please select a smaller one", "files-format-validation": "The selected file format is not supported", "undoWarningMessage": "No graphics to undo", "redoWarningMessage": "No graphics to redo", "logoutMessage": "Do you want to logout ?", "confirm": "Confirm", "cancel": "Cancel", "showFileToMapError": "Error during showing file to map. Please try again", "welcome": "Welcome", "current": "Current", "my_missions": "My Missions", "under_review": "Under Review", "under_approval": "Under Approval", "finished": "Finished", "from": "from", "to": "to", "DeletionSuccess": "Deletion Successfully", "ErrorOccurd": "An erro occurd, please try again later", "deleteTripConfirmation": "Are you sure you want to delete mission : ", "studiesActor": {"fields_required": "Please enter the required data", "specie_category": "Object type", "specie_placeholder": "Choose object type", "threats_type": "Threat type", "threats_type_placeholder": "Choose threat type", "specie_status": "Object status", "specie_status_placeholder": "Choose object status", "no_data": "No data", "invalidKMLFileType": "Please upload only a KML or KMZ file", "study_freezed": "Study has been successfully stopped", "freeze": "freeze", "notes_placeholder": "Enter notes", "incident_relate_placeholder": "Select the report link", "incident_relate": "Report link", "priority_placeholder": "Choose priority", "ecology_category_placeholder": "Choose a classification", "ecology_category": "Classification", "site_name": "Site name", "sighting": "Biodiversity", "incident": "Reports", "site": "Sites of Interest", "site_title": "Site details", "sighting_title": "Biodiversity Sighting Details", "incident_title": "Incident details", "study_confirmed": "Study confirmed successfully", "study_accepted": "Study accepted successfully", "graphics_saved": "Saved successfully", "no_graphics_to_save": "No drawings to be saved", "add_more_images": "Add more images", "observation_count": "Observation count", "observation_description": "Observation description", "report_name": "Report name", "monitoring_case": "Monitoring case", "monitoring_case_info": "Monitoring case info", "importSuccess": "The file was imported successfully.", "media_uploaded": "Media files uploaded successfully", "media_upload_failed": "An error occurred while uploading media files.", "image_deleted": "The file has been deleted and the changes saved successfully", "delete_error": "An error occurred while deleting the image", "update_error": "An error occurred while updating data.", "images_uploaded": "Images uploaded Successfully", "upload_error": "An error occurred while uploading images", "photos_upload_failed": "Error while uploading photos", "edit_all": "Edit all", "cancel_all": "Cancel all", "save_all": "Save all", "invalid_file_type": "Invalid file type", "files_size_validation": "File size is too large. Maximum 10MB", "files_format_validation": "Unsupported file type. Please upload images only.", "importance_level": "Importance level", "unsaved_changes_title": "Unsaved changes", "unsaved_changes": "Unsaved changes", "unsaved_changes_message": "You have unsaved changes. Do you want to continue without saving?", "discard_changes": "Discard changes", "continue_editing": "Continue editing", "count": "Count", "species_status": "Specie status", "sightings_count": "Sightings count", "ncw_category": "Category", "no_notes": "No notes", "update_success": "Update success", "save": "save", "observer": "Observer", "reviewer": "Reviewer", "incident_type": "Incident type", "species_name": "Specie name", "site_type": "Site type", "area_category": "Area category", "priority": "Priority", "other_paths_label": "Show only other paths on the map", "this_path_label": "Show this path only on the map", "no_speed_data": "Speed data is not available", "no_height_data": "Height data is not available", "no_wind_data": "Wind data is not available", "no_tempreature_data": "Temperature data is not available", "no_time_data": "Time data is not available", "no_paths_list": "There are no paths associated with this study", "no_monitoring_cases_list": "There are no monitoring cases associated with this study", "update_trip": "Update trip", "trip_name_label": "Trip Name", "trip_name_placeholder": "Wuaal Reserve Study", "from_label": "From", "from_placeholder": "From", "to_label": "To", "to_placeholder": "To", "manager_label": "Team Manager", "manager_placeholder": "<PERSON>", "select_team_label": "Select Team", "select_team_placeholder": "Select", "define_scope": "Define Study Scope", "description_label": "Description", "description_placeholder": "Enter study description", "attach_file": "Attach File", "attach_map": "Attach Map", "create_trip": "Create Trip", "attached_map_name": "Uruq Bani Ma'arid Reserve", "search": "Search", "search_placeholder": "Search by trip name or number", "monitoring_data": "Monitoring Data", "classification": "Classification", "classification_placeholder": "Classification", "place_type": "Place Type", "place_type_placeholder": "Place Type", "incident_type_placeholder": "Choose the report type", "notes": "Notes", "count_placeholder": "Enter the number", "description": "description", "notes_text": "The Ghada plant is used in many countries for rangeland rehabilitation and desert roadside afforestation, and is also used in furniture, paper, and dye industries.", "gallery": "Gallery", "gallery_modal_title": "Gallery and Videos", "share_placeholder": "With", "edit": "Edit", "observation_type": "Observation Type", "observation_type_placeholder": "Observation Type", "importance": "Importance", "share": "Share", "plant_name": "Ghada Plant", "date": "Date: {{date}}", "area": "Area: {{area}}", "reserve": "Reserve: {{reserve}}", "description_text": "Wuaal Reserve study in terms of the number of animals and plants and the inventory of new or discovered classifications. Wuaal Reserve study in terms of the number of animals and plants and the inventory of new or discovered classifications.", "team_members": "Team Members", "attachment_map_name": "Wuaal Reserve Boundary Map", "sample_map_name": "Jubail Marine Sanctuary", "my_paths": "My Paths", "monitoring_cases": "Monitoring Cases", "duration": "Duration", "team": "Team", "attachments": "Attachments", "maps": "Maps", "send_to_label": "Send to", "send_to_placeholder": "@ahmed", "send_for_review": "Send for review", "send": "Send", "path_heading": "Path for Imam <PERSON><PERSON> bin <PERSON>z Royal Reserve #123254", "distance": "Distance: {{distance}}", "export_kml": "Export KML", "import_file": "Import File", "time": "Time", "total_time": "Total time: ", "move_time": "Move time: ", "stop_time": "Stop time: ", "weather": "Weather", "max_temp": "Max temperature {{temp}} °C", "max_wind": "Max wind speed {{speed}} km/h", "height": "Height", "height_value": "{{height}}km above sea level", "speed": "Speed", "max_speed": "Max speed {{speed}}km/h", "avg_speed": "Average speed {{speed}} km/h", "return": "Return", "enter_notes": "Enter notes", "confirm": "Confirm", "accept": "Accept", "cancel": "Cancel"}}