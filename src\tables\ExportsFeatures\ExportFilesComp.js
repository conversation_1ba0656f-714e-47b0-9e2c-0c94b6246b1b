import React, { useCallback, useEffect, useState } from "react";
import { Menu, Dropdown, Space, Button } from "antd";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFileExport } from "@fortawesome/free-solid-svg-icons";
import {
  executeGPTool,
  showLoading,
  getLayerId,
  queryTask,
  getFeatureDomainName,
  getFromEsriRequest,
  convertTimeStampToDate,
} from "../../helper/common_func";
import { notificationMessage } from "../../helper/utilsFunc";
import ExportCSV from "./ExportCSV";
import {
  PARCEL_LANDS_LAYER_NAME,
  externalBtnsForTblData,
} from "../../helper/constants";
import { useTranslation } from "react-i18next";
import { Tooltip } from "@mui/material";
import * as XLSX from "xlsx";

function ExportFilesComp({
  activeLayer,
  dataSet,
  columns,
  filterWhereClause,
  isDepend,
  map,
  colFilterWhere,
}) {
  console.log("data set in export files comp :", dataSet);
  const { t } = useTranslation("print", "common");
  //const [exportCSVActive, setExportCSVActive] = useState(false);
  const [exportedData, setExportedData] = React.useState({
    dataSet: [],
    columns: [],
    labels: [],
    layerName: "",
    whereClause: "",
  });

  const mediaFieldsNames = (layerData) => {
    let mediaFields = layerData.layerMetadata?.outFields_Db.filter(
      (field) => field.is_media
    );
    let mediaFieldsNames = mediaFields.map((mediaField) => mediaField.enname);
    return mediaFieldsNames;
  };

  const getWhereFromMaxRestObjectIdLimit = function (
    neededWhereClause,
    layerName
  ) {
    return new Promise(async (resolve, reject) => {
      if (neededWhereClause == "1=1") {
        let layerdId = getLayerId(map.__mapInfo, layerName);
        queryTask({
          url: window.mapUrl + "/" + layerdId,
          where: neededWhereClause,
          outFields: ["OBJECTID"],
          returnGeometry: false,
          callbackResult: ({ features }) => {
            if (features && features.length) {
              resolve(
                "OBJECTID <= " +
                  features[features.length - 1].attributes["OBJECTID"]
              );
            } else {
              reject();
            }
          },
          callbackError(error) {
            reject();
          },
        });
      } else resolve(neededWhereClause);
    });
  };

  const exportFile = (fileType) => {
    if (dataSet.length) {
      let neededWhereClause = isDepend
        ? filterWhereClause.dep.filtered
          ? filterWhereClause.dep.filtered
          : filterWhereClause.dep.default
        : filterWhereClause.current;
      if (isDepend)
        neededWhereClause =
          isDepend === "1=1"
            ? colFilterWhere.dep.join(" AND ")
            : isDepend + " AND " + colFilterWhere.dep.join(" AND ");
      else
        neededWhereClause =
          neededWhereClause === "1=1"
            ? colFilterWhere.current.join(" AND ")
            : neededWhereClause;
      neededWhereClause = neededWhereClause || "1=1";

      getWhereFromMaxRestObjectIdLimit(
        neededWhereClause,
        activeLayer.layerData.layerName
      )
        .then((returnWhere) => {
          let whereClause = [
            {
              ["NCWEGDB.GIS." + activeLayer.layerData.layerName]: returnWhere,
            },
          ];
          let userObj = localStorage.getItem("user");
          if (userObj) userObj = JSON.parse(userObj);
          let params = {
            Filters: whereClause,
            FileType: fileType,
            token: userObj ? userObj.esriToken : "",
          };
          showLoading(true);
          //notification with it is succeeded
          notificationMessage(t("print:fileUploading"), 5);
          executeGPTool(
            window.exportFeaturesGPUrl,
            params,
            callBackExportFile,
            callbackExportError,
            "output_value",
            "execute",
            userObj?.esriToken
          );
        })
        .catch((error) => {
          showLoading(false);
        });
    } else {
      //notify there is no data to export
      showLoading(false);
    }
  };

  function callBackExportFile(result) {
    if (result) {
      let anchor = document.createElement("a");
      anchor.href = window.filesURL + result;
      // anchor.download = layersNames[activeLink].layerName
      document.body.appendChild(anchor);
      anchor.click();
    }
    showLoading(false);
  }
  function callbackExportError(err) {
    console.log("errorrr export", err);
    //notification with something error happened
    notificationMessage(t("print:ErrorOccurd"), 5);
    showLoading(false);
  }
  const exportPDF = async () => {
    let mapInfo = await getFromEsriRequest(window.dashboardMapUrl + "?f=pjson");

    let neededWhereClause = isDepend
      ? filterWhereClause.dep.filtered
        ? filterWhereClause.dep.filtered
        : filterWhereClause.dep.default
      : filterWhereClause.current;
    if (isDepend)
      neededWhereClause =
        isDepend === "1=1"
          ? colFilterWhere.dep.join(" AND ")
          : isDepend + " AND " + colFilterWhere.dep.join(" AND ");
    else
      neededWhereClause =
        neededWhereClause === "1=1"
          ? colFilterWhere.current.join(" AND ")
          : neededWhereClause;
    neededWhereClause = neededWhereClause || "1=1";
    let layerID = getLayerId(
      {
        info: {
          $layers: mapInfo,
        },
      },
      activeLayer.layerData?.layerName
    );
    let requiredOutFields = activeLayer.layerData.layerMetadata.outFields
      .filter(
        (outField) =>
          !mediaFieldsNames(activeLayer.layerData).includes(outField)
      )
      .sort();

    let requiredFields = activeLayer.layerData.layerMetadata.fields
      .filter((field) => requiredOutFields.includes(field.fieldName))
      .map((item) => item.fieldName);
    localStorage.setItem(
      "dataForExportPDF",
      layerID + ";" + neededWhereClause + ";" + requiredFields
    );
    window.open(process.env.PUBLIC_URL + "/ExportPdf");
  };
  const isMenuShown = () => {
    let xlIcon =
      columns.length &&
      activeLayer.layerData.layerMetadata?.dependencies?.find(
        (d) => d.name === externalBtnsForTblData.exportXlAttrTbl
      );
    let kmlIcon = activeLayer.layerData.layerMetadata?.dependencies?.find(
      (d) => d.name === externalBtnsForTblData.exportKmlAttrTbl
    );
    let googleIcon = activeLayer.layerData.layerMetadata?.dependencies?.find(
      (d) => d.name === externalBtnsForTblData.googleMapsAttrTbl
    );
    let cadIcon = activeLayer.layerData.layerMetadata?.dependencies?.find(
      (d) => d.name === externalBtnsForTblData.exportCadAttrTbl
    );
    let shpIcon = activeLayer.layerData.layerMetadata?.dependencies?.find(
      (d) => d.name === externalBtnsForTblData.exportShpAttrTbl
    );
    let pdfIcon = activeLayer.layerData.layerMetadata?.dependencies?.find(
      (d) => d.name === externalBtnsForTblData.exportPdfAttrTbl
    );
    return xlIcon || kmlIcon || googleIcon || cadIcon || pdfIcon;
  };

  // async function exportCSVFile(evt) {
  //   if (
  //     evt.target !== evt.currentTarget &&
  //     evt.currentTarget.querySelector("#main-elem-for-export")
  //   )
  //     return;
  //     let neededWhereClause = isDepend
  //     ? filterWhereClause.dep.filtered
  //       ? filterWhereClause.dep.filtered
  //       : filterWhereClause.dep.default
  //     : filterWhereClause.current;
  //   if (isDepend)
  //     neededWhereClause =
  //       isDepend === "1=1"
  //         ? colFilterWhere.dep.join(" AND ")
  //         : isDepend + " AND " + colFilterWhere.dep.join(" AND ");
  //   else
  //     neededWhereClause =
  //       neededWhereClause === "1=1"
  //         ? colFilterWhere.current.join(" AND ")
  //         : neededWhereClause + " AND " + colFilterWhere.current.join(" AND ");
  //   neededWhereClause = neededWhereClause || "1=1";
  //   let mapInfo = await getFromEsriRequest(window.dashboardMapUrl + "?f=pjson");

  //   let layerID = getLayerId(
  //     {
  //       info: {
  //         $layers: mapInfo,
  //       },
  //     },
  //     activeLayer.layerData?.layerName
  //   );

  //   let requiredOutFields =
  //     activeLayer.layerData.layerMetadata.outFields.filter(
  //       (outField) =>
  //         !mediaFieldsNames(activeLayer.layerData).includes(outField)
  //     );
  //   console.log("needed where clause", neededWhereClause);
  //   let queryParams = {
  //     url: window.dashboardMapUrl + "/" + layerID,
  //     notShowLoading: false,
  //     returnGeometry: false,
  //     where: neededWhereClause,
  //     outFields: requiredOutFields,
  //   };
  //   queryTask({
  //     ...queryParams,
  //     callbackResult: ({ features }) => {
  //       console.log("query features", features);
  //       if (features.length)
  //         getFeatureDomainName(
  //           features,
  //           layerID,
  //           false,
  //           window.dashboardMapUrl
  //         ).then((feats) => {
  //           let reqData = feats.map((f) => {
  //             return { ...f.attributes };
  //           });
  //           let requiredLabels = activeLayer.layerData.layerMetadata.fields
  //             .filter((field) => requiredOutFields.includes(field.fieldName))
  //             .map((field) => field.alias);
  //           setExportedData({
  //             dataSet: reqData,
  //             columns: requiredOutFields,
  //             labels: requiredLabels,
  //             layerName: activeLayer.layerData?.layerName,
  //             whereClause: neededWhereClause,
  //           });

  //           // setExportCSVActive(true);
  //         });
  //     },
  //   });

  // }

  // const exportCSVFile = () => {
  //   console.log("exported data when hover", exportedData);
  // };
  // const exportMenu = async () => {
  //   let isLayer = map.__mapInfo.info.$layers.layers.find(
  //     (lay) => lay.name === activeLayer.layerData.layerName
  //   );
  //   let neededWhereClause = isDepend
  //     ? filterWhereClause.dep.filtered
  //       ? filterWhereClause.dep.filtered
  //       : filterWhereClause.dep.default
  //     : filterWhereClause.current;
  //   if (isDepend)
  //     neededWhereClause =
  //       isDepend === "1=1"
  //         ? colFilterWhere.dep.join(" AND ")
  //         : isDepend + " AND " + colFilterWhere.dep.join(" AND ");
  //   else
  //     neededWhereClause =
  //       neededWhereClause === "1=1"
  //         ? colFilterWhere.current.join(" AND ")
  //         : neededWhereClause + " AND " + colFilterWhere.current.join(" AND ");
  //   neededWhereClause = neededWhereClause || "1=1";
  //   let mapInfo = await getFromEsriRequest(window.dashboardMapUrl + "?f=pjson");

  //   let layerID = getLayerId(
  //     {
  //       info: {
  //         $layers: mapInfo,
  //       },
  //     },
  //     activeLayer.layerData?.layerName
  //   );

  //   let requiredOutFields =
  //     activeLayer.layerData.layerMetadata.outFields.filter(
  //       (outField) =>
  //         !mediaFieldsNames(activeLayer.layerData).includes(outField)
  //     );
  //   console.log("needed where clause", neededWhereClause);
  //   let queryParams = {
  //     url: window.dashboardMapUrl + "/" + layerID,
  //     notShowLoading: false,
  //     returnGeometry: false,
  //     where: neededWhereClause,
  //     outFields: requiredOutFields,
  //   };
  //   queryTask({
  //     ...queryParams,
  //     callbackResult: ({ features }) => {
  //       console.log("query features", features);
  //       if (features.length)
  //         getFeatureDomainName(
  //           features,
  //           layerID,
  //           false,
  //           window.dashboardMapUrl
  //         ).then((feats) => {
  //           let reqData = feats.map((f) => {
  //             return { ...f.attributes };
  //           });
  //           let requiredLabels = activeLayer.layerData.layerMetadata.fields
  //             .filter((field) => requiredOutFields.includes(field.fieldName))
  //             .map((field) => field.alias);
  //           setExportedData({
  //             dataSet: reqData,
  //             columns: requiredOutFields,
  //             labels: requiredLabels,
  //             layerName: activeLayer.layerData?.layerName,
  //             whereClause: neededWhereClause,
  //           });
  //         });
  //     },
  //   });

  //   return (
  //     <Menu className="exportMenu">
  //       {columns.length &&
  //         activeLayer.layerData.layerMetadata?.dependencies?.find(
  //           (d) => d.name === externalBtnsForTblData.exportXlAttrTbl
  //         ) && (
  //           <Menu.Item disabled={!isLayer}>
  //             <div
  //               onClick={() => {
  //                 // setExportCSVActive(false);
  //                 exportCSVFile();
  //               }}
  //             >
  //               {exportedData.dataSet.length && exportedData.whereClause ? (
  //                 <ExportCSV
  //                   isForceClick={false}
  //                   {...exportedData}
  //                   setExportedData={setExportedData}
  //                   // isActive={exportCSVActive}
  //                 />
  //               ) : (
  //                 t("common:extractExcelFile")
  //               )}
  //             </div>
  //           </Menu.Item>
  //         )}
  //       {activeLayer.layerData.layerMetadata?.dependencies?.find(
  //         (d) => d.name === externalBtnsForTblData.exportKmlAttrTbl
  //       ) && (
  //         <Menu.Item disabled={!isLayer}>
  //           <span onClick={() => exportFile("KML")}>
  //             {t("print:ExtractFile")} KML
  //           </span>
  //         </Menu.Item>
  //       )}
  //       {activeLayer.layerData.layerMetadata?.dependencies?.find(
  //         (d) => d.name === externalBtnsForTblData.googleMapsAttrTbl
  //       ) && (
  //         <Menu.Item disabled={!isLayer}>
  //           <span>{t("print:googleMapExtr")}</span>
  //         </Menu.Item>
  //       )}
  //       {activeLayer.layerData.layerMetadata?.dependencies?.find(
  //         (d) => d.name === externalBtnsForTblData.exportCadAttrTbl
  //       ) && (
  //         <Menu.Item disabled={!isLayer}>
  //           <span onClick={() => exportFile("CAD")}>
  //             {t("print:ExtractFile")} CAD
  //           </span>
  //         </Menu.Item>
  //       )}
  //       {activeLayer.layerData.layerMetadata?.dependencies?.find(
  //         (d) => d.name === externalBtnsForTblData.exportShpAttrTbl
  //       ) && (
  //         <Menu.Item disabled={!isLayer}>
  //           <span onClick={() => exportFile("Shape")}>
  //             {t("print:ExtractFile")} Shape
  //           </span>
  //         </Menu.Item>
  //       )}
  //       {activeLayer.layerData.layerMetadata?.dependencies?.find(
  //         (d) => d.name === externalBtnsForTblData.exportPdfAttrTbl
  //       ) && (
  //         <Menu.Item disabled={!isLayer}>
  //           <span onClick={exportPDF}>{t("print:ExtractFile")} PDF</span>
  //         </Menu.Item>
  //       )}
  //     </Menu>
  //   );
  // };

  const fetchExportData = useCallback(async () => {
    if (!activeLayer.layerData) return;

    let neededWhereClause = isDepend
      ? filterWhereClause.dep.filtered
        ? filterWhereClause.dep.filtered
        : filterWhereClause.dep.default
      : filterWhereClause.current;
    if (isDepend)
      neededWhereClause =
        isDepend === "1=1"
          ? colFilterWhere.dep.join(" AND ")
          : isDepend + " AND " + colFilterWhere.dep.join(" AND ");
    else
      neededWhereClause =
        neededWhereClause === "1=1"
          ? colFilterWhere.current.join(" AND ")
          : // : neededWhereClause + " AND " + colFilterWhere.current.join(" AND ");
            neededWhereClause;
    neededWhereClause = neededWhereClause || "1=1";
    let mapInfo = await getFromEsriRequest(window.dashboardMapUrl + "?f=pjson");

    let layerID = getLayerId(
      { info: { $layers: mapInfo } },
      activeLayer.layerData.layerName
    );

    let requiredOutFields = activeLayer.layerData.layerMetadata.outFields
      .filter(
        (outField) =>
          !mediaFieldsNames(activeLayer.layerData).includes(outField)
      )
      .sort();

    let queryParams = {
      url: window.dashboardMapUrl + "/" + layerID,
      notShowLoading: false,
      returnGeometry: false,
      where: neededWhereClause,
      outFields: requiredOutFields,
    };

    queryTask({
      ...queryParams,
      callbackResult: ({ features }) => {
        if (features.length) {
          getFeatureDomainName(
            features,
            layerID,
            false,
            window.dashboardMapUrl
          ).then((feats) => {
            console.log("active layer in CSV", activeLayer);
            let reqData = feats.map((f) => ({ ...f.attributes }));
            let requiredFields = [];
            requiredFields = activeLayer.layerData.layerMetadata.fields.filter(
              (field) => requiredOutFields.includes(field.fieldName)
            );
            // requiredFields.sort((a, b) => {
            //   if (a.fieldName < b.fieldName) {
            //     return -1;
            //   }
            //   if (a.fieldName > b.fieldName) {
            //     return 1;
            //   }
            //   return 0;
            // });
            let requiredLabels = requiredFields.map((field) => field.alias);
            let requiredColumns = requiredFields.map(
              (field) => field.fieldName
            );
            console.log("Data set", reqData);
            console.log("columns", requiredColumns);
            console.log("labels", requiredLabels);

            setExportedData({
              dataSet: reqData,
              columns: requiredColumns,
              labels: requiredLabels,
              layerName: activeLayer.layerData.layerName,
              whereClause: neededWhereClause,
            });
          });
        }
      },
    });
  }, [activeLayer, isDepend, filterWhereClause, colFilterWhere]);

  useEffect(() => {
    fetchExportData();
  }, [fetchExportData]);

  const exportCSVFile = () => {
    const { dataSet, columns, labels, layerName } = exportedData;

    // 1. Convert dataSet rows from "columns" to "labels"
    //    This will produce a new array of objects with the 'labels' as keys
    const mappedData = dataSet.map((row) => {
      const newRow = {};
      columns.forEach((col, index) => {
        // The property in newRow will be the matching label,
        // and its value will come from row[col].
        newRow[labels[index]] = row[col];
        if (col == "CREATED_DATE") {
          newRow[labels[index]] = convertTimeStampToDate(newRow[labels[index]]);
        }
      });
      return newRow;
    });

    // 2. Create a worksheet from the new data
    const worksheet = XLSX.utils.json_to_sheet(mappedData);

    // 3. Create a new workbook and append the worksheet
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

    // 4. Write the file (choose any filename you like)
    XLSX.writeFile(workbook, layerName + ".xlsx");
  };

  const exportMenu = () => {
    return (
      <Menu className="exportMenu">
        {exportedData.columns.length &&
          activeLayer.layerData.layerMetadata?.dependencies?.find(
            (d) => d.name === externalBtnsForTblData.exportXlAttrTbl
          ) && (
            <Menu.Item disabled={!activeLayer}>
              <div onClick={exportCSVFile}>
                {
                  /*exportedData.dataSet.length && exportedData.whereClause ? (
                  <ExportCSV
                    isForceClick={false}
                    {...exportedData}
                    setExportedData={setExportedData}
                  />
                ) : (
                  t("common:extractExcelFile")
                )*/
                  t("common:extractExcelFile")
                }
              </div>
            </Menu.Item>
          )}
        {activeLayer.layerData.layerMetadata?.dependencies?.find(
          (d) => d.name === externalBtnsForTblData.exportKmlAttrTbl
        ) && (
          <Menu.Item disabled={!activeLayer}>
            <span onClick={() => exportFile("KML")}>
              {t("print:ExtractFile")} KML
            </span>
          </Menu.Item>
        )}
        {activeLayer.layerData.layerMetadata?.dependencies?.find(
          (d) => d.name === externalBtnsForTblData.googleMapsAttrTbl
        ) && (
          <Menu.Item disabled={!activeLayer}>
            <span>{t("print:googleMapExtr")}</span>
          </Menu.Item>
        )}
        {activeLayer.layerData.layerMetadata?.dependencies?.find(
          (d) => d.name === externalBtnsForTblData.exportCadAttrTbl
        ) && (
          <Menu.Item disabled={!activeLayer}>
            <span onClick={() => exportFile("CAD")}>
              {t("print:ExtractFile")} CAD
            </span>
          </Menu.Item>
        )}
        {activeLayer.layerData.layerMetadata?.dependencies?.find(
          (d) => d.name === externalBtnsForTblData.exportShpAttrTbl
        ) && (
          <Menu.Item disabled={!activeLayer}>
            <span onClick={() => exportFile("Shape")}>
              {t("print:ExtractFile")} Shape
            </span>
          </Menu.Item>
        )}
        {activeLayer.layerData.layerMetadata?.dependencies?.find(
          (d) => d.name === externalBtnsForTblData.exportPdfAttrTbl
        ) && (
          <Menu.Item disabled={!activeLayer}>
            <span onClick={exportPDF}>{t("print:ExtractFile")} PDF</span>
          </Menu.Item>
        )}
      </Menu>
    );
  };
  return isMenuShown() ? (
    <Dropdown overlay={exportMenu()}>
      <a onClick={(e) => e.preventDefault()}>
        <>
          <Button className="tableHeaderBtn">
            <Tooltip placement="top" title={t("print:export")}>
              <span>
                <FontAwesomeIcon icon={faFileExport} />
              </span>
            </Tooltip>
          </Button>
        </>
      </a>
    </Dropdown>
  ) : null;
}

export default ExportFilesComp;
