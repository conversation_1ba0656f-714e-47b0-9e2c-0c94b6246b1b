import React, { useEffect, useState } from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import Draggable from "react-draggable";
import { useTranslation } from "react-i18next";

const GoogleMaps = (props) => {
  const { i18n } = useTranslation();

  const [loc, setLoc] = useState(
    props.map.view.extent.center.latitude +
      "," +
      props.map.view.extent.center.longitude
  );
  const [zoom, setZoom] = useState(props.map.view.zoom);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const handleDrag = (e, position) => {
    setPosition({ x: position.x, y: position.y });
  };

  useEffect(() => {
    watchUtils.whenTrue(props.map.view, "ready", () => {
      watchUtils.whenOnce(props.map.view, "extent", () => {
        watchUtils.when(props.map.view, "stationary", (evt) => {
          if (evt) {
            let point = props.map.view.extent.center;
            setLoc(point.latitude + "," + point.longitude);
            setZoom(props.map.view.zoom);
          }
        });
      });
    });
  }, [props.map.view]);

  return (
    <Draggable
      position={position}
      onDrag={handleDrag}
      bounds={{
        left:
          i18n.language === "ar"
            ? -(window.innerWidth - 400)
            : -window.innerWidth,
        top: -300,
        right: i18n.language === "ar" ? window.innerWidth - 400 : 0, // Subtract component width
        bottom: window.innerHeight - 350, // Subtract component height
      }}
    >
      <div
        className="toolsMenu inquiryTool layersMenu leftToolMenu"
        style={{
          overflow: "auto",
          height: "fit-content",
          maxHeight: "500px",
          position: "relative",
          top: "95px",
          minWidth: "400px",
          border: "4px solid #fff",
        }}
      >
        <Fade left>
          <span
            style={{
              width: "100%",
              float: "left",
              textAlign: "left",
              marginLeft: "5px",
              marginTop: "-5px",
            }}
          >
            <FontAwesomeIcon
              icon={faTimes}
              style={{
                marginTop: "5px",
                marginRight: "5px",
                cursor: "pointer",
              }}
              onClick={(e) => {
                e.stopPropagation();
                props.closeToolsData();
              }}
            />
          </span>
          <iframe
            src={
              "https://www.google.com/maps/embed/v1/place?q=" +
              loc +
              "&zoom=" +
              zoom +
              "&key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8"
            }
            style={{ width: "100%", height: "93%", border: "0" }}
            title="mapExplorer"
            frameborder="0"
            allowfullscreen
          ></iframe>
        </Fade>
      </div>
    </Draggable>
  );
};

export default GoogleMaps;

/*** */

// import React, { Component, useEffect, useState } from "react";
// import Fade from "react-reveal/Fade";
// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import { faTimes } from "@fortawesome/free-solid-svg-icons";
// import * as watchUtils from "@arcgis/core/core/watchUtils";
// import { Resizable } from "re-resizable";
// import Draggable from "react-draggable";

// class GoogleMaps extends Component {

//   state = {
//     loc:
//       this.props.map.view.extent.center.latitude +
//       "," +
//       this.props.map.view.extent.center.longitude,
//     zoom: this.props.map.view.zoom,
//     x: 0,
//     y: 0,
//   };

//   handleDrag = (e, position) => {
//     this.setState({ x: position.x, y: position.y });
//   };

//   componentDidMount() {
//     watchUtils.whenTrue(this.props.map.view, "ready", () => {
//       watchUtils.whenOnce(this.props.map.view, "extent", () => {
//         watchUtils.when(this.props.map.view, "stationary", (evt) => {
//           if (evt) {
//             let point = this.props.map.view.extent.center;
//             //latitude:
//             //longitude:
//             this.setState({
//               loc: point.latitude + "," + point.longitude,
//               zoom: this.props.map.view.zoom,
//             });
//           }
//         });
//       });
//     });
//   }
//   render() {
//     return (
//       // <Fade left collapse>
//       <Draggable
//         position={{ x: this.state.x, y: this.state.y }}
//         onDrag={this.handleDrag}
//         bounds={
//           i18n.language === "ar"
//             ? {
//                 left: 0,
//                 top: -150,
//                 right: window.innerWidth - 400, // Subtract component width
//                 bottom: 50, // Subtract component height
//               }
//             : {
//                 right: 0,
//                 top: -150,
//                 left: window.innerWidth - 400, // Subtract component width
//                 bottom: 50, // Subtract component height
//               }
//         }
//       >
//         <div
//           className="toolsMenu inquiryTool layersMenu leftToolMenu"
//           style={{
//             overflow: "auto",
//             height: "fit-content",
//             maxHeight: "500px",
//             position: "relative",
//             top: "95px",
//             minWidth: "400px",
//           }}
//           // className="leftToolMenu"
//           // defaultSize={{
//           //   width: 400,
//           //   height: "auto",
//           // }}
//           // // minHeight={300}
//           // maxWidth={800}
//           // maxHeight={600}
//           // bounds="window"
//         >
//           <Fade left>
//             <span
//               style={{
//                 width: "100%",
//                 float: "left",
//                 textAlign: "left",
//                 marginLeft: "5px",
//                 marginTop: "-5px",
//               }}
//             >
//               <FontAwesomeIcon
//                 icon={faTimes}
//                 style={{
//                   marginTop: "5px",
//                   marginRight: "5px",
//                   cursor: "pointer",
//                 }}
//                 onClick={(e) => {
//                   e.stopPropagation();
//                   this.props.closeToolsData();
//                 }}
//               />
//             </span>
//             <iframe
//               src={
//                 "https://www.google.com/maps/embed/v1/place?q=" +
//                 this.state.loc +
//                 "&zoom=" +
//                 this.state.zoom +
//                 "&key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8"
//               }
//               style={{ width: "100%", height: "93%", border: "0" }}
//               title="mapExplorer"
//               frameborder="0"
//               allowfullscreen
//             ></iframe>
//           </Fade>
//         </div>
//       </Draggable>
//       // </Fade>
//     );
//   }
// }
// export default GoogleMaps;
