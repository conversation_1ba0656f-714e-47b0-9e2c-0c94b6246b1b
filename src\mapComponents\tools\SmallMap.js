import React from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import MapOverviewWidget from "../MapOverviewWidget";
import { Resizable } from "re-resizable";
import Draggable from "react-draggable";
import { useTranslation } from "react-i18next";

export default function SmallMap(props) {
  const { i18n } = useTranslation();

  const [x, setX] = React.useState(0);
  const [y, setY] = React.useState(0);

  const handleDrag = (e, position) => {
    setX(position.x);
    setY(position.y);
  };

  return (
    // <Fade left collapse>
    <Draggable
      position={{ x, y }}
      onDrag={handleDrag}
      bounds={{
        left:
          i18n.language === "ar"
            ? -(window.innerWidth - 400)
            : -window.innerWidth,
        top: -300,
        right: i18n.language === "ar" ? window.innerWidth - 400 : 0, // Subtract component width
        bottom: window.innerHeight - 500, // Subtract component height
      }}
      handle=".smallmap-drag-handle"
    >
      <div
        className="toolsMenu inquiryTool layersMenu leftToolMenu"
        style={{
          overflow: "auto",
          height: "300px",
          maxHeight: "500px",
          position: "relative",
          top: "140px",
          minWidth: "400px",
          border: "4px solid #fff",
        }}
        // className="leftToolMenu"
        // defaultSize={{
        //   width: 400,
        //   height: "300",
        // }}
        // // minHeight={300}
        // maxWidth={800}
        // maxHeight={600}
        // bounds="window"
      >
        {/* <Fade left> */}
        <span
          className="smallmap-drag-handle"
          style={{
            width: "100%",
            float: "left",
            textAlign: "left",
            marginLeft: "5px",
            marginTop: "-5px",
            cursor: "move",
          }}
        >
          {" "}
          <FontAwesomeIcon
            icon={faTimes}
            style={{
              marginTop: "5px",
              marginRight: "5px",
              cursor: "pointer",
            }}
            onClick={(e) => {
              e.stopPropagation();
              props.closeToolsData();
            }}
          />
        </span>
        <MapOverviewWidget mainMap={props.map} />
        {/* </Fade> */}
      </div>
    </Draggable>
    // </Fade>
  );
}
