import React, { Component } from "react";
import styled from "styled-components";
import moment from "moment";
import onClickOutside from "react-onclickoutside";
import DayNames from "./DayNames.js";
import MonthList from "./MonthsList.js";
import YearsList from "./YearsList.js";
import MonthDaysView from "./MonthDaysView.js";
import { Input, Popover } from "antd";
import { get } from "lodash";
import { withTranslation } from "react-i18next";
import { IoCloseSharp } from "react-icons/io5";

const Calendar = styled.div`
  width: 266px;
  direction: rtl;
  background: #ffffff;
  padding: 15px;
  border: 1px solid #ddd;
  margin-top: 2px;
  font-family: serif;
  box-sizing: unset;
  font-size: 14px;
  border-radius: 4px;
`;

const CalendarControls = styled.div`
  direction: rtl;
  text-align: center;
`;

const ControlButton = styled.button`
  position: absolute;
  border: 0px;
  font-weight: bold;
  font-size: 15px;
  cursor: pointer;
  background-color: #fff;
  :hover {
    color: #888888;
  }
  :focus {
    outline: unset;
  }
`;

const PreviousButton = styled(ControlButton)`
  right: 15px;
`;

const NextButton = styled(ControlButton)`
  left: 15px;
`;

const MonthName = styled.strong``;

const YearAndMonthList = styled.div`
  margin-top: 10px;
`;

class GregorianDatePicker extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentTime: get(props, "value")
        ? moment(get(props, "value", ""), props.dateFormat || "YYYY-MM-DD")
        : moment(),
      calendarShown: false,
    };
  }

  handleClickOutside = (evt) => {
    this.setState({
      calendarShown: false,
    });
  };

  subtractMonth = () => {
    this.setState((prevState) => ({
      currentTime: prevState.currentTime.subtract(1, "month"),
    }));
  };

  addMonth = () => {
    this.setState((prevState) => ({
      currentTime: prevState.currentTime.add(1, "month"),
    }));
  };

  setSelectedDate = (event) => {
    const { dateFormat = "YYYY-MM-DD", input: { onChange = () => {} } = {} } =
      this.props;
    let time = this.state.currentTime;
    time.date(parseInt(event.target.value, 10));
    onChange(time.format(dateFormat));
    this.setState({
      calendarShown: false,
    });
  };

  showCalendar = () => {
    this.setState({
      calendarShown: true,
    });
  };

  handleMonthChange = (event) => {
    let time = this.state.currentTime;
    time.month(parseInt(event.target.value, 10));
    this.setState({
      currentTime: time,
    });
  };

  handleYearChange = (event) => {
    let time = this.state.currentTime;
    time.year(parseInt(event.target.value, 10));
    this.setState({
      currentTime: time,
    });
  };

  render() {
    const {
      className,
      input,
      t,
      dateFormat = "YYYY-MM-DD",
      placeholder,
      type,
    } = this.props;
    return (
      <div onClickOutside={this.handleClickOutside.bind(this)}>
        <Popover
          getPopupContainer={(trigger) => trigger.parentNode}
          visible={this.state.calendarShown}
          placement="bottom"
          content={
            <div>
              <div style={{ display: "flex", justifyContent: "flex-end", marginBottom: "10px" }}>
                <IoCloseSharp
                  onClick={() => this.setState({ calendarShown: false })}
                  size={20}
                  style={{ cursor: "pointer" }}
                />
              </div>

              <Calendar>
                <CalendarControls>
                  {(!type || type === "monthly") && (
                    <>
                      <PreviousButton
                        onClick={this.subtractMonth}
                        type="button"
                      >
                        {"<"}
                      </PreviousButton>
                      <MonthName>
                        {t(this.state.currentTime.format("MMMM")) +
                          " (" +
                          this.state.currentTime.format("MM") +
                          ") " +
                          this.state.currentTime.format("YYYY")}
                      </MonthName>
                    </>
                  )}
                  {(!type || type === "yearly") && (
                    <>
                      <NextButton onClick={this.addMonth} type="button">
                        {" "}
                        {">"}{" "}
                      </NextButton>
                      <YearAndMonthList>
                        <YearsList
                          currentTime={this.state.currentTime}
                          onChange={this.handleYearChange}
                        />
                        <MonthList
                          currentTime={this.state.currentTime}
                          onChange={this.handleMonthChange}
                        />
                      </YearAndMonthList>
                    </>
                  )}
                </CalendarControls>
                <DayNames />
                <MonthDaysView
                  currentTime={this.state.currentTime}
                  dateFormat={dateFormat}
                  selectedDate={get(input, "value")}
                  setSelectedDate={this.setSelectedDate}
                />
              </Calendar>
            </div>
          }
        >
          <Input
            type="text"
            {...{ className }}
            {...input}
            {...{ placeholder }}
            onFocus={this.showCalendar}
            readOnly
          />
        </Popover>
      </div>
    );
  }
}

export default onClickOutside(withTranslation("labels")(GregorianDatePicker));
