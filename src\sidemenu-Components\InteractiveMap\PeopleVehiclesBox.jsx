import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { MdKeyboardArrowDown } from "react-icons/md";

import people_logo from "../../assets/images/interactive-map/people_logo.svg";

import people_1 from "../../assets/images/interactive-map/people_1.svg";
import people_2 from "../../assets/images/interactive-map/people_2.svg";
import people_3 from "../../assets/images/interactive-map/people_3.svg";
import people_4 from "../../assets/images/interactive-map/people_4.svg";
import people_5 from "../../assets/images/interactive-map/people_5.svg";
import people_6 from "../../assets/images/interactive-map/people_6.svg";

export default function PeopleVehiclesBox(props) {
  const { t } = useTranslation("sidemenu");

  const people_list = [
    { name: "explorer", src: people_1, label: t("explorer", { ns: "common" }) },
    {
      name: "engineer",
      src: people_2,
      // label: t("deer", { ns: "common" })
    },
    {
      name: "passenger",
      src: people_3,
      label: t("passenger", { ns: "common" }),
    },
    { name: "drone", src: people_4, label: t("drone", { ns: "common" }) },
    { name: "car", src: people_5, label: t("car", { ns: "common" }) },
    { name: "boat", src: people_6, label: t("boat", { ns: "common" }) },
  ];

  const [showItem, setShowItem] = useState(true);

  const handleSelectItem = (item) => {
    if (
      props.generalSelectedItem &&
      props.generalSelectedItem.index === item.index &&
      props.generalSelectedBox === "vehicles"
    ) {
      props.setGeneralSelectedItem(null);
      props.setGeneralSelectedBox("");
      props.updateGraphicState(undefined);

      return;
    }

    props.setGeneralSelectedItem(item);
    props.setGeneralSelectedBox("vehicles");
    props.updateGraphicState({
      graphicsLayerName: "PeopleVehicles_InteractiveGraphicLayer",
      symbolName: item.name,
    });
  };

  const handleRemoveAll = () => {
    props.setGeneralSelectedItem(null);
    props.setGeneralSelectedBox("");
    let graphicLayer = props.map.layers.items.find(
      (layer) => layer.id == "InteractiveMapGraphicLayer"
    );

    let graphicsToRemove = graphicLayer.graphics.items.filter(
      (graphic) =>
        graphic._layerName == "PeopleVehicles_InteractiveGraphicLayer"
    );

    if (graphicsToRemove.length > 0) {
      props.history.current.undoStack.push(graphicsToRemove);
      graphicLayer.graphics.removeMany(graphicsToRemove);
    }
    props.updateGraphicState(undefined);
  };

  return (
    <div className="box">
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          color: "#fff",
          cursor: "pointer",
        }}
        onClick={() => setShowItem(!showItem)}
      >
        <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
          <img src={people_logo} alt="people logo" />
          <span>{t("people_vehicles")}</span>
        </div>

        <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
          <Button
            onClick={(e) => {
              e.stopPropagation();
              handleRemoveAll();
            }}
          >
            {t("clearAll")}
          </Button>
          <MdKeyboardArrowDown
            size={20}
            style={{
              // cursor: "pointer",
              transform: `rotate(${!showItem ? "180deg" : 0})`,
            }}
            // onClick={() => setShowItem(!showItem)}
          />
        </div>
      </div>

      {showItem && (
        <div
          className="images"
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(6, 1fr)",
            gap: "10px",
            marginTop: "10px",
          }}
        >
          {people_list.map((item, indx) => (
            <Tooltip title={item.label} placement="bottom">
              <div
                key={indx}
                className="image"
                name={item.name}
                style={{
                  background: props.generalSelectedItem
                    ? props.generalSelectedItem.index === indx &&
                      props.generalSelectedBox === "vehicles"
                      ? "#B55433"
                      : "transparent"
                    : "transparent",
                }}
                onClick={() =>
                  handleSelectItem({
                    name: item.name,
                    src: item.src,
                    index: indx,
                  })
                }
              >
                <img
                  src={item.src}
                  alt="tool"
                  style={{
                    filter: props.generalSelectedItem
                      ? props.generalSelectedItem.index === indx &&
                        props.generalSelectedBox === "vehicles"
                        ? "brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(2%) hue-rotate(264deg) brightness(105%) contrast(101%)"
                        : ""
                      : "",
                  }}
                />
              </div>
            </Tooltip>
          ))}
        </div>
      )}
    </div>
  );
}
