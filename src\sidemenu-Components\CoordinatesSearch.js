import React, { useState } from "react";
import { Row, Col, Input, Form, Select } from "antd";
import { Tabs, Tab } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import { FaCheck } from "react-icons/fa6";
import { RiArrowDropDownFill } from "react-icons/ri";

import { SearchOutlined } from "@ant-design/icons";
import Point from "@arcgis/core/geometry/Point";
import SpatialReference from "@arcgis/core/geometry/SpatialReference";
import locationIcon from "../assets/images/location.png";
import {
  addPictureSymbol,
  getLayerId,
  highlightFeature,
  project,
  queryTask,
} from "../helper/common_func";
import usePersistentState from "../helper/usePersistentState";
const componentName = "CoordinatesSearch";
export default function CoordinatesSearch(props) {
  const { t } = useTranslation("common");
  const [formValues, setFormValues] = usePersistentState("formValues", {
    latitude: "",
    longitude: "",
    //
    latitudeDeg: "",
    latitudeMinutes: "",
    latitudeSeconds: "",
    longitudeDeg: "",
    longitudeMinutes: "",
    longitudeSeconds: "",

    //
    longitudeDec: "",
    latitudeDec: "",
  }, componentName);
  const [selectedTab, setSelectedKey] = usePersistentState("selectedTab", undefined, componentName);

  const [errorMessage, setErrorMessage] = usePersistentState("errorMessage", null, componentName);
  const [WGS_Geographic, setWGS_Geographic] = usePersistentState("WGS_Geographic", null, componentName);
  const [WGS_Projected, setWGS_Projected] = usePersistentState("WGS_Projected", null, componentName);
  const [KSA_GRF17_Projected, setKSA_GRF17_Projected] = usePersistentState("KSA_GRF17_Projected", null, componentName);
  const [KSA_GRF17_Geographic, setKSA_GRF17_Geographic] = usePersistentState("KSA_GRF17_Geographic", null, componentName);

  const handleChange = (e) => {
    setFormValues({ ...formValues, [e.target.name]: e.target.value });
  };

  const CoordinateSearch = (e) => { };
  const degSearch = (e) => { };

  const [dropSystem, setdropSystem] = usePersistentState("dropSystem", undefined, componentName);
  const [metersType, setMetersType] = usePersistentState("metersType", 1, componentName);
  const [dropType, setdropType] = usePersistentState("dropType", undefined, componentName);
  // const [WGS84_ZoneID, setWGS84_ZoneID] = useState(undefined);
  const [KSA_GRF17_ZoneID, setKSA_GRF17_ZoneID] = usePersistentState("KSA_GRF17_ZoneID", undefined, componentName);
  const [KsaPointZone, setKsaPointZone] = usePersistentState("KsaPointZone", undefined, componentName);
  const [WgsPointZone, setWgsPointZone] = usePersistentState("WgsPointZone", undefined, componentName);

  const onFinish = (values) => {
    values = formValues;
    setWGS_Projected(null);
    setWGS_Geographic(null);
    setKSA_GRF17_Geographic(null);
    setKSA_GRF17_Projected(null);
    //setWGS84_ZoneID(null)
    //setKSA_GRF17_ZoneID(null)
    setKsaPointZone(null);
    setWgsPointZone(null);
    const KSAGRF17_UTM_Zones = {
      9356: {
        longitude: {
          east_longitude: 36.01,
          west_longitude: 34.44,
        },
        latitude: {
          south_latitude: 24.92,
          north_latitude: 29.38,
        },
      },
      9357: {
        longitude: {
          east_longitude: 42,
          west_longitude: 36,
        },
        latitude: {
          south_latitude: 16.29,
          north_latitude: 32.16,
        },
      },
      9358: {
        longitude: {
          east_longitude: 48,
          west_longitude: 41.99,
        },
        latitude: {
          south_latitude: 16.35,
          north_latitude: 31.15,
        },
      },
      9359: {
        longitude: {
          east_longitude: 54.01,
          west_longitude: 47.99,
        },
        latitude: {
          south_latitude: 17.94,
          north_latitude: 28.94,
        },
      },
      9360: {
        longitude: {
          east_longitude: 55.67,
          west_longitude: 54.02,
        },
        latitude: {
          south_latitude: 19.66,
          north_latitude: 22.77,
        },
      },
    };

    const WGS84_UTM_Zones = {
      32636: {
        longitude: {
          east_longitude: 36.01,
          west_longitude: 34.44,
        },
        latitude: {
          south_latitude: 24.92,
          north_latitude: 29.38,
        },
      },
      32637: {
        longitude: {
          east_longitude: 42,
          west_longitude: 36,
        },
        latitude: {
          south_latitude: 16.29,
          north_latitude: 32.16,
        },
      },
      32638: {
        longitude: {
          east_longitude: 48,
          west_longitude: 41.99,
        },
        latitude: {
          south_latitude: 16.35,
          north_latitude: 31.15,
        },
      },
      32639: {
        longitude: {
          east_longitude: 54.01,
          west_longitude: 47.99,
        },
        latitude: {
          south_latitude: 17.94,
          north_latitude: 28.94,
        },
      },
      32640: {
        longitude: {
          east_longitude: 55.67,
          west_longitude: 54.02,
        },
        latitude: {
          south_latitude: 19.66,
          north_latitude: 22.77,
        },
      },
    };

    let convertedZone = 4326;
    const getZone = () => {
      if (KSA_GRF17_ZoneID) {
        if (dropSystem == 3 && dropType == 2 && KSA_GRF17_ZoneID == 9356) {
          convertedZone = 32636;
          return KSA_GRF17_ZoneID;
        }
        if (dropSystem == 2 && dropType == 2 && KSA_GRF17_ZoneID == 9356) {
          convertedZone = 32636;
          return convertedZone;
        }

        if (dropSystem == 3 && dropType == 2 && KSA_GRF17_ZoneID == 9357) {
          convertedZone = 32637;
          return KSA_GRF17_ZoneID;
        }
        if (dropSystem == 2 && dropType == 2 && KSA_GRF17_ZoneID == 9357) {
          convertedZone = 32637;
          return convertedZone;
        }

        if (dropSystem == 3 && dropType == 2 && KSA_GRF17_ZoneID == 9358) {
          convertedZone = 32638;
          return KSA_GRF17_ZoneID;
        }
        if (dropSystem == 2 && dropType == 2 && KSA_GRF17_ZoneID == 9358) {
          convertedZone = 32638;
          return convertedZone;
        }

        if (dropSystem == 3 && dropType == 2 && KSA_GRF17_ZoneID == 9359) {
          convertedZone = 32639;
          return KSA_GRF17_ZoneID;
        }
        if (dropSystem == 2 && dropType == 2 && KSA_GRF17_ZoneID == 9359) {
          convertedZone = 32639;
          return convertedZone;
        }

        if (dropSystem == 3 && dropType == 2 && KSA_GRF17_ZoneID == 9360) {
          convertedZone = 32640;
          return KSA_GRF17_ZoneID;
        }
        if (dropSystem == 2 && dropType == 2 && KSA_GRF17_ZoneID == 9360) {
          convertedZone = 32640;
          return convertedZone;
        }
      }

      if (dropSystem == 3 && dropType == 1) {
        convertedZone = 4326;
        return 9333;
      }
      if (dropSystem == 2 && dropType == 1) {
        convertedZone = 9333;
        return 4326;
      }
    };

    let wkID = getZone();
    console.log(convertedZone, "spatial_ref", wkID);

    const between = (value, min, max) => value >= min && value <= max;

    const getPointZone = (zones, latitudeDec, longitudeDec) => {
      let latitudeToCheck = latitudeDec;
      let longitudeToCheck = longitudeDec;

      for (const key in zones) {
        if (zones.hasOwnProperty(key)) {
          const zone = zones[key];
          const { longitude, latitude } = zone;

          const zoneLatitudes = [
            latitude.south_latitude,
            latitude.north_latitude,
          ];
          const zoneLongitudes = [
            longitude.east_longitude,
            longitude.west_longitude,
          ];

          const isLatitudeInRange = between(
            latitudeToCheck,
            Math.min(...zoneLatitudes),
            Math.max(...zoneLatitudes)
          );
          const isLongitudeInRange = between(
            longitudeToCheck,
            Math.min(...zoneLongitudes),
            Math.max(...zoneLongitudes)
          );

          if (isLatitudeInRange && isLongitudeInRange) {
            return key; // Return the key of the matching zone
          }
        }
      }

      return 102100; // If no matching zone is found, return "out"
    };

    let point;
    if (values.x && values.y) {
      point = new Point({
        x: values.x,
        y: values.y,
        spatialReference: new SpatialReference({ wkid: wkID }),
      });

      if (dropSystem == 3 && dropType == 2) {
        setKSA_GRF17_Projected(null);
        //wgs 84 utm
        project([point], convertedZone, (res) => {
          setWGS_Projected({ x: res[0].x, y: res[0].y });
        });
      }

      if (dropSystem == 2 && dropType == 2) {
        setWGS_Projected(null);
        //KSA_GRF17
        project([point], KSA_GRF17_ZoneID, (res) => {
          setKSA_GRF17_Projected({ x: res[0].x, y: res[0].y });
        });
      }

      project([point], 4326, (res) => {
        setWGS_Geographic({
          latitude: fromLatLngToDegree(res[0].latitude),
          longitude: fromLatLngToDegree(res[0].longitude),
        });

        project([point], 9333, (res) => {
          setKSA_GRF17_Geographic({
            latitude: fromLatLngToDegree(res[0].latitude),
            longitude: fromLatLngToDegree(res[0].longitude),
          });
        });

        addPictureSymbol(
          res[0],
          locationIcon,
          "locationGraphicLayer",
          props.map,
          25,
          34,
          34 / 2 - 2
        );

        highlightFeature(res[0], props.map, {
          layerName: "ZoomGraphicLayer",
          isZoom: true,
          isZoomOnly: true,
          zoomDuration: 1000,
        });
      });
    } else if (values.latitudeDeg || values.latitudeDec) {
      if (values.latitudeDec) {
        point = zoomToLatLng(
          values.latitudeDec,
          values.longitudeDec,
          values.latitudeDec
        );

        let ksaGrf17Zone = getPointZone(
          KSAGRF17_UTM_Zones,
          values.latitudeDec,
          values.longitudeDec
        );
        setKsaPointZone(ksaGrf17Zone);
        let wgs84Zone = getPointZone(
          WGS84_UTM_Zones,
          values.latitudeDec,
          values.longitudeDec
        );

        project([point], wgs84Zone, (res) => {
          setWGS_Projected({
            x: res[0].x,
            y: res[0].y,
          });
        });

        if (dropSystem == 2) {
          setWGS_Geographic(null);
          //KSA GRF 17 geographic
          project([point], 9333, (res) => {
            setKSA_GRF17_Geographic({
              latitude: fromLatLngToDegree(res[0].latitude),
              longitude: fromLatLngToDegree(res[0].longitude),
            });
          });
        }
        if (dropSystem == 2 && dropType == 1 && ksaGrf17Zone !== 102100) {
          project([point], ksaGrf17Zone, (res) => {
            setKSA_GRF17_Projected({
              x: res[0].x,
              y: res[0].y,
            });
          });
        }
        if (dropSystem == 3) {
          setKSA_GRF17_Geographic(null);
          //wgs 84 geographic
          project([point], 4326, (res) => {
            setWGS_Geographic({
              latitude: fromLatLngToDegree(res[0].latitude),
              longitude: fromLatLngToDegree(res[0].longitude),
            });
          });
        }

        //KSA GRF 17
        if (dropSystem == 3 && dropType == 1 && ksaGrf17Zone !== 102100) {
          project([point], ksaGrf17Zone, (res) => {
            setKSA_GRF17_Projected({
              x: res[0].x,
              y: res[0].y,
            });
          });
        }
      }
      if (values.latitudeDeg) {
        point = zoomToDegreePoint(values);
        let ksaGrf17ZoneDeg = getPointZone(
          KSAGRF17_UTM_Zones,
          values.latitudeDeg,
          values.longitudeDeg
        );
        let wgs84ZoneDeg = getPointZone(
          WGS84_UTM_Zones,
          values.latitudeDeg,
          values.longitudeDeg
        );
        setKsaPointZone(ksaGrf17ZoneDeg);

        project([point], wgs84ZoneDeg, (res) => {
          setWGS_Projected({
            x: res[0].x,
            y: res[0].y,
          });
        });

        if (dropSystem == 2) {
          setWGS_Geographic(null);
          //KSA GRF 17 geographic
          project([point], 9333, (res) => {
            setKSA_GRF17_Geographic({
              latitude: fromLatLngToDegree(res[0].latitude),
              longitude: fromLatLngToDegree(res[0].longitude),
            });
          });
        }

        if (dropSystem == 2 && dropType == 1 && ksaGrf17ZoneDeg !== 102100) {
          project([point], ksaGrf17ZoneDeg, (res) => {
            setKSA_GRF17_Projected({
              x: res[0].x,
              y: res[0].y,
            });
          });
        }

        if (dropSystem == 3) {
          setKSA_GRF17_Geographic(null);
          //wgs 84 geographic
          project([point], 4326, (res) => {
            setWGS_Geographic({
              latitude: fromLatLngToDegree(res[0].latitude),
              longitude: fromLatLngToDegree(res[0].longitude),
            });
          });
        }

        if (dropSystem == 3 && dropType == 1 && ksaGrf17ZoneDeg !== 102100) {
          //KSA GRF 17
          project([point], ksaGrf17ZoneDeg, (res) => {
            setKSA_GRF17_Projected({
              x: res[0].x,
              y: res[0].y,
            });
          });
        }
      }
    } else {
      setErrorMessage("من فضلك قم بإدخال الحقول");
      return;
    }

    setErrorMessage(null);
    //checkPointInAmana(point);
  };

  const checkPointInAmana = (point) => {
    project([point], 102100, (res) => {
      let layerdId = getLayerId(props.map.__mapInfo, "Province_Boundary");

      queryTask({
        url: window.mapUrl + "/" + layerdId,
        geometry: res[0],
        outFields: ["OBJECTID"],
        returnGeometry: false,
        callbackResult: ({ features }) => {
          //if (!features.length) message.warning("هذة النقطة خارج حدود الأمانة");
        },
      });
    });
  };

  const fromDegreeToLatLng = (values) => {
    let latitudeResult =
      +values.latitudeDeg +
      +values.latitudeMinutes / 60 +
      +values.latitudeSeconds / 3600;
    let longitudeResult =
      +values.longitudeDeg +
      +values.longitudeMinutes / 60 +
      +values.longitudeSeconds / 3600;

    return { latitude: latitudeResult, longitude: longitudeResult };
  };

  const zoomToDegreePoint = (values) => {
    let latitudeResult =
      +values.latitudeDeg +
      +values.latitudeMinutes / 60 +
      +values.latitudeSeconds / 3600;
    let longitudeResult =
      +values.longitudeDeg +
      +values.longitudeMinutes / 60 +
      +values.longitudeSeconds / 3600;

    return zoomToLatLng(latitudeResult, longitudeResult);
  };

  const zoomToLatLng = (lat, lng) => {
    let point = new Point({
      latitude: lat,
      longitude: lng,
    });

    addPictureSymbol(point, locationIcon, "locationGraphicLayer", props.map,
          25,
          34,
          34 / 2 - 2);

    highlightFeature(point, props.map, {
      layerName: "ZoomGraphicLayer",
      isZoom: true,
      isZoomOnly: true,
      zoomDuration: 1000
    });

    return point;
  };

  const onPublicUserDecimalSubmit = (values) => {
    if (values.latitude && values.longitude) {
      let point = new Point({
        latitude: values.latitude,
        longitude: values.longitude,
      });

      addPictureSymbol(point, locationIcon, "locationGraphicLayer", props.map,
          25,
          34,
          34 / 2 - 2);

      highlightFeature(point, props.map, {
        layerName: "ZoomGraphicLayer",
        isZoom: true,
        isZoomOnly: true,
        zoomDuration: 1000,
      });

      checkPointInAmana(point);
    }
  };

  const fromLatLngToDegree = (angleInDegrees, returnObject) => {
    while (angleInDegrees < -180.0) angleInDegrees += 360.0;

    while (angleInDegrees > 180.0) angleInDegrees -= 360.0;

    var result = {};

    angleInDegrees = Math.abs(angleInDegrees);

    //gets the degree
    result.deg = Math.floor(angleInDegrees);
    var delta = angleInDegrees - result.deg;

    //gets minutes and seconds
    var seconds = 3600.0 * delta;
    result.sec = seconds % 60;
    result.min = Math.floor(seconds / 60.0);

    if (returnObject) {
      return result;
    }

    return (
      "''" +
      (+result.sec).toFixed(3) +
      " '" +
      result.min +
      " " +
      result.deg +
      "° "
    );
    //return result;
  };

  const onPublicUserDegreesSubmit = (values) => {
    // convert (deg, min, sec) to value of (lat,lng)
    let point = zoomToDegreePoint(values);
    //checkPointInAmana(point);
  };

  const onChangeCheckBox = (type) => {
    setMetersType(type);

    if (type == 1) {
      let latlng = fromDegreeToLatLng(formValues);
      if (latlng.latitude && latlng.longitude) {
        formValues.latitudeDec = latlng.latitude;
        formValues.longitudeDec = latlng.longitude;

        formValues.latitudeDeg = null;
        formValues.latitudeMinutes = null;
        formValues.latitudeSeconds = null;

        formValues.longitudeDeg = null;
        formValues.longitudeMinutes = null;
        formValues.longitudeSeconds = null;
      }
    } else {
      if (formValues.latitudeDec && formValues.longitudeDec) {
        let latdegree = fromLatLngToDegree(formValues.latitudeDec, true);
        let lngdegree = fromLatLngToDegree(formValues.longitudeDec, true);

        formValues.latitudeDeg = latdegree.deg;
        formValues.latitudeMinutes = latdegree.min;
        formValues.latitudeSeconds = latdegree.sec;

        formValues.longitudeDeg = lngdegree.deg;
        formValues.longitudeMinutes = lngdegree.min;
        formValues.longitudeSeconds = lngdegree.sec;

        formValues.latitudeDec = null;
        formValues.longitudeDec = null;
      }
    }
  };
  // const onChangeCheckBox = (e, type) => {
  //   setMetersType(type);

  //   if (type == 1) {
  //     let latlng = fromDegreeToLatLng(formValues);
  //     if (latlng.latitude && latlng.longitude) {
  //       formValues.latitudeDec = latlng.latitude;
  //       formValues.longitudeDec = latlng.longitude;

  //       formValues.latitudeDeg = null;
  //       formValues.latitudeMinutes = null;
  //       formValues.latitudeSeconds = null;

  //       formValues.longitudeDeg = null;
  //       formValues.longitudeMinutes = null;
  //       formValues.longitudeSeconds = null;
  //     }
  //   } else {
  //     if (formValues.latitudeDec && formValues.longitudeDec) {
  //       let latdegree = fromLatLngToDegree(formValues.latitudeDec, true);
  //       let lngdegree = fromLatLngToDegree(formValues.longitudeDec, true);

  //       formValues.latitudeDeg = latdegree.deg;
  //       formValues.latitudeMinutes = latdegree.min;
  //       formValues.latitudeSeconds = latdegree.sec;

  //       formValues.longitudeDeg = lngdegree.deg;
  //       formValues.longitudeMinutes = lngdegree.min;
  //       formValues.longitudeSeconds = lngdegree.sec;

  //       formValues.latitudeDec = null;
  //       formValues.longitudeDec = null;
  //     }
  //   }
  // };

  const handleSelect = (name) => (value, e) =>
    e !== undefined
      ? name === "dropSystem"
        ? (setdropSystem(e.id), setFormValues({}))
        : name === "dropType"
          ? (setdropType(e.id), setFormValues({}))
          : null
      : null;

  const handleZoneSelect = (set, name) => (value, e) =>
    e !== undefined ? (name === "zoneName" ? set(e.id) : null) : null;

  return (
    <div className="coordinates" style={{ textAlign: "right" }}>
      {/* {loading ? <Loader /> : null} */}
      {!localStorage.getItem("user") ? (
        <Tabs
          id="uncontrolled-tab-example"
          className=""
          activeKey={selectedTab}
          onSelect={(key) => {
            setSelectedKey(key);
          }}
        >
          <Tab eventKey="coord" title={t("decimalCoor")}>
            <Form
              onFinish={onPublicUserDecimalSubmit}
              className="coordinateForm"
              layout="vertical"
              name="validate_other"
              style={{ padding: "10px" }}
            >
              <>
                <Row>
                  <Col span={24} className="">
                    {/* <h5 className="mt-4 "> {t("Latitude")}</h5> */}
                    <Form.Item
                      name="latitude"
                      style={{ marginBlock: "8px" }}
                      rules={[
                        {
                          message: t("LatitudeSelect"),
                          required: true,
                        },
                      ]}
                    >
                      <Input
                        // type="number"
                        // required={true}
                        name="latitude"
                        onChange={handleChange}
                        value={formValues.latitude}
                        defaultValue={formValues.latitude}
                        placeholder={t("Latitude")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24} className="">
                    {/* <h5 className="mt-2"> {t("longitude")}</h5> */}
                    <Form.Item
                      style={{ marginBlock: "8px" }}
                      rules={[
                        {
                          message: t("longitudeSelect"),
                          required: true,
                        },
                      ]}
                      name="longitude"
                    >
                      <Input
                        // type="number"
                        // required={true}
                        name="longitude"
                        onChange={handleChange}
                        value={formValues.longitude}
                        defaultValue={formValues.longitude}
                        placeholder={t("longitude")}
                        label={t("longitude")}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <div style={{ textAlign: "center" }}>
                  <button
                    icon={<SearchOutlined />}
                    onClick={CoordinateSearch}
                    className="SearchBtn mt-3 w-25"
                    size="large"
                    htmlType="submit"
                  >
                    {t("search")}
                  </button>
                </div>
              </>
            </Form>
          </Tab>

          <Tab eventKey="deg-min" title={t("degMinSec")}>
            <Form
              onFinish={onPublicUserDegreesSubmit}
              className="coordinateForm"
              layout="vertical"
              name="validate_other"
              style={{ padding: "10px" }}
            >
              <>
                <h5 className="mt-4 mr-1">{t("Latitude")}</h5>
                <Row style={{ justifyContent: "space-between" }}>
                  <Col span={8}>
                    <Form.Item
                      name="latitudeSeconds"
                      rules={[
                        {
                          message: t("chooseSec"),
                          required: true,
                        },
                      ]}
                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        // required={true}
                        name="latitudeSeconds"
                        onChange={handleChange}
                        value={formValues.latitudeSeconds}
                        defaultValue={formValues.latitudeSeconds}
                        placeholder={t("seconds")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7} className="mr-1">
                    <Form.Item
                      rules={[
                        {
                          message: t("chooseMin"),
                          required: true,
                        },
                      ]}
                      name="latitudeMinutes"

                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        // required={true}
                        name="latitudeMinutes"
                        onChange={handleChange}
                        defaultValue={formValues.latitudeMinutes}
                        value={formValues.latitudeMinutes}
                        placeholder={t("minutes")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7} className="mr-1 ml-2">
                    <Form.Item
                      rules={[
                        {
                          message: t("chooseDeg"),
                          required: true,
                        },
                      ]}
                      name="latitudeDeg"

                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        // required={true}
                        name="latitudeDeg"
                        onChange={handleChange}
                        defaultValue={formValues.latitudeDeg}
                        value={formValues.latitudeDeg}
                        placeholder={t("degrees")}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <h5 className="mt-4 mr-1">{t("longitude")}</h5>
                <Row style={{ justifyContent: "space-between" }}>
                  <Col span={8}>
                    <Form.Item
                      name="longitudeSeconds"
                      rules={[
                        {
                          message: t("chooseSec"),
                          required: true,
                        },
                      ]}

                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        // required={true}
                        name="longitudeSeconds"
                        onChange={handleChange}
                        defaultValue={formValues.longitudeSeconds}
                        value={formValues.longitudeSeconds}
                        placeholder={t("seconds")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7} className="mr-1">
                    <Form.Item
                      name="longitudeMinutes"
                      rules={[
                        {
                          message: t("chooseMin"),
                          required: true,
                        },
                      ]}
                    // help="Should be combination of numbers & alphabets"
                    >
                      <Input
                        // type="number"
                        // required={true}
                        name="longitudeMinutes"
                        onChange={handleChange}
                        defaultValue={formValues.longitudeMinutes}
                        value={formValues.longitudeMinutes}
                        placeholder={t("minutes")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={7} className="mr-1 ml-2">
                    <Form.Item
                      name="longitudeDeg"
                      rules={[
                        {
                          message: t("chooseDeg"),
                          required: true,
                        },
                      ]}
                    >
                      <Input
                        // type="number"
                        // required={true}
                        name="longitudeDeg"
                        onChange={handleChange}
                        defaultValue={formValues.longitudeDeg}
                        value={formValues.longitudeDeg}
                        placeholder={t("degrees")}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <div style={{ textAlign: "center" }}>
                  <button
                    icon={<SearchOutlined />}
                    onClick={degSearch}
                    className="SearchBtn mt-3 w-25"
                    size="large"
                    htmlType="submit"
                  >
                    {t("search")}
                  </button>
                </div>
              </>
            </Form>
          </Tab>
        </Tabs>
      ) : (
        <>
          <Form
            onFinish={onFinish}
            className="coordinateForm"
            layout="vertical"
            name="validate_other"
            style={{ padding: "10px" }}
          >
            <Form.Item label={t("dropSys")} name="dropSystem">
              <Select
                virtual={false}
                // suffixIcon={<DownCircleFilled />}
                suffixIcon={<RiArrowDropDownFill size={30} />}
                showSearch
                allowClear
                // defaultValue="WGS-84"
                className="dont-show"
                onChange={handleSelect("dropSystem")}
                defaultValue={dropSystem === 2 ? "WGS-84" : dropSystem === 3 ? "KSA-GRF17" : undefined}
                value={dropSystem}
                placeholder={t("dropSysSelect")}
                getPopupContainer={(trigger) => trigger.parentNode}
                onClear={() => setdropSystem(undefined)}
                optionFilterProp="value"
                filterOption={(input, option) =>
                  option.value.indexOf(input) >= 0
                }
              >
                <Select.Option value="WGS-84" id={2}>
                  WGS-84
                </Select.Option>
                <Select.Option value="KSA-GRF17" id={3}>
                  KSA GRF17
                </Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              label={t("dropType")}
              name="dropType"
              rules={[
                {
                  message: t("dropTypeSelect"),
                  required: true,
                },
              ]}
            >
              <Select
                virtual={false}
                // suffixIcon={<DownCircleFilled />}
                suffixIcon={<RiArrowDropDownFill size={30} />}
                showSearch
                allowClear
                className="dont-show"
                onChange={handleSelect("dropType")}
                value={dropType}
                defaultValue={dropType === 1 ? "geographic" : dropType == 2 ? "metric" : undefined}
                placeholder={t("dropTypeSelect")}
                getPopupContainer={(trigger) => trigger.parentNode}
                optionFilterProp="value"
                filterOption={(input, option) =>
                  option.value.indexOf(input) >= 0
                }
              >
                <Select.Option value={"geographic"} id={1}>
                  {t("geographic")}
                </Select.Option>
                <Select.Option value={"metric"} id={2}>
                  {t("metric")}
                </Select.Option>
              </Select>
            </Form.Item>

            {dropType && dropSystem && (
              <>
                <div
                  style={{
                    textAlign: "center",
                    marginBlock: "20px",
                    borderRadius: "30px",
                    border: "1px solid #fff",
                    display: "flex",
                    alignItems: "center",
                    overflow: "hidden",
                    fontSize: "12px",
                  }}
                >
                  {/* <Checkbox
                    style={{ fontWeight: "bold" }}
                    onChange={(e) => onChangeCheckBox(e, 1)}
                    checked={metersType == 1}
                  >
                    {t("decimalDeg")}{" "}
                  </Checkbox>
                  <Checkbox
                    style={{ fontWeight: "bold" }}
                    onChange={(e) => onChangeCheckBox(e, 2)}
                    checked={metersType == 2}
                  >
                    {t("degMinSec")}
                  </Checkbox> */}

                  <div
                    style={{
                      flex: "1",
                      paddingBlock: "8px",
                      cursor: "pointer",
                      display: "flex",
                      gap: "5px",
                      justifyContent: "center",
                      alignItems: "center",
                      backgroundColor: `${metersType === 1 ? "#b45333" : "transparent"
                        }`,
                      // color: `${metersType === 1 ? "#fff" : "#000"}`,
                      color: "#fff",
                    }}
                    onClick={() => {
                      onChangeCheckBox(1);
                    }}
                  >
                    {t("decimalDeg")}
                    {metersType === 1 && <FaCheck />}
                  </div>
                  <div
                    style={{
                      flex: "1",
                      paddingBlock: "8px",
                      cursor: "pointer",
                      display: "flex",
                      gap: "5px",
                      justifyContent: "center",
                      alignItems: "center",
                      backgroundColor: `${metersType === 2 ? "#b45333" : "transparent"
                        }`,
                      // color: `${metersType === 2 ? "#fff" : "#000"}`,
                      color: "#fff",
                    }}
                    onClick={() => {
                      onChangeCheckBox(2);
                    }}
                  >
                    {t("degMinSec")}
                    {metersType === 2 && <FaCheck />}
                  </div>
                </div>

                {dropType === 1 ? (
                  <>
                    <h5>
                      {t("Latitude")}( {t("north")} )
                    </h5>
                    {metersType == 2 ? (
                      <Row style={{ justifyContent: "space-between" }}>
                        <Col span={7}>
                          <Form.Item
                            rules={[
                              {
                                message: t("dropTypeSelect"),
                                required: true,
                              },
                            ]}
                          >
                            <Input
                              // type="number"
                              // required={true}
                              name="latitudeSeconds"
                              onChange={handleChange}
                              defaultValue={formValues.latitudeSeconds}
                              value={formValues.latitudeSeconds}
                              placeholder={t("seconds")}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={7} className="mx-1">
                          <Form.Item
                            rules={[
                              {
                                message: t("dropTypeSelect"),
                                required: true,
                              },
                            ]}
                          >
                            <Input
                              // type="number"
                              // required={true}
                              name="latitudeMinutes"
                              onChange={handleChange}
                              defaultValue={formValues.latitudeMinutes}
                              value={formValues.latitudeMinutes}
                              placeholder={t("minutes")}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={7} className="">
                          <Form.Item
                            rules={[
                              {
                                message: t("dropTypeSelect"),
                                required: true,
                              },
                            ]}
                          >
                            <Input
                              // type="number"
                              // required={true}
                              name="latitudeDeg"
                              onChange={handleChange}
                              defaultValue={formValues.latitudeDeg}
                              value={formValues.latitudeDeg}
                              placeholder={t("degrees")}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    ) : (
                      <Row
                        style={{
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <Col span={24}>
                          <Form.Item
                            rules={[
                              {
                                message: t("dropTypeSelect"),
                                required: true,
                              },
                            ]}
                          >
                            <Input
                              // type="number"
                              // required={true}
                              name="latitudeDec"
                              onChange={handleChange}
                              defaultValue={formValues.latitudeDec}
                              value={formValues.latitudeDec}
                              placeholder=""
                            />
                          </Form.Item>
                        </Col>
                        {/* <Col span={4}>
                      <h5
                        style={{
                          fontWeight: "bold",
                          marginRight: "10px",
                          fontSize: ".8rem",
                        }}
                      >
                        {" "}
                        {t("north")} °
                      </h5>
                    </Col> */}
                      </Row>
                    )}

                    <h5>
                      {t("longitude")} ( {t("east")} )
                    </h5>
                    {metersType == 2 ? (
                      <Row style={{ justifyContent: "space-between" }}>
                        <Col span={7}>
                          <Form.Item
                            rules={[
                              {
                                message: t("dropTypeSelect"),
                                required: true,
                              },
                            ]}
                          >
                            <Input
                              // type="number"
                              // required={true}
                              name="longitudeSeconds"
                              onChange={handleChange}
                              defaultValue={formValues.longitudeSeconds}
                              value={formValues.longitudeSeconds}
                              placeholder={t("seconds")}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={7} className="mx-1">
                          <Form.Item
                            rules={[
                              {
                                message: t("dropTypeSelect"),
                                required: true,
                              },
                            ]}
                          >
                            <Input
                              // type="number"
                              // required={true}
                              name="longitudeMinutes"
                              onChange={handleChange}
                              value={formValues.longitudeMinutes}
                              defaultValue={formValues.longitudeMinutes}
                              placeholder={t("minutes")}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={7} className="">
                          <Form.Item
                            rules={[
                              {
                                message: t("dropTypeSelect"),
                                required: true,
                              },
                            ]}
                          >
                            <Input
                              // type="number"
                              // required={true}
                              name="longitudeDeg"
                              onChange={handleChange}
                              value={formValues.longitudeDeg}
                              defaultValue={formValues.longitudeDeg}
                              placeholder={t("degrees")}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    ) : (
                      <Row
                        style={{
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <Col span={24}>
                          <Form.Item
                            rules={[
                              {
                                message: t("dropTypeSelect"),
                                required: true,
                              },
                            ]}
                          >
                            <Input
                              // type="number"
                              // required={true}
                              name="longitudeDec"
                              onChange={handleChange}
                              defaultValue={formValues.longitudeDec}
                              value={formValues.longitudeDec}
                              placeholder=""
                            />
                          </Form.Item>
                        </Col>
                        {/* <Col span={4}>
                      <h5
                        style={{
                          fontWeight: "bold",
                          marginRight: "10px",
                          fontSize: ".8rem",
                        }}
                      >
                        {" "}
                        {t("east")} °
                      </h5>
                    </Col> */}
                      </Row>
                    )}
                  </>
                ) : dropType === 2 ? (
                  <>
                    <Form.Item
                      label={t("zoneName")}
                      name="zoneName"
                      rules={[
                        {
                          message: t("zoneNameSelect"),
                          required: true,
                        },
                      ]}
                    >
                      <Select
                        virtual={false}
                        // suffixIcon={<DownCircleFilled />}
                        suffixIcon={<RiArrowDropDownFill size={30} />}
                        showSearch
                        allowClear
                        //defaultValue={t("KSA GRF17 UTM_zone_36N")}
                        className="dont-show"
                        onChange={handleZoneSelect(
                          setKSA_GRF17_ZoneID,
                          "zoneName"
                        )}
                        defaultValue={KSA_GRF17_ZoneID}
                        value={KSA_GRF17_ZoneID}
                        placeholder={t("zoneNameSelect")}
                        getPopupContainer={(trigger) => trigger.parentNode}
                        optionFilterProp="value"
                        filterOption={(input, option) =>
                          option.value.indexOf(input) >= 0
                        }
                      >
                        <Select.Option
                          value={t("KSA GRF17 UTM_zone_36N")}
                          id={9356}
                        >
                          {t("UTM_zone_36N")}
                        </Select.Option>
                        <Select.Option
                          value={t("KSA GRF17 UTM_zone_37N")}
                          id={9357}
                        >
                          {t("UTM_zone_37N")}
                        </Select.Option>
                        <Select.Option
                          value={t("KSA GRF17 UTM_zone_38N")}
                          id={9358}
                        >
                          {t("UTM_zone_38N")}
                        </Select.Option>
                        <Select.Option
                          value={t("KSA GRF17 UTM_zone_39N")}
                          id={9359}
                        >
                          {t("UTM_zone_39N")}
                        </Select.Option>
                        <Select.Option
                          value={t("KSA GRF17 UTM_zone_40N")}
                          id={9360}
                        >
                          {t("UTM_zone_40N")}
                        </Select.Option>
                      </Select>
                    </Form.Item>

                    <Row>
                      <Col span={24} className="">
                        <div style={{ textAlign: "justify" }}>
                          <label style={{ fontWeight: "bold" }}>
                            {" "}
                            {t("xCoor")}
                          </label>
                        </div>
                        <Form.Item
                          rules={[
                            {
                              message: t("dropTypeSelect"),
                              required: true,
                            },
                          ]}
                        >
                          <Input
                            // type="number"
                            // required={true}
                            name="x"
                            onChange={handleChange}
                            value={formValues.x}
                            defaultValue={formValues.x}
                            placeholder={t("xCoor")}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={24} className="">
                        <div
                          style={{ textAlign: "justify", marginTop: "10px" }}
                        >
                          <label style={{ fontWeight: "bold" }}>
                            {" "}
                            {t("yCoor")}
                          </label>
                        </div>
                        <Form.Item
                          rules={[
                            {
                              message: t("dropTypeSelect"),
                              required: true,
                            },
                          ]}
                        >
                          <Input
                            // type="number"
                            // required={true}
                            name="y"
                            onChange={handleChange}
                            value={formValues.y}
                            defaultValue={formValues.y}
                            placeholder={t("yCoor")}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </>
                ) : null}
              </>
            )}

            {dropSystem !== undefined && dropType !== undefined ? (
              <div style={{ textAlign: "center" }}>
                <button
                  icon={<SearchOutlined />}
                  className="SearchBtn mt-3 w-25"
                  size="large"
                  htmlType="submit"
                >
                  {t("search")}
                </button>
              </div>
            ) : null}

            {KsaPointZone && (
              <div>
                <p className="coordinateData">{t("zoneName")}</p>
                <table className="table table-bordered">
                  <tr>
                    <td>
                      <span> {t(`${KsaPointZone}`)} </span>
                    </td>
                  </tr>
                </table>
              </div>
            )}

            {WGS_Geographic && (
              <div>
                <p className="coordinateData">WGS ( Geographic )</p>
                <table className="table table-bordered">
                  <tr>
                    <td>
                      <span> {t("Latitude")}</span>
                    </td>
                    <td>
                      <span>{WGS_Geographic.latitude}</span>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <span> {t("longitude")}</span>
                    </td>
                    <td>
                      <span>{WGS_Geographic.longitude}</span>
                    </td>
                  </tr>
                </table>
              </div>
            )}
            {WGS_Projected && (
              <div>
                <p className="coordinateData">WGS Projected ( UTM )</p>
                <table className="table table-bordered">
                  <tr>
                    <td>
                      <span> {t("xCoor")}</span>
                    </td>
                    <td>
                      <span>{WGS_Projected.x}</span>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <span>{t("yCoor")}</span>
                    </td>
                    <td>
                      <span>{WGS_Projected.y}</span>
                    </td>
                  </tr>
                </table>
              </div>
            )}

            {KSA_GRF17_Projected && (
              <div>
                <p className="coordinateData">KSA GRF17 Projected ( UTM )</p>
                <table className="table table-bordered">
                  <tr>
                    <td>
                      <span> {t("xCoor")}</span>
                    </td>
                    <td>
                      <span>{KSA_GRF17_Projected.x}</span>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <span> {t("yCoor")} </span>
                    </td>
                    <td>
                      <span>{KSA_GRF17_Projected.y}</span>
                    </td>
                  </tr>
                </table>
              </div>
            )}

            {KSA_GRF17_Geographic && (
              <div>
                <p className="coordinateData">KSA GRF17 ( Geographic )</p>
                <table className="table table-bordered">
                  <tr>
                    <td>
                      <span> {t("Latitude")} </span>
                    </td>
                    <td>
                      <span>{KSA_GRF17_Geographic.latitude}</span>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <span> {t("longitude")}</span>
                    </td>
                    <td>
                      <span>{KSA_GRF17_Geographic.longitude}</span>
                    </td>
                  </tr>
                </table>
              </div>
            )}
          </Form>
          {errorMessage && (
            <div style={{ textAlign: "center" }}>
              <label style={{ fontSize: "18px", color: "red" }}>
                {errorMessage}
              </label>
            </div>
          )}
        </>
      )}
    </div>
  );
}
