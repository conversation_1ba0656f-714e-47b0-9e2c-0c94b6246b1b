import React, { useState, useEffect } from "react";
import isDeepEqual from "fast-deep-equal/react";
import { <PERSON><PERSON>, Mo<PERSON>, Spin } from "antd";

import { useTranslation } from "react-i18next";
import Loader from "../../../../containers/Loader";
import Bar<PERSON><PERSON>Comp from "./BarChart";
import Donut<PERSON><PERSON> from "./DonutChart";
import <PERSON><PERSON><PERSON>Comp from "./lineC<PERSON>";
import <PERSON><PERSON><PERSON> from "./PieChart";
import Polar<PERSON><PERSON><PERSON><PERSON> from "./PolarAreaChart";
import RadialBar from "./RadialBar";
import { getLayerId } from "../../../../helper/common_func";
import {
  getStatisticsDataForChart,
  getTimePeriod,
} from "../../helpers/helperFunc";
import MAPLOGO from "../../../../assets/images/dashboard/map-marker.png";
import { incidentLayerName } from "../../../../helper/layers";
import {
  getDateInFormatYYYYMMDD,
  getUniqueArrValues,
  notificationMessage,
} from "../../../../helper/utilsFunc";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlusCircle, faExpandArrowsAlt } from "@fortawesome/free-solid-svg-icons";
function GenericChart({
  chartObj,
  onClickTitle,
  selectedLayer,
  title,
  type,
  map,
  sideTblTitle,
  queryData,
}) {
  const { t } = useTranslation("dashboard");
  const dateDataRef = React.useRef();
  const boundaryTypeRef = React.useRef("MUNICIPALITY_NAME");
  const [preparedChartData, setPreparedChartData] = useState(null);
  const [preparedChildChartData, setPreparedChildChartData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(null);

  if (
    !isDeepEqual(dateDataRef.current, queryData?.selectedTimeContext?.dateData)
  ) {
    dateDataRef.current = queryData?.selectedTimeContext?.dateData;
  }
  console.log(isModalOpen);
  useEffect(() => {
    let layerID = chartObj?.dependentLayer
      ? getLayerId(map.__mapInfo, chartObj?.dependentLayer)
      : getLayerId(map.__mapInfo, selectedLayer);
    let statisticsObj = chartObj;
    let fieldName = chartObj?.relatedChildChart
      ? queryData?.selectedBoundaryType?.value || chartObj.name
      : chartObj.name;
    if (chartObj?.relatedChildChart) statisticsObj.name = fieldName;
    let timeContextType = queryData.selectedTimeContext.type;
    let whereClause = "";
    if (
      queryData?.selectedBoundaryType?.value &&
      queryData?.selectedBoundaryType?.selectedBoundary
    ) {
      if (
        queryData?.selectedBoundaryType?.value === "MUNICIPALITY_NAME" &&
        chartObj?.dependentLayer === "construction_license"
      ) {
        console.log(chartObj?.dependentLayer);
        //get mun_name from domain
        let mapLayersArr = [...map.__mapInfo.info.$layers.layers];
        let currentLayer = mapLayersArr.find(
          (i) => i.name === selectedLayer
        ).fields;
        let domains = currentLayer.find((i) => i.name === "MUNICIPALITY_NAME")
          .domain?.codedValues;
        if (domains) {
          let domain = domains.find((d) => {
            return (
              d?.code === queryData?.selectedBoundaryType?.selectedBoundary
            );
          });

          whereClause += `BALADIA_NAME LIKE '%${domain.name}%'`;
        }
      } else if (
        queryData?.selectedBoundaryType?.value === "DISTRICT_NAME" &&
        chartObj?.dependentLayer === "construction_license"
      ) {
        console.log(chartObj?.dependentLayer);
        let mapLayersArr = [...map.__mapInfo.info.$layers.layers];
        let currentLayer = mapLayersArr.find(
          (i) => i.name === selectedLayer
        ).fields;
        let domains = currentLayer.find((i) => i.name === "DISTRICT_NAME")
          .domain?.codedValues;
        if (domains) {
          let domain = domains.find((d) => {
            return (
              d?.code === queryData?.selectedBoundaryType?.selectedBoundary
            );
          });

          whereClause += `DISTRICTNAME LIKE '%${domain.name}%'`;
        }
      } else
        whereClause += `${queryData?.selectedBoundaryType?.value} = ${queryData?.selectedBoundaryType?.selectedBoundary}`;
    }
    if (queryData.selectedTimeContext.dateData?.length) {
      let timePeriod = getTimePeriod(queryData);
      let extractedPeriod =
        timeContextType === "yearly"
          ? "YEAR"
          : timeContextType === "monthly"
          ? "MONTH"
          : "DAY";
      let extractedStatment =
        timeContextType === "yearly"
          ? `EXTRACT (${extractedPeriod} FROM ${
              chartObj?.filterDateField || "CREATED_DATE"
            })`
          : timeContextType === "monthly"
          ? `EXTRACT (${extractedPeriod} FROM ${
              chartObj?.filterDateField || "CREATED_DATE"
            }), EXTRACT (YEAR FROM ${
              chartObj?.filterDateField || "CREATED_DATE"
            })`
          : `EXTRACT (${extractedPeriod} FROM ${
              chartObj?.filterDateField || "CREATED_DATE"
            }),EXTRACT (MONTH FROM ${
              chartObj?.filterDateField || "CREATED_DATE"
            }), EXTRACT (YEAR FROM ${
              chartObj?.filterDateField || "CREATED_DATE"
            })`;
      let whereClauseDueToTimeContext =
        timeContextType === "yearly"
          ? `${extractedStatment} IN (${timePeriod.join(",")})`
          : timePeriod.length > 1 &&
            ["monthly", "daily"].includes(timeContextType)
          ? "" +
            `${chartObj?.filterDateField || "CREATED_DATE"}` +
            " BETWEEN DATE '" +
            timePeriod.join("' AND DATE '") +
            "'"
          : `${
              chartObj?.filterDateField || "CREATED_DATE"
            } BETWEEN TIMESTAMP '${timePeriod.join(" ")} 00:00:00' AND TIMESTAMP '${timePeriod.join(" ")} 23:59:59'`;
      whereClause = whereClause
        ? whereClause + " AND " + whereClauseDueToTimeContext
        : whereClauseDueToTimeContext;
    }
    if (chartObj?.restrictionDateWhereClause)
      whereClause = whereClause
        ? whereClause +
          ` AND ${
            chartObj?.restrictionDateWhereClause
          } '${getDateInFormatYYYYMMDD(new Date())}'`
        : `${chartObj?.restrictionDateWhereClause} '${getDateInFormatYYYYMMDD(
            new Date()
          )}'`;
    if (chartObj?.restrictionWhereClause)
      whereClause = whereClause
        ? whereClause + ` AND ${chartObj?.restrictionWhereClause} `
        : `${chartObj?.restrictionWhereClause} `;
    setIsLoading(true);
    getStatisticsDataForChart(
      statisticsObj,
      layerID,
      ({ data }) => {
        let reqData = [];
        if (chartObj?.maxShownItems) {
          reqData = data
            .filter((item) => item[fieldName])
            .slice(0, chartObj?.maxShownItems);
        } else reqData = [...data];
        let haveCodeValue = reqData.find((i) => i[fieldName + "_Code"]);
        let preparedSeries = reqData.reduce((cu, item) => {
          let hasArea = statisticsObj.shownData.find((i) => i === "area");
          if (!cu.length) {
            if (
              (![null, -1, undefined].includes(item[fieldName]) &&
                !haveCodeValue) ||
              (haveCodeValue &&
                item[fieldName + "_Code"] &&
                ![null, -1, undefined].includes(item[fieldName + "_Code"]))
            )
              //to filter null, no value
              hasArea
                ? cu.push({
                    label: item[fieldName],
                    count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                    area: parseFloat(item["AREADISTINCT"] || item["AREA"]),
                    value:
                      haveCodeValue &&
                      item[fieldName + "_Code"] &&
                      ![null, -1, undefined].includes(item[fieldName + "_Code"])
                        ? item[fieldName + "_Code"]
                        : item[fieldName],
                  })
                : cu.push({
                    label: item[fieldName],
                    count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                    value:
                      haveCodeValue &&
                      item[fieldName + "_Code"] &&
                      ![null, -1, undefined].includes(item[fieldName + "_Code"])
                        ? item[fieldName + "_Code"]
                        : item[fieldName],
                  });
            else
              hasArea
                ? cu.push({
                    label: t("NotDefined"),
                    count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                    area: parseFloat(item["AREADISTINCT"] || item["AREA"]),
                    value:
                      haveCodeValue &&
                      item[fieldName + "_Code"] &&
                      ![null, -1, undefined].includes(item[fieldName + "_Code"])
                        ? item[fieldName + "_Code"]
                        : item[fieldName],
                  })
                : cu.push({
                    label: t("NotDefined"),
                    count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                    value:
                      haveCodeValue &&
                      item[fieldName + "_Code"] &&
                      ![null, -1, undefined].includes(item[fieldName + "_Code"])
                        ? item[fieldName + "_Code"]
                        : item[fieldName],
                  });
          } else {
            if (
              (![null, -1, undefined].includes(item[fieldName]) &&
                !haveCodeValue) ||
              (haveCodeValue &&
                item[fieldName + "_Code"] &&
                ![null, -1, undefined].includes(item[fieldName + "_Code"]))
            )
              hasArea
                ? cu.push({
                    label: item[fieldName],
                    count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                    area: parseFloat(item["AREADISTINCT"] || item["AREA"]),
                    value:
                      haveCodeValue &&
                      item[fieldName + "_Code"] &&
                      ![null, -1, undefined].includes(item[fieldName + "_Code"])
                        ? item[fieldName + "_Code"]
                        : item[fieldName],
                  })
                : cu.push({
                    label: item[fieldName],
                    count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                    value:
                      haveCodeValue &&
                      item[fieldName + "_Code"] &&
                      ![null, -1, undefined].includes(item[fieldName + "_Code"])
                        ? item[fieldName + "_Code"]
                        : item[fieldName],
                  });
            else {
              let isUndefinedExist = cu.find(
                (i) => i.label === t("NotDefined")
              );
              if (isUndefinedExist) {
                isUndefinedExist["count"] += parseFloat(
                  item["COUNTDISTINCT"] || item["COUNT"]
                );
                if (hasArea)
                  isUndefinedExist.area += parseFloat(
                    item["AREADISTINCT"] || item["AREA"]
                  );
              } else
                hasArea
                  ? cu.push({
                      label: t("NotDefined"),
                      count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                      area: parseFloat(item["AREADISTINCT"] || item["AREA"]),
                      value:
                        haveCodeValue &&
                        item[fieldName + "_Code"] &&
                        ![null, -1, undefined].includes(
                          item[fieldName + "_Code"]
                        )
                          ? item[fieldName + "_Code"]
                          : item[fieldName],
                    })
                  : cu.push({
                      label: t("NotDefined"),
                      count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                      value:
                        haveCodeValue &&
                        item[fieldName + "_Code"] &&
                        ![null, -1, undefined].includes(
                          item[fieldName + "_Code"]
                        )
                          ? item[fieldName + "_Code"]
                          : item[fieldName],
                    });
            }
          }

          return cu;
        }, []);
        setPreparedChartData(preparedSeries);
        if (chartObj?.relatedChildChart) {
          getChildChartData(preparedSeries, whereClause, chartObj);
        } else setIsLoading(false);
      },
      (err) => {
        console.log(err);
        setIsLoading(false);
      },
      whereClause //where clause
    );
  }, [
    selectedLayer,
    dateDataRef.current,
    queryData?.selectedBoundaryType?.selectedBoundary,
  ]);

  useEffect(() => {
    if (
      chartObj?.relatedChildChart &&
      queryData?.selectedLayer === "incidents940"
    ) {
      if (
        queryData?.selectedBoundaryType?.value &&
        boundaryTypeRef.current === queryData?.selectedBoundaryType?.value
      )
        return;
      else if (
        !queryData?.selectedBoundaryType?.value &&
        boundaryTypeRef.current !== "MUNICIPALITY_NAME"
      ) {
        boundaryTypeRef.current = "MUNICIPALITY_NAME";
      } else if (
        chartObj?.relatedChildChart &&
        queryData?.selectedBoundaryType?.value &&
        !(boundaryTypeRef.current === queryData?.selectedBoundaryType?.value)
      )
        boundaryTypeRef.current = queryData?.selectedBoundaryType?.value;
      let layerID = chartObj?.dependentLayer
        ? getLayerId(map.__mapInfo, chartObj?.dependentLayer)
        : getLayerId(map.__mapInfo, selectedLayer);
      let statisticsObj = { ...chartObj };
      let fieldName = chartObj?.relatedChildChart
        ? queryData?.selectedBoundaryType?.value || chartObj.name
        : chartObj.name;
      if (chartObj?.relatedChildChart) statisticsObj.name = fieldName;
      let timeContextType = queryData.selectedTimeContext.type;
      let whereClause = "";
      if (
        queryData?.selectedBoundaryType?.value &&
        queryData?.selectedBoundaryType?.selectedBoundary
      ) {
        whereClause += `${queryData?.selectedBoundaryType?.value} = ${queryData?.selectedBoundaryType?.selectedBoundary}`;
      }
      if (queryData.selectedTimeContext.dateData?.length) {
        let timePeriod = getTimePeriod(queryData);
        let extractedPeriod =
          timeContextType === "yearly"
            ? "YEAR"
            : timeContextType === "monthly"
            ? "MONTH"
            : "DAY";
        let extractedStatment =
          timeContextType === "yearly"
            ? `EXTRACT (${extractedPeriod} FROM ${
                chartObj?.filterDateField || "CREATED_DATE"
              })`
            : timeContextType === "monthly"
            ? `EXTRACT (${extractedPeriod} FROM ${
                chartObj?.filterDateField || "CREATED_DATE"
              }), EXTRACT (YEAR FROM ${
                chartObj?.filterDateField || "CREATED_DATE"
              })`
            : `EXTRACT (${extractedPeriod} FROM ${
                chartObj?.filterDateField || "CREATED_DATE"
              }),EXTRACT (MONTH FROM ${
                chartObj?.filterDateField || "CREATED_DATE"
              }), EXTRACT (YEAR FROM ${
                chartObj?.filterDateField || "CREATED_DATE"
              })`;
        let whereClauseDueToTimeContext =
          timeContextType === "yearly"
            ? `${extractedStatment} IN (${timePeriod.join(",")})`
            : timePeriod.length > 1 &&
              ["monthly", "daily"].includes(timeContextType)
            ? "" +
              `${chartObj?.filterDateField || "CREATED_DATE"}` +
              " BETWEEN DATE '" +
              timePeriod.join("' AND DATE '") +
              "'"
            : `${
                chartObj?.filterDateField || "CREATED_DATE"
              } BETWEEN TIMESTAMP '${timePeriod.join(" ")} 00:00:00' AND TIMESTAMP '${timePeriod.join(" ")} 23:59:59'`;
        whereClause = whereClause
          ? whereClause + " AND " + whereClauseDueToTimeContext
          : whereClauseDueToTimeContext;
      }
      if (chartObj?.restrictionDateWhereClause)
        whereClause = whereClause
          ? whereClause +
            ` AND ${
              chartObj?.restrictionDateWhereClause
            } '${getDateInFormatYYYYMMDD(new Date())}'`
          : `${chartObj?.restrictionDateWhereClause} '${getDateInFormatYYYYMMDD(
              new Date()
            )}'`;
      if (chartObj?.restrictionWhereClause)
        whereClause = whereClause
          ? whereClause + ` AND ${chartObj?.restrictionWhereClause} `
          : `${chartObj?.restrictionWhereClause} `;
      setIsLoading(true);
      getStatisticsDataForChart(
        statisticsObj,
        layerID,
        ({ data }) => {
          let reqData = [];
          if (chartObj?.maxShownItems) {
            reqData = data
              .filter((item) => item[fieldName])
              .slice(0, chartObj?.maxShownItems);
          } else reqData = [...data];
          let haveCodeValue = reqData.find((i) => i[fieldName + "_Code"]);
          let preparedSeries = reqData.reduce((cu, item) => {
            let hasArea = statisticsObj.shownData.find((i) => i === "area");
            if (!cu.length) {
              if (
                (![null, -1, undefined].includes(item[fieldName]) &&
                  !haveCodeValue) ||
                (haveCodeValue &&
                  item[fieldName + "_Code"] &&
                  ![null, -1, undefined].includes(item[fieldName + "_Code"]))
              )
                //to filter null, no value
                hasArea
                  ? cu.push({
                      label: item[fieldName],
                      count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                      area: parseFloat(item["AREADISTINCT"] || item["AREA"]),
                      value:
                        haveCodeValue &&
                        item[fieldName + "_Code"] &&
                        ![null, -1, undefined].includes(
                          item[fieldName + "_Code"]
                        )
                          ? item[fieldName + "_Code"]
                          : item[fieldName],
                    })
                  : cu.push({
                      label: item[fieldName],
                      count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                      value:
                        haveCodeValue &&
                        item[fieldName + "_Code"] &&
                        ![null, -1, undefined].includes(
                          item[fieldName + "_Code"]
                        )
                          ? item[fieldName + "_Code"]
                          : item[fieldName],
                    });
              else
                hasArea
                  ? cu.push({
                      label: t("NotDefined"),
                      count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                      area: parseFloat(item["AREADISTINCT"] || item["AREA"]),
                      value:
                        haveCodeValue &&
                        item[fieldName + "_Code"] &&
                        ![null, -1, undefined].includes(
                          item[fieldName + "_Code"]
                        )
                          ? item[fieldName + "_Code"]
                          : item[fieldName],
                    })
                  : cu.push({
                      label: t("NotDefined"),
                      count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                      value:
                        haveCodeValue &&
                        item[fieldName + "_Code"] &&
                        ![null, -1, undefined].includes(
                          item[fieldName + "_Code"]
                        )
                          ? item[fieldName + "_Code"]
                          : item[fieldName],
                    });
            } else {
              if (
                (![null, -1, undefined].includes(item[fieldName]) &&
                  !haveCodeValue) ||
                (haveCodeValue &&
                  item[fieldName + "_Code"] &&
                  ![null, -1, undefined].includes(item[fieldName + "_Code"]))
              )
                hasArea
                  ? cu.push({
                      label: item[fieldName],
                      count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                      area: parseFloat(item["AREADISTINCT"] || item["AREA"]),
                      value:
                        haveCodeValue &&
                        item[fieldName + "_Code"] &&
                        ![null, -1, undefined].includes(
                          item[fieldName + "_Code"]
                        )
                          ? item[fieldName + "_Code"]
                          : item[fieldName],
                    })
                  : cu.push({
                      label: item[fieldName],
                      count: parseFloat(item["COUNTDISTINCT"] || item["COUNT"]),
                      value:
                        haveCodeValue &&
                        item[fieldName + "_Code"] &&
                        ![null, -1, undefined].includes(
                          item[fieldName + "_Code"]
                        )
                          ? item[fieldName + "_Code"]
                          : item[fieldName],
                    });
              else {
                let isUndefinedExist = cu.find(
                  (i) => i.label === t("NotDefined")
                );
                if (isUndefinedExist) {
                  isUndefinedExist["count"] += parseFloat(
                    item["COUNTDISTINCT"] || item["COUNT"]
                  );
                  if (hasArea)
                    isUndefinedExist.area += parseFloat(
                      item["AREADISTINCT"] || item["AREA"]
                    );
                } else
                  hasArea
                    ? cu.push({
                        label: t("NotDefined"),
                        count: parseFloat(
                          item["COUNTDISTINCT"] || item["COUNT"]
                        ),
                        area: parseFloat(item["AREADISTINCT"] || item["AREA"]),
                        value:
                          haveCodeValue &&
                          item[fieldName + "_Code"] &&
                          ![null, -1, undefined].includes(
                            item[fieldName + "_Code"]
                          )
                            ? item[fieldName + "_Code"]
                            : item[fieldName],
                      })
                    : cu.push({
                        label: t("NotDefined"),
                        count: parseFloat(
                          item["COUNTDISTINCT"] || item["COUNT"]
                        ),
                        value:
                          haveCodeValue &&
                          item[fieldName + "_Code"] &&
                          ![null, -1, undefined].includes(
                            item[fieldName + "_Code"]
                          )
                            ? item[fieldName + "_Code"]
                            : item[fieldName],
                      });
              }
            }

            return cu;
          }, []);
          setPreparedChartData(preparedSeries);
          if (chartObj?.relatedChildChart) {
            getChildChartData(preparedSeries, whereClause, chartObj);
          } else setIsLoading(false);
        },
        (err) => {
          console.log(err);
          setIsLoading(false);
        },
        whereClause //where clause
      );
    }
  }, [queryData?.selectedBoundaryType?.value]);

  const getChildChartData = async (preparedSeries, whereClause, chartObj) => {
    let { relatedChildChart } = chartObj;
    let queryValues = preparedSeries.map((item) => item.value);

    console.log({ queryValues });
    let layerID = chartObj?.dependentLayer
      ? getLayerId(map.__mapInfo, chartObj?.dependentLayer)
      : getLayerId(map.__mapInfo, selectedLayer);
    let statisticsObj = relatedChildChart;
    let fieldName = queryData?.selectedBoundaryType?.value || chartObj.name;
    try {
      let promises = preparedSeries.map((queryVal) => {
        let whereClauseClone = whereClause;
        let addedWhere = "";

        if (queryVal.value)
          addedWhere =
            fieldName === "PLAN_NO"
              ? ` AND ${fieldName} = '${queryVal.value}'`
              : ` AND ${fieldName} = ${queryVal.value}`;
        else
          addedWhere =
            fieldName === "PLAN_NO"
              ? ` AND ${fieldName} is null `
              : ` AND ${fieldName} is null`;
        whereClauseClone = whereClauseClone + addedWhere;
        return new Promise((resolve, reject) => {
          getStatisticsDataForChart(
            statisticsObj,
            layerID,
            ({ data }) => {
              let fieldNameChildChart =
                chartObj?.relatedChildChart?.name ||
                queryData?.selectedBoundaryType?.value ||
                chartObj?.name;
              let reqData = [];
              if (relatedChildChart?.maxShownItems) {
                reqData = data.slice(0, relatedChildChart?.maxShownItems);
              } else reqData = [...data];
              let haveCodeValue = reqData.find(
                (i) => i[fieldNameChildChart + "_Code"]
              );
              let preparedSeriesChildChart = reqData.reduce((cu, item) => {
                let hasArea = statisticsObj.shownData.find((i) => i === "area");
                if (!cu.length) {
                  if (
                    (![null, -1, undefined].includes(
                      item[fieldNameChildChart]
                    ) &&
                      !haveCodeValue) ||
                    (haveCodeValue &&
                      item[fieldNameChildChart + "_Code"] &&
                      ![null, -1, undefined].includes(
                        item[fieldNameChildChart + "_Code"]
                      ))
                  )
                    //to filter null, no value
                    hasArea
                      ? cu.push({
                          label: item[fieldNameChildChart],
                          count: parseFloat(
                            item["COUNTDISTINCT"] || item["COUNT"]
                          ),
                          area: parseFloat(
                            item["AREADISTINCT"] || item["AREA"]
                          ),
                          value:
                            haveCodeValue &&
                            item[fieldNameChildChart + "_Code"] &&
                            ![null, -1, undefined].includes(
                              item[fieldNameChildChart + "_Code"]
                            )
                              ? item[fieldNameChildChart + "_Code"]
                              : item[fieldNameChildChart],
                        })
                      : cu.push({
                          label: item[fieldNameChildChart],
                          count: parseFloat(
                            item["COUNTDISTINCT"] || item["COUNT"]
                          ),
                          value:
                            haveCodeValue &&
                            item[fieldNameChildChart + "_Code"] &&
                            ![null, -1, undefined].includes(
                              item[fieldNameChildChart + "_Code"]
                            )
                              ? item[fieldNameChildChart + "_Code"]
                              : item[fieldNameChildChart],
                        });
                  else
                    hasArea
                      ? cu.push({
                          label: t("NotDefined"),
                          count: parseFloat(
                            item["COUNTDISTINCT"] || item["COUNT"]
                          ),
                          area: parseFloat(
                            item["AREADISTINCT"] || item["AREA"]
                          ),
                          value:
                            haveCodeValue &&
                            item[fieldNameChildChart + "_Code"] &&
                            ![null, -1, undefined].includes(
                              item[fieldNameChildChart + "_Code"]
                            )
                              ? item[fieldNameChildChart + "_Code"]
                              : item[fieldNameChildChart],
                        })
                      : cu.push({
                          label: t("NotDefined"),
                          count: parseFloat(
                            item["COUNTDISTINCT"] || item["COUNT"]
                          ),
                          value:
                            haveCodeValue &&
                            item[fieldNameChildChart + "_Code"] &&
                            ![null, -1, undefined].includes(
                              item[fieldNameChildChart + "_Code"]
                            )
                              ? item[fieldNameChildChart + "_Code"]
                              : item[fieldNameChildChart],
                        });
                } else {
                  if (
                    (![null, -1, undefined].includes(
                      item[fieldNameChildChart]
                    ) &&
                      !haveCodeValue) ||
                    (haveCodeValue &&
                      item[fieldNameChildChart + "_Code"] &&
                      ![null, -1, undefined].includes(
                        item[fieldNameChildChart + "_Code"]
                      ))
                  )
                    hasArea
                      ? cu.push({
                          label: item[fieldNameChildChart],
                          count: parseFloat(
                            item["COUNTDISTINCT"] || item["COUNT"]
                          ),
                          area: parseFloat(
                            item["AREADISTINCT"] || item["AREA"]
                          ),
                          value:
                            haveCodeValue &&
                            item[fieldNameChildChart + "_Code"] &&
                            ![null, -1, undefined].includes(
                              item[fieldNameChildChart + "_Code"]
                            )
                              ? item[fieldNameChildChart + "_Code"]
                              : item[fieldNameChildChart],
                        })
                      : cu.push({
                          label: item[fieldNameChildChart],
                          count: parseFloat(
                            item["COUNTDISTINCT"] || item["COUNT"]
                          ),
                          value:
                            haveCodeValue &&
                            item[fieldNameChildChart + "_Code"] &&
                            ![null, -1, undefined].includes(
                              item[fieldNameChildChart + "_Code"]
                            )
                              ? item[fieldNameChildChart + "_Code"]
                              : item[fieldNameChildChart],
                        });
                  else {
                    let isUndefinedExist = cu.find(
                      (i) => i.label === t("NotDefined")
                    );
                    if (isUndefinedExist) {
                      isUndefinedExist["count"] += parseFloat(
                        item["COUNTDISTINCT"] || item["COUNT"]
                      );
                      if (hasArea)
                        isUndefinedExist.area += parseFloat(
                          item["AREADISTINCT"] || item["AREA"]
                        );
                    } else
                      hasArea
                        ? cu.push({
                            label: t("NotDefined"),
                            count: parseFloat(
                              item["COUNTDISTINCT"] || item["COUNT"]
                            ),
                            area: parseFloat(
                              item["AREADISTINCT"] || item["AREA"]
                            ),
                            value:
                              haveCodeValue &&
                              item[fieldNameChildChart + "_Code"] &&
                              ![null, -1, undefined].includes(
                                item[fieldNameChildChart + "_Code"]
                              )
                                ? item[fieldNameChildChart + "_Code"]
                                : item[fieldNameChildChart],
                          })
                        : cu.push({
                            label: t("NotDefined"),
                            count: parseFloat(
                              item["COUNTDISTINCT"] || item["COUNT"]
                            ),
                            value:
                              haveCodeValue &&
                              item[fieldNameChildChart + "_Code"] &&
                              ![null, -1, undefined].includes(
                                item[fieldNameChildChart + "_Code"]
                              )
                                ? item[fieldNameChildChart + "_Code"]
                                : item[fieldNameChildChart],
                          });
                  }
                }

                return cu;
              }, []);
              // setPreparedChildChartData(preparedSeriesChildChart);

              // setIsLoading(false);
              resolve({ data: preparedSeriesChildChart, name: queryVal.label });
            },
            (err) => {
              console.log(err);
              reject(err);
              // setIsLoading(false);
            },
            whereClauseClone //where clause
          );
        });
      });
      let results = await Promise.all(promises);

      let allChildLabels = results.flatMap((i) => {
        return i.data.map((j) => j.value);
      });
      let uniqValuesArr = getUniqueArrValues(allChildLabels);
      let childChartData = uniqValuesArr.map((item) => {
        let reqData = results.map((resItem) => {
          return resItem.data.find((dItem) => dItem.value === item)?.count || 0;
        });
        return {
          name: item,
          data: reqData,
        };
      });
      // let childChartData= results.map(i=>{
      //   uniqValuesArr.forEach(item=>{

      //           if(i.data.map(ii=>ii.value).includes(item)) return;
      //           else i.data.push({label:item, value:item,count:0 })

      //   })
      //   return i
      // });
      setPreparedChildChartData({
        data: childChartData,
        categories: results.map((o) => o.name),
        chartOriginalData: results,
      });
      setIsLoading(false);
      console.log({ results, childChartData });
    } catch (err) {
      console.log({ err });
      notificationMessage("حدث خطأ أثناء استرجاع المعلومات");
      setIsLoading(false);
    }
  };
  const renderChartElem = (type, chartObject, childChart) => {
    if (preparedChartData && !isLoading) {
      let isParent = chartObject?.relatedChildChart && !childChart;
      let reqTitle = title;
      if (
        isParent &&
        queryData?.selectedBoundaryType?.value &&
        queryData?.selectedLayer === "incidents940"
      ) {
        switch (queryData?.selectedBoundaryType?.value) {
          case "MUNICIPALITY_NAME":
            reqTitle = t("incidentNuPermuniciplityName");
            break;
          case "SUB_MUNICIPALITY_NAME":
            reqTitle = t("incidentNuPerSubMuniciplityName");
            break;
          case "DISTRICT_NAME":
            reqTitle = t("incidentNuPerDistrictName");
            break;
          default:
            break;
        }
      }
      let titleChart = childChart
        ? t(chartObject?.relatedChildChart?.alias)
        : reqTitle;
      if (queryData?.selectedBoundaryType?.selectedBoundary && childChart) {
        let selectedBound =
          queryData?.selectedBoundaryType?.boundariesArr?.find(
            (i) => i.value === queryData?.selectedBoundaryType?.selectedBoundary
          )?.name;
        if (selectedBound) titleChart += ` (${selectedBound})`;
      }
      if (["bar", "col"].includes(type))
        return (
          <BarChartComp
            preparedChartData={
              childChart ? preparedChildChartData : preparedChartData
            }
            shownDataTypes={
              childChart
                ? chartObject?.relatedChildChart?.shownData
                : chartObject.shownData
            }
            title={titleChart}
            onClickTitle={onClickTitle}
            sideTblTitle={sideTblTitle}
            type={childChart ? chartObject?.relatedChildChart?.type : type}
            mapLogoSrc={MAPLOGO}
            isChildChart={childChart}
          />
        );
      else if (type === "line")
        return (
          <LineChartComp
            preparedChartData={
              childChart ? preparedChildChartData : preparedChartData
            }
            shownDataTypes={
              childChart
                ? chartObject?.relatedChildChart?.shownData
                : chartObject.shownData
            }
            title={
              childChart ? t(chartObject?.relatedChildChart?.alias) : title
            }
            onClickTitle={onClickTitle}
            sideTblTitle={sideTblTitle}
            mapLogoSrc={MAPLOGO}
            isChildChart={childChart}
          />
        );
      else if (type === "pie")
        return (
          <PieChart
            preparedChartData={
              childChart ? preparedChildChartData : preparedChartData
            }
            shownDataTypes={
              childChart
                ? chartObject?.relatedChildChart?.shownData
                : chartObject.shownData
            }
            title={
              childChart ? t(chartObject?.relatedChildChart?.alias) : title
            }
            onClickTitle={onClickTitle}
            sideTblTitle={sideTblTitle}
            mapLogoSrc={MAPLOGO}
            isChildChart={childChart}
          />
        );
      else if (type === "donut")
        return (
          <DonutChart
            preparedChartData={
              childChart ? preparedChildChartData : preparedChartData
            }
            shownDataTypes={
              childChart
                ? chartObject?.relatedChildChart?.shownData
                : chartObject.shownData
            }
            title={
              childChart ? t(chartObject?.relatedChildChart?.alias) : title
            }
            onClickTitle={onClickTitle}
            sideTblTitle={sideTblTitle}
            mapLogoSrc={MAPLOGO}
            isChildChart={childChart}
          />
        );
      else if (type === "radialBar")
        return (
          <RadialBar
            preparedChartData={
              childChart ? preparedChildChartData : preparedChartData
            }
            shownDataTypes={
              childChart
                ? chartObject?.relatedChildChart?.shownData
                : chartObject.shownData
            }
            title={
              childChart ? t(chartObject?.relatedChildChart?.alias) : title
            }
            onClickTitle={onClickTitle}
            sideTblTitle={sideTblTitle}
            mapLogoSrc={MAPLOGO}
            isChildChart={childChart}
          />
        );
      else if (type === "polarArea")
        return (
          <PolarAreaChart
            preparedChartData={
              childChart ? preparedChildChartData : preparedChartData
            }
            shownDataTypes={
              childChart
                ? chartObject?.relatedChildChart?.shownData
                : chartObject.shownData
            }
            title={
              childChart ? t(chartObject?.relatedChildChart?.alias) : title
            }
            onClickTitle={onClickTitle}
            sideTblTitle={sideTblTitle}
            mapLogoSrc={MAPLOGO}
            isChildChart={childChart}
          />
        );
      else return null;
    } else return <Spin />;
  };

  //component render
  if (!chartObj?.relatedChildChart) return renderChartElem(type, chartObj);
  else if (chartObj?.relatedChildChart)
    return (
      <React.Fragment>
        <div className="normal-chart-item mapSquare">
          <FontAwesomeIcon
            onClick={() => setIsModalOpen(1)}
            className="faplusChart"
            // icon={faPlusCircle}
            icon={faExpandArrowsAlt}
          />
          <Modal
            cancelText="إغلاق"
            className=""
            visible={isModalOpen == 1 ? true : false}
            onCancel={() => setIsModalOpen(null)}
            footer={null}
            width={"50%"}>
            {" "}
            {renderChartElem(type, chartObj)}
          </Modal>

          {renderChartElem(type, chartObj)}
         
        </div>
        <div className="normal-chart-item mapSquare">
        
          <FontAwesomeIcon
            onClick={() => setIsModalOpen(2)}
            className="faplusChart"
            // icon={faPlusCircle}
            icon={faExpandArrowsAlt}
          />
          {renderChartElem(type, chartObj, true)}
        </div> <Modal
            cancelText="إغلاق"
            className=""
            visible={isModalOpen == 2 ? true : false}
            onCancel={() => setIsModalOpen(null)}
            footer={null}
            width={"50%"}>
         
            {renderChartElem(type, chartObj, true)}
          </Modal>
      </React.Fragment>
    );
  else return null;
}

export default GenericChart;
