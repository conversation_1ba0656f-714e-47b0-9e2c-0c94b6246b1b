import React, { Component } from "react";

import {
  createMap,
} from "./helpers";

import { find } from "lodash";
import { getMapInfo } from "../../../helper/common_func";
import SpatialReference from "@arcgis/core/geometry/SpatialReference";
import Extent from "@arcgis/core/geometry/Extent";

class MapComponent extends Component {
  shouldComponentUpdate() {
    return false;
  }

  componentDidMount() {
    let mapLayers = [
        {
            id: "baseMap",
            url: window.mapUrl,
            opacity: 1,
            type: "MapImageLayer"
        },
        {
            id: "zoomGraphicLayer", type: "GraphicsLayer", opacity: 1
        }
    ];

    getMapInfo(window.mapUrl).then((data) => {
      window.mapInfo = data;
          var mapSetting = {};
          let extent = data.info.mapInfo.fullExtent
          mapSetting.basemap = this.props.basemap;
          mapSetting.extent = new Extent(extent.xmin,
            extent.ymin,
            extent.xmax,
            extent.ymax,
            new SpatialReference({
              wkid: extent.spatialReference.wkid
            })
          )
          // setting layers and tables in window object
          // layers and tables are resulted
          // from mapservice definition
          // window.__layers__ = mapLayers;
let layer;
          if (this.props.isConditionMap) {
            layer = find(mapLayers, {
              id: "baseMap"
            });

            layer.url =  this.props.mapUrl;
          } else {
            layer = find(mapLayers, {
              id: "baseMap"
            });

            layer.url =
              this.props.mapUrl || layer.url;
          }
          var {map, view} = createMap(this.props.mapId, mapSetting, mapLayers);
        view.ui._removeComponents(["attribution"]);

            var visible = [];
            window[`${this.props.mapId}`] = map;
            //كروكي الموقع
            if (this.props.isConditionMap) {
              var layerDefs = [];
              visible = [];
              data.info.mapInfo.layers.forEach((layer, index) => {
                if (
                  layer.name === "INVEST_SITE_CORNER" ||
                  layer.name === "INVEST_SITE_BOUNDARY"
                ) {
                  layerDefs[index] =
                    "PARCEL_SPATIAL_ID=" + this.props.siteSpatial;
                  visible.push(index);
                }
              });
              map.allLayers.items.find(i=>i.id === "baseMap").visible = visible;
            //   map.allLayers.items.find(i=>i.id === "baseMap").setLayerDefinitions(layerDefs);
            }
            //الحدود الادارية
            if (this.props.isAdminBordersMap) {
              var layerDefs1 = [];
              visible = [];
              data.info.mapInfo.layers.forEach((layer, index) => { 
              if (
                layer.name !== "MUNICIPALITY_BOUNDARY"
              ) {
                layerDefs1[index] = "1!=1";
              } else {
                visible.push(index);
              }
            })
            map.allLayers.items.find(i=>i.id === "baseMap").visible = visible;
            }
            //المصور الفضائي
            if (this.props.isOnlyfeature) {
              var layerDefs1 = [];
              visible = [];
              data.info.mapInfo.layers.forEach((layer, index) => {
                if (
                  layer.name == "INVEST_SITE_CORNER" ||
                  layer.name == "INVEST_SITE_BOUNDARY"
                ) {
                  layerDefs1[index] = "1!=1";
                } else {
                  visible.push(index);
                }
              });

              map.allLayers.items.find(i=>i.id === "baseMap").visible = visible;
            }
            if(this.props.isParcels){
              var layerDefs1 = [];
              visible = [];
              data.info.mapInfo.layers.forEach((layer, index) => {
                if (
                  layer.name !== "PARCELS"
                ) {
                  layerDefs1[index] = "1!=1";
                } else {
                  //layerDefs1[index] = "1!=1";
                  visible.push(index);
                }
              });

              map.allLayers.items.find(i=>i.id === "baseMap").visible = visible;
            }
            map.view = view;
            view.when(this.props.onMapCreate.bind(this, map))
        }
      );
    
  }

  render() {
    return (
      <>
        <div id={this.props.mapId} 
        style={this.props.style}
        ></div>
      </>
    );
  }
}
export default MapComponent;
