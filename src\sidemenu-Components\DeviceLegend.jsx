import React, { useState } from "react";
import device_icon from "../assets/images/sidemenu/device_icon.svg";
import { IoMdClose } from "react-icons/io";
import { Button, DatePicker } from "antd";

import path_icon from "../assets/images/sidemenu/path.svg";
import history_calls from "../assets/images/sidemenu/history_calls.svg";
import play_icon from "../assets/images/sidemenu/mdi_play.svg";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

export default function DeviceLegend(props) {
  const { t } = useTranslation("layersmenu");
  const [isVisible, setIsVisible] = useState(props.isVisible);

  const navigate = useNavigate();

  const isEmergency = false;
  const isInProgress = false;

  if (!isVisible) return null;

  return (
    <div
      className="legend"
      style={{
        padding: "10px",
        maxHeight: "270px",
        width: "300px",
      }}
    >
      <div
        style={{
          display: "flex",
          gap: "5px",
          alignItems: "center",
          justifyContent: "space-between",
          fontSize: "16px",
        }}
      >
        <img src={device_icon} alt="device icon" />
        <div>{t("device_number")} : 123456</div>
        <IoMdClose
          style={{ cursor: "pointer" }}
          size={24}
          onClick={() => setIsVisible(false)}
          aria-label="Close"
        />
      </div>

      {isEmergency && (
        <div
          style={{ display: "flex", flexDirection: "column", marginTop: "7px" }}
        >
          <div
            style={{
              color: "#EA4335",
              textAlign: "start",
              paddingRight: "20px",
            }}
          >
            {t("emergency_call")}
          </div>
          <Button
            style={{
              borderRadius: "20px",
              padding: "20px",
              backgroundColor: "#EA4335",
              border: "none",
              color: "#fff",
              width: "100%",
            }}
          >
            <img src={play_icon} alt="" />
            {t("run_call")}
          </Button>
        </div>
      )}

      {isInProgress && (
        <div
          style={{ display: "flex", flexDirection: "column", marginTop: "7px" }}
        >
          <div
            style={{
              color: "#34A853",
              textAlign: "start",
              paddingRight: "20px",
            }}
          >
            {t("inprogress_call")}
          </div>
          <Button
            style={{
              borderRadius: "20px",
              padding: "20px",
              backgroundColor: "#34A853",
              border: "none",
              color: "#fff",
              width: "100%",
            }}
          >
            <img src={play_icon} alt="" />
            {t("run_call")}
          </Button>
        </div>
      )}

      <div
        style={{
          marginBlock: "10px",
          display: "flex",
          flexDirection: "row",
          gap: "5px",
        }}
      >
        <DatePicker placeholder={t("from")} />
        <DatePicker placeholder={t("to")} />
      </div>

      <div style={{ display: "flex", gap: "10px", flexDirection: "column" }}>
        <Button
          style={{
            borderRadius: "20px",
            padding: "20px",
            backgroundColor: "#b45333",
            border: "none",
            color: "#fff",
          }}
        >
          <img src={path_icon} alt="" />
          {t("show_path")}
        </Button>
        <Button
          style={{
            borderRadius: "20px",
            padding: "20px",
            backgroundColor: "transparent",
            border: "1px solid #fff",
            color: "#fff",
          }}
          onClick={() => {
            navigate("/wireless-devices");
          }}
        >
          <img src={history_calls} alt="" />
          {t("calls_history")}
        </Button>
      </div>
    </div>
  );
}
