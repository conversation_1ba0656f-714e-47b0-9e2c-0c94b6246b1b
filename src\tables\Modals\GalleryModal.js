import { Modal } from "antd";
import ReactImageGallery from "react-image-gallery"; // Import the play icon from react-icons
import { renderThumbnail, renderVideo } from "../../helper/functions";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function GalleryModal(props) {
  const [updatedGalleryData, setUpdatedGalleryData] = useState(undefined);

  const { t } = useTranslation("common");

  useEffect(() => {
    setUpdatedGalleryData(props.gallery_data);
  }, []);

  return (
    <div>
      {updatedGalleryData && (
        <Modal
          title={t("image_gallery")}
          open={props.openGalleryModal}
          onCancel={() => {
            props.setOpenGalleryModal(false);
          }}
          // width={"70%"}
          width={"700px"}
          maxWidth="100%"
          className="gallery-modal"
        >
          <ReactImageGallery
            items={updatedGalleryData}
            renderItem={renderVideo}
            renderThumbInner={renderThumbnail}
            showPlayButton={false}
          />
        </Modal>
      )}
    </div>
  );
}
