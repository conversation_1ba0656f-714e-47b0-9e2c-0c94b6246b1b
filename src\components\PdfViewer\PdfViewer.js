import React from "react";
import { EyeOutlined, DownloadOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import axios from "axios";
import pdfIconThumbnail from "./adobe-pdf-icon.png";
import { notificationMessage } from "../../helper/utilsFunc";

function PdfViewer(props) {
  const { t } = useTranslation("common");
  const { isUnPlannedLands } = props;
  const handleDowloadViewArchiveFileForUnplannedParcels = (payload, type) => {
    console.log(payload);
    props.showLoading(true);
    let token = localStorage.getItem("token");
    let fileId = payload.fileId
      ? payload.fileId.replaceAll("}", "").replaceAll("{", "")
      : "";
    let requestURL =
      window.ApiUrl +
      `/GetCorrespondenceAttachment?fileId=${fileId}&fileName=${payload.fileName}&fileType=${payload.fileType}`;
    axios
      .get(requestURL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        responseType: "blob",
      })
      .then((res) => {
        if (type === "view") window.open(URL.createObjectURL(res.data));
        else {
          let url = window.URL.createObjectURL(res.data);
          let a = document.createElement("a");
          a.href = url;
          a.download = payload.fileName;
          a.click();
          a.remove();
          setTimeout(() => window.URL.revokeObjectURL(url), 100);
        }
        props.showLoading(false);
      })
      .catch((err) => {
        console.log(err);
        props.showLoading(false);
        notificationMessage("حدث خطأ أثناء استرجاع البيانات", 5);
      });
  };
  const handleDowloadViewArchiveFileInGeneral = (payload, type) => {
    let pathFileUrl =
      window.archiveGalleryPrefixUrl + payload.Path.replaceAll("\\", "/");

    if (type === "view") window.open(pathFileUrl, "blank");
    else {
      props.showLoading(true);

      fetch(pathFileUrl)
        .then((response) => response.blob())
        .then((blob) => {
          props.showLoading(false);

          const url = URL.createObjectURL(new Blob([blob]));
          const link = document.createElement("a");
          link.href = url;
          link.download = payload.Name + "." + payload.Path.split(".")[1];
          document.body.appendChild(link);
          link.click();
          URL.revokeObjectURL(url);
          link.remove();
        })
        .catch((err) => {
          props.showLoading(false);
          let anchorUrl = pathFileUrl;
          let a = document.createElement("a");
          a.href = anchorUrl;
          a.target = "_blank";
          // a.download = payload.fileName;
          a.download = payload.Name + "." + payload.Path.split(".")[1];

          a.click();
          a.remove();
          setTimeout(() => window.URL.revokeObjectURL(pathFileUrl), 100);
        });
    }
  };
  return (
    <>
      <h2 className="text-center">{props.title}</h2>
      <div
        style={{
          display: "flex",
          flexWrap: "wrap",
          height: "30rem",
          overflowY: "scroll",
          columnGap: "2rem",
          justifyContent: "center",
        }}
      >
        {props?.data?.length
          ? props?.data?.map((item, idx) => (
              <div
                key={"gArch" + idx}
                style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "start",
                  margin: "0 3rem",
                }}
              >
                <div class="ant-image css-nnuwmp" style={{ width: "100px" }}>
                  <img
                    class="ant-image-img css-nnuwmp"
                    alt="thumbnail"
                    src={
                      isUnPlannedLands
                        ? pdfIconThumbnail
                        : (!isUnPlannedLands &&
                            item.Path.replaceAll("\\", "/")?.split(
                              "."
                            )?.[1]) === "pdf"
                        ? pdfIconThumbnail
                        : window.archiveGalleryPrefixUrl +
                          item.Path.replaceAll("\\", "/")
                    }
                    width="100"
                  />
                  <div class="ant-image-mask">
                    <div
                      class="ant-image-mask-info"
                      onClick={() => {
                        if (isUnPlannedLands)
                          handleDowloadViewArchiveFileForUnplannedParcels(
                            item,
                            "view"
                          );
                        else
                          handleDowloadViewArchiveFileInGeneral(item, "view");
                      }}
                      style={{ display: "flex", flexDirection: "column" }}
                    >
                      <span
                        role="img"
                        aria-label="eye"
                        title={t("view")}
                        class="anticon anticon-eye"
                      >
                        <EyeOutlined />
                      </span>
                      {/* {t('view')} */}
                    </div>
                    <div
                      class="ant-image-mask-info"
                      onClick={() => {
                        if (isUnPlannedLands)
                          handleDowloadViewArchiveFileForUnplannedParcels(
                            item,
                            "download"
                          );
                        else
                          handleDowloadViewArchiveFileInGeneral(
                            item,
                            "download"
                          );
                      }}
                      style={{ display: "flex", flexDirection: "column" }}
                    >
                      <span
                        role="img"
                        aria-label="eye"
                        title={t("download")}
                        class="anticon anticon-eye"
                      >
                        <DownloadOutlined />
                      </span>
                      {/* {t('download')} */}
                    </div>
                  </div>
                </div>
                <span
                  className="text-center"
                  style={{ width: "100px", textWrap: "wrap" }}
                >
                  {item.fileName || item.Name}
                </span>
              </div>
            ))
          : t("noImageAvailable")}
      </div>
    </>
  );
}

export default PdfViewer;
