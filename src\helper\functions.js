import { FaPlay } from "react-icons/fa";
import ReactPlayer from "react-player";

export function renderVideo(item) {
  if (item.isVideo) {
    return (
      <div className="video-wrapper">
        {/* <FaPlay className="thumbnail-play-icon" /> */}
        <ReactPlayer
          playing
          controls={true}
          url={item.original}
          width={"100%"}
          height={"100%"}
          muted={true}
        />
      </div>
    );
  }

  return <img src={item.original} alt={item.originalAlt} width={"100%"} />;
}

export function renderThumbnail(item) {
  return (
    <div className="image-gallery-thumbnail-inner">
      {item.isVideo ? (
        <>
          <FaPlay className="thumbnail-play-icon" />
          <video
            className="image-gallery-thumbnail-image"
            style={{ height: "55px" }}
            preload
          >
            <source src={item.original + "#t=0.001"} type="video/mp4" />
          </video>
        </>
      ) : (
        <img
          src={item.thumbnail}
          alt={item.originalAlt}
          className="image-gallery-thumbnail-image"
        />
      )}
    </div>
  );
}
