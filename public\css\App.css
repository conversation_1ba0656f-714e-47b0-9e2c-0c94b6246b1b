/*general style*/
@import "https://js.arcgis.com/4.21/@arcgis/core/assets/esri/themes/light/main.css";

* {
  box-sizing: border-box !important;
}

@font-face {
  font-family: "NeoSansArabic";
  src: url(./ArbFONTS-NotoKufiArabic-Regular.ttf);
}

@font-face {
  font-family: "FrutigerLTArabic45Light";
  src: url(./FrutigerLTArabic55Roman_en.ttf);
}

@font-face {
  font-family: arabicNums;
  src: url(./arabicNumber.ttf);
}

* {
  font-family: FrutigerLTArabic45Light !important;
}

/* p {
  font-family: "NeoSansArabic";
} */

[aria-checked="true"] .ant-switch-inner {
  background-color: #b45333 !important;
}

[aria-checked="false"] .ant-switch-inner {
  background-color: #ddd !important;
}

.ant-select-item-option-state {
  color: #fff !important;
}

.table td,
.table th {
  border-top: none !important;
  color: #fff;
}

/* @media print {
  body {
    -webkit-print-color-adjust: exact;
  }
} */

/* span {
  font-family: arabicNums;
} */

label {
  /* font-family: "NeoSansArabic"; */
  color: #fff !important;
}

input {
  cursor: pointer !important;
}

input::placeholder {
  color: rgb(255 255 255 / 70%) !important;
}

.MuiTooltip-tooltip,
.ant-tooltip-inner {
  background-color: #b45333 !important;
  border-radius: 20px !important;
}

.ant-tooltip-arrow {
  display: none !important;
}

.ant-tooltip {
  text-wrap: wrap;
}

/* html {
  overflow: hidden;
} */

/* .ant-select {
  border-radius: 30px;
  overflow: hidden;
  border: 1px solid #909191;
} */

.loading {
  background: rgba(240, 243, 244, 0.4);
  position: fixed;
  z-index: 9999999 !important;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.loading img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
}

.ant-table-cell {
  color: #fff !important;
}

#basemapGallery {
  background-color: transparent !important;
}

#basemapGallery ul {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  padding-inline-end: 7px;
}

#basemapGallery ul li.esri-basemap-gallery__item--selected {
  /* border-left-color: transparent; */
  border: none !important;
  background-color: #fff1ec;
}

.esri-basemap-gallery__item--selected {
  background-color: #fff1ec !important;
}

.esri-basemap-gallery__item--selected div {
  color: #b45333 !important;
}

#basemapGallery ul li.esri-basemap-gallery__item--selected div {
  color: #b45333 !important;
}

#basemapGallery ul li div {
  text-wrap: wrap;
  color: #fff !important;
  font-size: 10px !important;
  padding: 0 !important;
}

#basemapGallery ul li img {
  width: 100%;
  border-radius: 8px;
}

.esri-basemap-gallery__item {
  border: none !important;
}

.esri-basemap-gallery__item:hover,
.esri-basemap-gallery__item:focus {
  border: none !important;
  background-color: #ffffff4d;
}

.dec_search_table th,
.dec_search_table td {
  border-bottom: 0 !important;
  border-left: 1.5px solid #eee7e1 !important;
  margin: 2px;
}

.dec_search_table th {
  background-color: #eee7e1;
}

.ant-select-item ant-select-item-option {
  border-radius: 30px !important;
}

.ant-select-item-option {
  padding: 12px !important;
  font-size: 16px !important;
  margin: 3px;
  border-radius: 30px !important;
  color: #fff !important;
}

.ant-select-item-option:hover,
.ant-select-item-option-selected {
  background-color: #b45333 !important;
  color: #fff !important;
}

.ant-form-rtl
  .ant-form-item.ant-form-item-has-success
  .ant-form-item-children-icon,
.ant-form-rtl
  .ant-form-item.ant-form-item-has-warning
  .ant-form-item-children-icon,
.ant-form-rtl
  .ant-form-item.ant-form-item-has-error
  .ant-form-item-children-icon,
.ant-form-rtl
  .ant-form-item.ant-form-item-is-validating
  .ant-form-item-children-icon {
  left: 20px !important;
  top: 18px !important;
}

.ant-form-rtl .ant-form-item > .ant-select .ant-select-arrow,
.ant-form-rtl .ant-form-item > .ant-select .ant-select-clear,
.ant-form-rtl
  .ant-form-item
  :not(.ant-input-group-addon)
  > .ant-select
  .ant-select-arrow,
.ant-form-rtl
  .ant-form-item
  :not(.ant-input-number-group-addon)
  > .ant-select
  .ant-select-arrow {
  left: 9px !important;
  transform: translateY(-50%);
}

.ant-form-rtl
  .ant-form-item
  :not(.ant-input-group-addon)
  > .ant-select
  .ant-select-clear,
.ant-form-rtl
  .ant-form-item
  :not(.ant-input-number-group-addon)
  > .ant-select
  .ant-select-clear {
  left: 35px !important;
}

.ant-select-arrow,
.ant-select-clear svg {
  font-size: 17px !important;
  fill: #ccc;
}

.ant-select-arrow svg {
  /* color: #b45333 !important; */
  color: #fff !important;
}

.ant-select-clear {
  /* left: 15px !important; */
  left: 40px !important;
  margin-top: -8px !important;
  background-color: transparent !important;
}

.ant-select-focused:not(.ant-select-disabled).ant-select:not(
    .ant-select-customize-input
  )
  .ant-select-selector,
.ant-select-selector:hover {
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
}

.ant-select-selector .anticon-close-circle svg {
  font-size: 20px !important;
}

.ant-select-selector svg {
  font-size: 16px !important;
}

@keyframes fadeInLeft {
  0% {
    transform: translateX(500px);
  }

  100% {
    transform: translateX(0);
  }
}

button:focus {
  outline: none !important;
}

.esri-basemap-gallery__item:first-child {
  margin-top: 0 !important;
}

/* .ant-form-item-control {
  animation: fadeInLeft 0.7s ease-in alternate;
} */

.image-gallery-slides {
  height: 300px;
}

.image-gallery-slides > div {
  height: 100%;
}

.image-gallery .image-gallery-slides img,
.image-gallery .image-gallery-slides iframe {
  height: 300px !important;
  object-fit: cover !important;
  border-radius: 10px;
}

.leftToolMenu .image-gallery-slides {
  height: 150px;
}

.leftToolMenu .image-gallery .image-gallery-slides img,
.leftToolMenu .image-gallery .image-gallery-slides video {
  height: 150px !important;
}

.image-gallery-content.fullscreen .image-gallery-slides img,
.image-gallery-content.fullscreen .image-gallery-slides video,
.image-gallery-content.fullscreen .video-wrapper,
.leftToolMenu .image-gallery-content.fullscreen .image-gallery-slides {
  height: 90vh !important;
}

.image-gallery-content.fullscreen .video-wrapper svg {
  display: none;
}

.image-gallery-content.fullscreen .image-gallery-slides,
.image-gallery-content.fullscreen .image-gallery-swipe {
  height: 100%;
}

.image-gallery-thumbnail img {
  height: 55px;
  border-radius: 10px;
}

.ant-upload-wrapper {
  display: flex;
  flex-direction: column;
}

.image-gallery-thumbnail.active {
  border-radius: 10px;
}

.thumbnail-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  z-index: 2;
  stroke: black;
  stroke-width: 10px;
}

.leftToolMenu .image-gallery-thumbnails-wrapper {
  display: none;
}

.leftToolMenu
  .image-gallery-content.fullscreen
  .image-gallery-thumbnails-wrapper {
  display: block;
}

/* td, */
th {
  /* font-family: "NeoSansArabic" !important; */
}

.copy-right {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
}

.site-controls {
  position: absolute;
  bottom: 50px;
  right: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  font-size: 20px;
  color: #fff;
}

.site-controls svg {
  cursor: pointer;
  transform: rotateY(180deg);
}

::-webkit-scrollbar {
  width: 10px;
  height: 7px;
  -webkit-appearance: none;
}

::-webkit-scrollbar-thumb {
  background-color: #b45333 !important;
  /* color of the scroll thumb */
  border-radius: 5px;
  /* roundness of the scroll thumb */
  border: 1px solid #b45333;
  /* creates padding around scroll thumb */
}

.ant-table-wrapper .ant-checkbox-checked .ant-checkbox-inner,
.ant-checkbox-checked .ant-checkbox-inner:hover {
  background-color: #b45333 !important;
  border: solid 2px #b45333 !important;
}

.ant-checkbox:focus,
.ant-checkbox:hover {
  border-color: #b45333 !important;
}

:where(.css-dev-only-do-not-override-kghr11).ant-steps
  .ant-steps-item-process
  .ant-steps-item-icon {
  background-color: #b45333 !important;
  border-color: #b45333 !important;
}

.checkDiv {
  /* font-family: "NeoSansArabic" !important; */
  font-size: 15px;
  font-weight: bold;
  text-align: right;
}

.ant-checkbox-input:checked + .ant-checkbox-inner {
  background-color: #b45333 !important;
}

.ant-checkbox-inner {
  width: 20px !important;
  height: 20px !important;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner,
.ant-checkbox-input + .ant-checkbox-inner {
  border-color: #b45333 !important;
}

.ant-checkbox-checked .ant-checkbox-inner::after {
  border-color: #fff !important;
}

.study-actor .ant-input.search {
  padding-inline-start: 45px !important;
}

.ant-select-selector,
/* input  */
input:not(.ant-select-selector input) {
  padding: 5px 15px !important;
  /* height: 40px !important; */
  border-radius: 30px !important;
  border: solid 1px #d4d6de;
  background-color: #ffffff;
  /* font-family: "NeoSansArabic" !important; */
  font-size: 14px;
  line-height: 1.36;
  width: 100%;
  color: #000;
  margin: auto;
  font-size: 0.8rem;
  /* padding-right: 2px !important; */
  background-color: transparent !important;
}

.ant-input {
  height: 39.6px;
  color: #fff !important;
}

input {
  padding-right: 11px !important;
}

.ant-select-selector input {
  padding-right: 2px !important;
  cursor: pointer !important;
}

.ant-select-selector {
  cursor: pointer !important;
}

.ant-select-selection-placeholder {
  text-align: right !important;
  color: rgb(255 255 255 / 70%) !important;
}

.ant-select-selection-item {
  color: #fff !important;
}

.ant-select-selection-placeholder,
.ant-select-selection-item {
  /* padding-right: 13px !important; */
  padding-right: 2px !important;
}

.ant-select-selector:hover,
input:hover {
  /* border-color: #b45333 !important; */
  border-color: #fff !important;
}

.ant-form-item-label > label::before {
  display: none !important;
}

.ant-form-item-explain-error {
  text-align: right;
  padding: 5px 0;
  /* font-family: "NeoSansArabic" !important; */
}

.ant-select {
  width: 100% !important;
  margin-block: 15px;
  border-radius: 30px !important;
}

.ant-select-dropdown,
.ant-input,
.ant-select-selection-item,
.ant-select-selection-search-input {
  text-align: right !important;
  direction: rtl !important;
  /* font-family: "NeoSansArabic" !important; */
  color: #fff !important;
}

.ant-select-arrow {
  margin-top: 0 !important;
  transform: translateY(-50%);
}

.ant-form-item-label {
  /* font-family: "NeoSansArabic" !important; */
  font-size: 16px;
  line-height: 1.63;
  text-align: right;
  /* font-weight: 600; */
  font-weight: 400;
  color: #382f2d;
}

/*SideMenu Styles*/
.SideMenu {
  overflow-y: hidden !important;
  z-index: 999;
  position: absolute;
  bottom: 0;
  inset-inline-end: 44px;
}

.SideMenu .MuiPaper-root {
  /* padding: 10px; */
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  overflow: visible !important;
  width: fit-content;
  /* background-image: url("../bg_image.png"); */
  /* background-image: url("../content_bg.png"); */
  /* background-repeat: no-repeat; */
  /* background-size: cover; */
  background-color: rgb(51 51 51 / 60%) !important;
  z-index: unset;
  font-size: 0.9rem;
  backdrop-filter: blur(4px);
}

.css-1r9jet7 {
  padding: 0 !important;
}

.coordinates .ant-form-item {
  margin-bottom: 10px !important;
}

.sidemenu-home .MuiPaper-root {
  /* background-image: url("../bg_image.png"); */
  background-color: rgb(51 51 51 / 60%) !important;
}

.closed-sidemenu .MuiPaper-root {
  background-image: none;
  /* width: 40px !important  ; */
}

.SideMenu.map-tools-menu .MuiPaper-root {
  left: 0;
  right: auto;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

.SideMenu .MuiList-root {
  /* overflow-y: auto !important;
  overflow-x: hidden !important; */
  padding: 10px;
  /* overflow: auto; */
  height: 100vh;
  direction: ltr;
  color: #fff;
}

div.MuiList-root {
  direction: rtl !important;
}

.sideMenuFooter {
  text-align: center;
  white-space: pre-wrap !important;
  /* font-family: "NeoSansArabic"; */
  font-size: 12px;
  background-color: #57565626;
  color: #fff;
  border-radius: 10px;
  padding: 10px;
  margin: 0 !important;
  margin-top: 5px !important;
  /* width: 190px; */
  background-color: rgb(87 86 86 / 60%);
  display: none;
}

.sideMenuTitle {
  text-align: right;
  /* position: absolute;
  right: 0 !important; */
  white-space: break-spaces;
  /* font-family: "NeoSansArabic"; */
  font-size: 14px;
  padding-right: 5px;
  padding-top: 10px;
  margin: auto;
}

.TitleEdition {
  text-align: right;
  /* font-family: "NeoSansArabic"; */
  font-size: 10px;
  padding-right: 5px;
  margin: auto;
}

.SideMenu .css-9mgopn-MuiDivider-root {
  /* border-color: #25b6bd; */
  display: none;
}

.css-11o2879-MuiDrawer-docked .MuiDrawer-paper,
.css-uqx0qm-MuiDrawer-docked .MuiDrawer-paper {
  box-shadow: -6px 0px 4px 0 rgb(0 0 0 / 32%), -2px -2px 2px 0 rgb(0 0 0 / 20%);
}

.css-1u2mxbp {
  min-height: 60px !important;
}

.SideMenu .MuiListItem-button {
  text-align: right !important;
}

.SideMenu .MuiTypography-root {
  text-decoration: none !important;
  color: #707070 !important;
  /* font-family: "NeoSansArabic"; */
  text-decoration-line: none !important;
  font-weight: 100 !important;
  text-align: center;
  font-size: 0.7rem;
}

.changeLanguageAREN {
  color: #707070;
  /* font-family: "NeoSansArabic"; */
}

.MuiListItem-root:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
  background-color: transparent !important;
}

.mainPage {
  height: 100vh;
  overflow: hidden;
}

.mainPage .css-k008qs {
  direction: rtl !important;
}

.mainPage .css-1191obr-MuiPaper-root-MuiAppBar-root {
  margin-left: unset !important;
  margin-right: 240px !important;
}

.languageIcon {
  position: absolute;
}

.fullScreenIcon svg,
.homeIcon svg,
.languageIcon {
  color: #117074 !important;
}

.sideLinkDiv {
  /* text-align: right; */
  vertical-align: middle;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  box-shadow: 0 0 1px transparent;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 5px;
  margin-block-start: 2px;
  transition: 0.5s;
  direction: rtl;
}

.sideLinkDiv svg {
  color: #fff !important;
}

.sideLinkDiv:hover {
  background-color: #b45333;
}

.sideLinkDiv:hover i {
  color: #fff;
}

.sideLinkDiv .MuiButtonBase-root {
  height: 100% !important;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
  padding: 8px 16px !important;
}

.css-tlelie-MuiListItemText-root {
  margin: 0 !important;
}

.sideLinkDiv .MuiButtonBase-root img {
  padding-top: 0;
}

.sideLinkDiv .MuiButtonBase-root .MuiTypography-root {
  text-align: start !important;
  color: #fff !important;
}

.sideLinkDiv:hover .MuiButtonBase-root .MuiTypography-root {
  color: #fff !important;
}

.sideLinkDiv
  img:not(
    .image-gallery-slides img,
    .image-gallery-thumbnail .image-gallery-thumbnail-image,
    #basemapGallery ul li img,
    .layer-img
  ) {
  /* filter: invert(39%) sepia(0%) saturate(30%) hue-rotate(136deg) brightness(0%)
    contrast(74%) !important; */
  width: 20px;
  filter: brightness(0) invert(1) !important;
}

.sideLinkDiv:hover
  img:not(
    .inquiry-img,
    .base_img,
    .layer-img,
    .image-gallery-slides img,
    .image-gallery-thumbnail .image-gallery-thumbnail-image,
    #basemapGallery ul li img
  ) {
  /* filter: invert(39%) sepia(0%) saturate(30%) hue-rotate(136deg) brightness(0%)
    contrast(74%) !important; */
  filter: brightness(0) invert(1) !important;
}

/* .sideLinkDiv:hover img {
  filter: unset !important;
}

.sideLinkDiv:hover .marsadImg {
  filter: invert(100%) sepia(2%) saturate(11%) hue-rotate(105deg)
    brightness(105%) contrast(100%) !important;
}

.sideLinkDiv:hover .changeLanguageAREN {
  color: #fff;
} */

@keyframes hoverAnimation {
  16.65% {
    -webkit-transform: translateY(8px);
    transform: translateY(8px);
  }

  33.3% {
    -webkit-transform: translateY(-6px);
    transform: translateY(-6px);
  }

  49.95% {
    -webkit-transform: translateY(4px);
    transform: translateY(4px);
  }

  66.6% {
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
  }

  83.25% {
    -webkit-transform: translateY(1px);
    transform: translateY(1px);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

/* .sideLinkDiv::before {
  width: 100%;
  height: 100%;
  content: "";
  margin: auto;
  position: absolute;
  left: -100%;
  background: #b45333;
  transition: all 0.5s;
  z-index: -1;
  border-radius: 0 20px 20px 0;
  margin-right: 10px;
  overflow: hidden !important;
}

.sideLinkDiv:hover:before {
  top: 0;
  left: 0;
  overflow: hidden !important;
}

.sideLinkDiv:hover .MuiTypography-root {
  color: #fff !important;
} */

/* 
.fullScreenIcon svg,
.homeIcon svg {
  font-size: 25px !important;
} */
.languageIcon button {
  font-size: 18px;
  height: 35px;
  width: 35px;
  font-weight: bold;
}

.fullScreenIcon button,
.homeIcon button,
.languageIcon button {
  padding: 5px !important;
  background: #fff !important;
  top: 10px !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  cursor: pointer;
}

.fullScreenIcon button:hover,
.homeIcon button:hover,
.languageIcon button:hover {
  color: #fff !important;
  background-color: #117074 !important;
}

.fullScreenIcon button:hover svg,
.homeIcon button:hover svg {
  color: #fff !important;
  background-color: #117074 !important;
}

.css-zxdg2z {
  padding: unset !important;
}

/*Map*/

#mapDiv {
  padding: 0;
  margin: 0;
  height: 100%;
  width: 100%;
}

#mapOverviewDiv {
  padding: 0;
  margin: 0;
  width: 100%;
}

.mapOverviewDrag {
  cursor: move;
  z-index: 10;
  border: 1px solid #9d4223;
  background-color: #9d4223;
  display: block;
  opacity: 0.5;
  left: 35%;
  top: 45%;
  position: absolute;
  width: 110px;
  height: 70px;
  border-radius: 5px;
}

.mapOuterSearch {
  position: absolute;
  top: 10px;
}

.outerSearchIcon {
  color: #fff;
  background-color: #b45333;
  padding: 8px;
  border-radius: 50px;
  width: 40px;
  text-align: center;
  top: 12px;
}

.outerSearchInput input {
  height: 25px !important;
}

.outerSearchInput {
  border-radius: 10px !important;
  margin-bottom: 5px !important;
}

.mapOuterSearch .outerSearchInput {
  width: fit-content;
}

.outerSearchInput .ant-input-affix-wrapper-focused {
  box-shadow: none !important;
  border-color: #d4d6de !important;
}

.outerSearchForm button {
  background-color: #b45333 !important;
  border-radius: 10px !important;
  color: #fff;
  left: 0;
  height: 40px;
  /* font-family: "NeoSansArabic"; */
  margin-bottom: 10px;
}

.outerSearchForm button:hover {
  color: #b45333;
  background-color: transparent !important;
  border: 1px solid #b45333;
}

.ant-input-affix-wrapper {
  background-color: rgba(51, 51, 51, 0.6) !important;
  border-color: #fff !important;
}

.outerSearchInput .ant-input-affix-wrapper {
  border-radius: 50px !important;
  cursor: pointer;
  background-color: rgb(87 86 86 / 70%) !important;
  /* border: none; */
}

.mapOuterSearch .outerSearchInput .ant-input-affix-wrapper input {
  width: 0 !important;
  padding: 0 !important;
}

.mapOuterSearch .outerSearchInput.activeOuterSearchInput input {
  width: 200px !important;
  padding-inline-start: 11px !important;
  height: 25px !important;
}

.mapOuterSearch .outerSearchInput .ant-input-affix-wrapper span:first-of-type {
  margin: 0 !important;
}

.mapOuterSearch .outerSearchInput .ant-input-affix-wrapper span:last-of-type {
  display: none !important;
}

.mapOuterSearch
  .activeOuterSearchInput
  .ant-input-affix-wrapper
  span:last-of-type {
  display: inline !important;
}

.outerSearchInput .ant-input-affix-wrapper:hover {
  border-color: #b45333;
}

.mapOuterSearch .outerSearchInput .ant-input-affix-wrapper:hover input {
  width: 200px !important;
  padding-inline-start: 11px !important;
}

.mapOuterSearch
  .outerSearchInput
  .ant-input-affix-wrapper:hover
  span:last-of-type {
  display: inline !important;
}

.mapOuterSearch .anticon svg {
  margin-top: -5px !important;
}

.outerSearchForm {
  position: absolute;
  padding: 10px 10px 2px 10px !important;
  border-radius: 7px;
  width: 300px;
  /* right: 20px; */
  /* right: -80px; */
  right: 0;
}

.outerSearchAutoComplete {
  text-align: right;
  padding-right: 10px;
  padding-top: 10px;
  height: 200px;
  overflow: auto;
  background-color: #57565680 !important;
  /* background-color: #fff !important; */
  /* font-family: "NeoSansArabic"; */
}

.outerSearchAutoComplete label {
  cursor: pointer;
}

.outerSearchAutoComplete div:hover {
  font-weight: bold;
  color: #117074;
}

.leftIconMenu {
  position: absolute;
  top: 8px;
  left: 12px;
  right: unset;
  cursor: pointer;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  /* background-color: #33333366; */
  background-color: rgb(51 51 51 / 60%) !important;
  padding: 5px;
  width: 36px;
  height: 36px;
  border-radius: 10px;
  text-align: center;
  z-index: 99 !important;
}

.leftIconMenu img {
  width: 24px;
  height: 24px;
  /* filter: invert(30%) sepia(72%) saturate(7467%) hue-rotate(169deg)
    brightness(90%) contrast(97%) !important; */
}

.mapTools {
  position: absolute;
  bottom: 2%;
  z-index: 1;
}

.mapTools ul {
  direction: ltr;
  padding-right: 15px !important;
}

.mapTools ul,
.painting ul {
  list-style-type: none !important;
}

.mapTools li {
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  padding: 5px 10px 5px 10px;
  color: #b45333 !important;
  cursor: pointer;
  margin-bottom: 15px;
  text-align: center;
  border-radius: 50px;
}

.mapTools li:hover {
  background: #b45333 !important;
  color: #fff !important;
}

.merge {
  background-image: url("./mergeBG.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/*SideMenu Sections*/

/*Coordinate*/

/* .coordinates .nav-tabs {
  padding: 0 !important;
} */

/* .coordinates  */
.ant-select {
  /* width: 400px !important; */
  height: auto !important;
  margin: 0 !important;
  /* padding: 0 7px !important; */
}

.MuiSlider-root {
  margin-inline: 12px !important;
}

.coordinates .nav-item {
  /* padding: 5px 25px !important; */
  flex: 1;
}

.coordinates .nav-item:has(.active) {
  background-color: #b45333 !important;
  color: #fff !important;
}

.coordinates .nav-item button {
  width: 100%;
}

.coordinates h3,
.SideMenu h3 {
  /* font-family: "NeoSansArabic"; */
  font-size: 18px;
  line-height: 1.93;
  text-align: center;
  /* color: #b45333; */
  color: #fff;
}

/* .SideMenu h3 {
  position: relative;
  right: righ;
  right: 50%;
  top: 10px;
} */

.coordinates .nav-tabs {
  border: 1.5px solid #dddd;
  border-radius: 30px;
  overflow: hidden;
  padding: 0 !important;
  /* border: none !important; */
  /* background-color: rgba(231, 233, 241, 0.59); */
  direction: rtl;
  margin-block: 8px !important;
}

.coordinates .nav-tabs .nav-link,
.coordinates .nav-tabs .nav-link:focus {
  border: none !important;
  background: transparent !important;
  outline: none !important;
  font-size: 14px;
}

.coordinates .nav-link.active {
  /* background-color: #b45333 !important; */
  color: #fff !important;
  font-weight: bold;
  /* border-radius: 30px; */
}

.coordinates .nav-link {
  /* font-family: "NeoSansArabic"; */
  font-size: 15px;

  letter-spacing: normal;
  color: #fff !important;
  margin-right: auto;
}

/* .coordinates .nav-tabs {
  border: none !important;
  background-color: rgba(231, 233, 241, 0.59);
  direction: rtl;
} */

.backBtn h3 {
  margin: auto;
}

#SearchSidemenu .css-1u2mxbp {
  /* display: -webkit-box !important; */
  display: flex;
  flex-direction: column;
}

#h3SideSearch {
  margin: unset !important;
  margin-left: auto !important;
  /* padding-right: 35px; */
}

.bookmarkDiv svg {
  color: #b45333;
}

.backBar {
  margin-left: auto;
}

.backBar svg {
  /* transform: rotate(90deg); */
  color: #25b6bd !important;
}

.backBtn svg {
  color: #b45333 !important;
}

.legend {
  position: absolute;
  bottom: 55px;
  left: 70px;
  background-color: #57565680;
  padding-inline: 10px;
  border-radius: 10px;
  width: 260px;
  max-height: 220px;
  overflow: auto;
  color: #fff;
  text-wrap: wrap;
  text-align: center;
  direction: rtl;
  font-size: 0.9rem;
  backdrop-filter: blur(4px);
}

.layersMenuPage {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow: auto;
  height: calc(100vh - 64px);
  color: #fff;
}

.sketch-picker {
  position: relative;
  z-index: 100;
  width: 250px !important;
}

.study-actor .sketch-picker {
  top: -290px !important;
}

.study-actor .generalSearchCard {
  margin: 0 !important;
}
.sketch-picker label {
  color: rgb(34, 34, 34) !important;
}

.sketch-picker input {
  border-radius: 0 !important;
  height: auto !important;
  width: 100% !important;
}

.layersMenuPage .box,
.interactiveMap .box,
.import_file .box,
.study-actor .box {
  /* box-shadow: 0.5px 0.5px 3px #b45333; */
  border: 1px solid #fff;
  border-radius: 20px;
  padding: 8px;
}

/* Ensure study-actor container doesn't clip dropdowns */
.study-actor {
  position: relative;
}

.study-actor .ant-select {
  position: relative;
  /* z-index: 1; */
}

.interactiveMap .box .images .image,
.topBar .images .image,
.study-actor .box .images .image {
  border: 1px solid #fff;
  padding: 5px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36.6px;
  height: 36.6px;
  position: relative;
}

.topBar .images .image .badge {
  position: absolute;
  top: -8px;
  right: 25px;
  background-color: #b45333;
  color: #fff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.interactiveMap .box .images .image img,
.topBar .images .image img {
  width: 25px;
  height: 25px;
}

.layersMenuPage button,
.interactiveMap button:not(.SearchBtn),
.study-actor button:not(.SearchBtn) {
  /* width: 100% !important; */
  background-color: #fff !important;
  color: #b45333 !important;
  border-radius: 15px;
  font-size: 12px;
  border-color: #fff !important;
  box-shadow: none !important;
}

.layersMenuPage .card {
  border-radius: 10px;
  /* box-shadow: 0.5px 0.5px 1px #b45333; */
  border: 1px solid #fff;
  padding: 8px;
  text-wrap: wrap;
  text-align: center;
  cursor: pointer;
  font-size: 10px;
  background-color: transparent;
}

.layersMenuPage .card img {
  width: 35px;
  height: 35px;
  margin-inline: auto;
  display: block;
}

.layersMenuPage .card.active {
  background-color: #b45333;
  color: #fff;
  border-color: #b45333 !important;
}

.layersMenuPage .card.active img {
  filter: brightness(0) invert(1) !important;
}

.coordinateForm h5,
.generalSearchCard h5,
.generalSearchCardWithoutHover h5 {
  /* font-family: "NeoSansArabic"; */
  font-size: 14px;
  line-height: 1.94;
  text-align: right;
  margin-bottom: 0 !important;
  color: #fff;
  text-wrap: wrap;
}

.coordinateForm input {
  height: 40px;
  text-align: right;
  border-radius: 5px;
}

.coordinates {
  /* overflow-y: scroll !important; */
  /* height: 100%; */
  overflow-x: hidden !important;
  /* height: 530px; */
  height: calc(100vh - 150px) !important;
}

.add-bookmark.coordinates {
  height: calc(100vh - 90px) !important;
}

/*Measurement*/
.spaceMeasureBtn,
.distanceMeasureBtn,
.CoordinateMeasureBtn,
.paintBtn {
  border: none !important;
  box-shadow: none !important;
  padding: 5px;
  width: 30px;
  height: 30px;
  background-color: #eee7e1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.paintBtn span {
  display: none;
}

/* .spaceMeasureBtn:hover,
.distanceMeasureBtn:hover,
.CoordinateMeasureBtn:hover {
  background-color: #d4d6de6e !important;
} */

.MuiTooltip-tooltip {
  font-size: 13px !important;
  /* font-family: "NeoSansArabic" !important; */
  text-align: right !important;
}

#activeSpaceBtn,
#activeDistanceBtn,
#activeCooBtn,
#activePaintBtn {
  background-color: #f4dfd9 !important;
  width: fit-content;
  color: #b45333 !important;
  border-radius: 20px;
}

#activeSpaceBtn span,
#activeDistanceBtn span,
#activeCooBtn span,
#activePaintBtn span {
  display: inline;
}

.measurePage h6 {
  /* font-family: "NeoSansArabic"; */
  font-size: 15px;
  line-height: 1.93;
  text-align: center;
  font-weight: bold;
  color: #25b6bd;
  padding-bottom: 3px;
  border-bottom: 1px solid #25b6bd;
  margin: auto;
  width: 50%;
}

.measurePage > button {
  width: 40px;
  height: 40px;
  margin-inline: 5px;
  border-radius: 20px;
  background-color: #eee7e1 !important;
}

.measurePage button span,
.paintBtn span {
  display: none;
}

.measurePage p {
  text-align: center;
  color: #000;
  /* font-family: "NeoSansArabic"; */
  white-space: normal;
  text-align: right;
  font-size: 0.8rem;
}

.esri-button:hover,
.esri-elevation-profile__header button:hover {
  background-color: #b45333;
  border-color: #b45333 !important;
}

.unitSpan {
  font-weight: bold;
}

.measurePage th {
  text-align: center;
  /* color: #25b6bd; */
  font-weight: bold;
  /* font-family: "NeoSansArabic" !important; */
}

.measurePage td {
  text-align: center;
  color: #fff;
  /* font-family: "NeoSansArabic" !important; */
}

.measurePage th {
  color: #fff;
}

.measurePage h5 {
  /* font-family: "NeoSansArabic"; */
  line-height: 1.93;
  font-size: 13px;
  text-align: right;
  font-weight: bold;
  color: #25b6bd;
  padding-top: 10px;
  border-top: 1px solid #25b6bd;
  white-space: break-spaces !important;
}

/*Painting*/

.painting {
  height: calc(100vh - 80px);
  overflow: auto;
}

.painting ul {
  padding: unset !important;
}

.painting li {
  padding: 5px 10px 5px 10px;
  color: #fff;
  cursor: pointer;
  margin-bottom: 5px;
  /* margin-bottom: 15px; */
  text-align: right;
  border-radius: 3px;
  /* font-family: "NeoSansArabic"; */
  width: 100%;
  padding: 10px 5px !important;
}

.painting .ant-form-item {
  margin-bottom: 5px !important;
}

.painting li {
  border-radius: 10px;
  padding: 10px !important;
  display: flex;
  align-items: center;
  gap: 7px;
}

.painting li img {
  filter: brightness(0) saturate(100%) invert(32%) sepia(98%) saturate(411%)
    hue-rotate(329deg) brightness(99%) contrast(92%);
}

.painting li svg {
  color: #b45333 !important;
}

.painting li:hover {
  background-color: #b45333;
  color: #fff;
}

.painting li:hover img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(14%) saturate(3%)
    hue-rotate(160deg) brightness(102%) contrast(101%);
}

.painting li:hover svg {
  color: #fff !important;
}

.painting svg {
  color: #fff;
  font-size: 20px;
}

.btnsGroup {
  padding: 5px;
  margin: auto;
}

.btnsGroup button,
.btnsGroup button:focus,
.btnsGroup button:active {
  border-radius: 4px;
  margin: 5px;
  box-shadow: none !important;
  background-color: #0b2548 !important;
  /* font-family: "NeoSansArabic"; */
  font-size: 16px;
  text-align: center;
  outline: none !important;
  border: none !important;
  color: #fffefb !important;
}

.btnsGroup button:hover {
  background-color: #fffefb !important;
  border: 1px solid #4e4f50 !important;
  color: #4e4f50 !important;
}

.addMark {
  border-radius: 4px;
  margin: 5px;
  box-shadow: none !important;
  background-color: #d4d6de !important;
  /* font-family: "NeoSansArabic"; */
  font-size: 16px;
  text-align: center;
  outline: none !important;
  border: none !important;
  color: #b45333 !important;
}

.addMark:hover {
  background-color: #b45333 !important;
  /* border: 1px solid #4e4f50 !important; */
  color: #fff !important;
}

.bookmarkDiv {
  padding: 10px;
  margin: 10px 5px;
  background-color: #f5f5f5;
  cursor: pointer;
}

.bookmarkDiv p {
  /* font-family: "NeoSansArabic"; */
  font-size: 16px;
  text-align: center;
}

.bookmarkDiv svg {
  margin: 5px;
  cursor: pointer;
}

.starMark {
  font-size: 25px;
}

/*ImportFile*/
.importTableHidden,
.searchTableHidden {
  /* width: 100%; */
  bottom: 0 !important;
  z-index: 999 !important;
  /* overflow-x: auto; */
  background-color: rgba(51, 51, 51, 0.6);
  position: absolute;
  transition: height 1s;
  height: 60px !important;
  /* right: 50px; */
  left: 0px;
}

.dataTableHidden {
  /* width: 100%; */
  bottom: 0 !important;
  z-index: 999 !important;
  /* overflow-x: auto; */
  background-color: rgba(51, 51, 51, 0.6);
  backdrop-filter: blur(4px);
  position: absolute;
  transition: height 1s;
  /* height: 300px !important; */
  height: 83px !important;
  /* right: 50px; */
  left: 0px;
}

.dataTableHidden svg {
  color: #fff !important;
}

.importTableShown,
.searchTableShown {
  /* width: 100%; */
  bottom: 0;
  height: 60vh;
  background-color: rgba(51, 51, 51, 0.6);
  backdrop-filter: blur(4px);
  position: absolute;
  z-index: 999 !important;
  transition: height 1s;
  /* right: 50px; */
  left: 0;
  color: #fff;
}

.importTableHidden th,
.importTableShown th,
.searchTableHidden th,
.searchTableShown th {
  /* font-family: "NeoSansArabic"; */
  text-align: center;
  color: #fff;
  cursor: pointer;
  font-weight: bold !important;
  vertical-align: middle !important;
}

.importTableHidden td,
.importTableShown td,
.searchTableHidden td,
.searchTableShown td {
  vertical-align: middle;
  text-align: center;
  /* font-family: "NeoSansArabic"; */
  font-size: 15px;
  text-align: right;
  /* padding: 10px !important; */
  color: #fff;
  width: 200px;
}

.searchTableShown td svg {
  color: #fff !important;
}

#searchMeta tr:nth-child(odd),
.dashDataTable tr:nth-child(odd) {
  /* background-color: #f2f2f2; */
}

.ant-table-thead th {
  background-color: rgba(238, 231, 225, 0.2) !important;

  border: none !important;
  border-inline-end: 1px solid #fff !important;
  /* color: #fff !important; */
}

.ant-table-thead th span,
.ant-table-thead th svg {
  color: #fff !important;
}

/* .ant-table-container tr:hover {
  background-color: #eee7e1 !important;
} */

.ant-table-container th,
.ant-table-container td {
  padding: 5px 10px !important;
  unicode-bidi: plaintext;
}

.tableArrow {
  z-index: 9999 !important;
  position: absolute;
  font-size: 20px;
  color: #fbfcfe;
  margin: auto;
  text-align: center;
  padding: 2px 14px;
  border-radius: 4px;
  cursor: pointer;
  background-color: #b45333 !important;
}

.searchTableArrow {
  position: absolute;
  left: 50%;
  top: -15px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid #ddd;
  /* background-color: #fff; */
  background-color: rgba(51, 51, 51, 0.4);
  color: #fff !important;
}

.importTableArrow {
  left: 50%;
}

.fa-arrow-left.tableArrow {
  z-index: 9999 !important;
  position: absolute;
  font-size: 20px;
  color: #fbfcfe;
  margin: auto;
  text-align: center;
  padding: 2px 14px;
  border-radius: 4px;
  opacity: 0.8;
  background-color: #0b2548 !important;
  left: 45%;
}

@media (max-width: 576px) {
  .tableArrow {
    left: 20%;
  }

  .modal-content {
    width: 100%;
  }
}

.closeImportTable,
.closeSearchTable {
  display: none;
}

.ant-upload {
  width: 100%;
}

.ant-upload-list {
  /* margin: 10px auto; */
}

.ant-upload-list-item-container {
  padding: 20px 10px;
  /* box-shadow: 0 2px 3px 3px #ddd; */
  border-radius: 5px;
  /* border-right: 3px solid #b45333; */
  border: 1px solid #fff;
  margin-top: 10px;
  color: #fff;
}

.ant-upload-list-item-container svg {
  color: #fff !important;
}

.ant-upload-list-item:hover {
  background-color: transparent !important;
}

.importFileInput::-webkit-file-upload-button {
  visibility: hidden;
}

.importFileInput::before {
  content: "";
  /* -webkit-user-select: none; */
  cursor: pointer;
}

.import_file .react-tabs__tab-list {
  display: flex;
  gap: 10px;
  margin-block: 10px;
  border-bottom: none !important;
}

.import_file .react-tabs__tab {
  text-align: center;
  border-radius: 50px;
  background-color: #eee7e1;
  flex: 1;
  color: #b69d7f !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 7px;
}

.import_file .react-tabs__tab img {
  filter: brightness(0) saturate(100%) invert(66%) sepia(10%) saturate(875%)
    hue-rotate(353deg) brightness(96%) contrast(91%);
}

.import_file .react-tabs__tab.react-tabs__tab--selected {
  background-color: #f4dfd9 !important;
  color: #b45333 !important;
}

.import_file .react-tabs__tab.react-tabs__tab--selected img {
  filter: brightness(0) saturate(100%) invert(37%) sepia(56%) saturate(659%)
    hue-rotate(329deg) brightness(95%) contrast(93%);
}

.import_file .react-tabs__tab:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.import_file .react-tabs__tab:focus:after {
  display: none !important;
}

.tableHeaderIconsDiv {
  position: relative;
  /* box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%); */
  background-color: transparent !important;
  /* padding: 2px 5px 40px 5px; */
  text-align: right;
}

.tableHeaderIconsDiv svg {
  color: #fff !important;
  /* color: #000 !important; */
}

.tableHeaderBtn {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  background-color: transparent !important;
  color: #fff;
}

.table-responsive {
  height: 100%;
}

.resultsNumber {
  /* font-family: NeoSansArabic; */
  color: #fff;
  font-size: 15px;
  text-align: right;
  /* padding: 5px 20px; */
  float: right;
}

.resultsNumber button {
  background-color: transparent !important;
}

/* #searchMeta svg {
  color: #0b2548;
} */

#searchMeta {
  border-radius: 15px;
}

.openMetaHelp {
  height: 100%;
  /* background-color: rgba(51, 51, 51, 0.40); */
}

#searchMeta .ant-table-body {
  padding-bottom: 10px !important;
  overflow: auto scroll;
  max-height: 76% !important;
  /* height: 39vh !important; */
  height: 46vh !important;
  position: absolute !important;
  width: 100% !important;
}

#searchMeta .ant-spin-container,
#searchMeta .ant-table-container,
#searchMeta .ant-spin-nested-loading,
#searchMeta .ant-table-wrapper,
#searchMeta .ant-table {
  height: 100% !important;
  background-color: transparent;
}

.ant-table {
  background-color: transparent !important;
}

#searchMeta .ant-table-container,
#searchMeta .ant-table {
  /* height: calc(100% - 42px) !important; */
  scrollbar-color: inherit !important;
}

#searchMeta .ant-table-container {
  border: none !important;
}

.metaheaderBtn {
  padding: 5px;
  border-radius: 5px !important;
  margin-left: 10px;
  border: none !important;
}

.metaSide {
  margin-top: 5px;
  padding: 10px;
  /* padding: 5px; */
  border-radius: 7px;
  margin-left: 20px;
  max-height: 97%;
  height: 100%;
  position: absolute;
  margin: 10px;
  width: 97%;
  padding-inline-start: 0;
  margin-inline-start: 0;
}

.tableFiltersButtons .ant-btn {
  padding: 4px 7px !important;
}

.metaSideScroll {
  overflow-y: auto;
  height: 50vh;
  /* box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%); */
  padding-bottom: 60px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.metaRow {
  height: 100% !important;
  overflow: hidden;
}

.metaSideDiv {
  text-align: center;
  /* font-family: "NeoSansArabic"; */
  padding: 12px;
  /* border-bottom: 1px solid #f0f0f0; */
  cursor: pointer;
  /* font-size: 16px; */
  font-size: 12px;
  border-radius: 30px;
}

.metaSideDiv:hover {
  /* background-color: #edf1f5; */
  background-color: #b45333;
}

.closedToolsMenu,
.closedservicesMenu {
  display: none;
}

.openedToolsMenu {
  display: block;
  position: absolute;
  top: 50px;
  left: 0%;
  direction: ltr;
}

.openedToolsMenu ul,
.openedservicesMenu ul {
  list-style-type: none;
  /* background-color: #fff; */
  /* box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%); */
  border-radius: 5px;
  padding: 2px;
}

.openedservicesMenu {
  top: 0%;
  left: 50px;
  display: block;
  position: absolute;
}

.openedservicesMenuImg {
  cursor: pointer;
  /* filter: invert(30%) sepia(72%) saturate(7467%) hue-rotate(169deg)
    brightness(90%) contrast(97%) !important; */
  filter: brightness(0) invert(1) !important;
  width: 24px;
  height: 24px;
}

.nearbyIcon:hover {
  /* filter: invert(30%) sepia(72%) saturate(7467%) hue-rotate(169deg)
    brightness(90%) contrast(97%) !important; */
  /* filter: brightness(0) saturate(100%) invert(38%) sepia(19%) saturate(1705%)
    hue-rotate(329deg) brightness(98%) contrast(95%) !important; */
}

.pieChartClass .recharts-surface {
  height: 160px !important;
}

.recharts-default-tooltip {
  z-index: 10 !important;
}

.indicatorTitle {
  width: 100%;
  text-align: center;
  font-size: 15px;
  font-weight: bold;
  background: #ffffffe0;
}

.indicatorTable .table td {
  padding: 0.25rem;
  vertical-align: top;
  text-align: center;
  max-width: 200px;
  white-space: pre-wrap;
}

.openedservicesMenu li:not(.layers_li) {
  /* display: inline-block !important; */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  /* margin: 7px 15px; */
  margin: 4px;
  /* background-color: #33333366; */
  background-color: rgb(51 51 51 / 60%) !important;
  width: 36px;
  height: 36px;
  /* padding: 5px; */
  text-align: center;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 10px;
}

.activeService {
  background: #b45333 !important;
}

.activeMetaSide {
  background: #b45333 !important;
  color: #fff;
}

.activeService img {
  /* filter: unset !important; */
}

:where(.css-dev-only-do-not-override-j9bb5n).ant-select:hover
  .ant-select-arrow:not(:last-child) {
  opacity: 1 !important;
}

.ant-select:hover .ant-form-item-feedback-icon,
.ant-select:hover .ant-select-arrow {
  /* display: none !important; */
  opacity: 1 !important;
}

.openedToolsMenuLi {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  /* margin: 15px 7px; */
  margin: 7px !important;
  position: relative;
  /* background-color: #33333366; */
  background-color: rgb(51 51 51 / 60%) !important;
  width: 36px !important;
  height: 36px !important;
  /* padding: 5px; */
  text-align: center;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 10px;
}

.moreLessIcon {
  /* border-left: 2px solid #364464;
  line-height: 3; */
  padding-left: 10px;
}

.serviceSearch {
  padding: 10px !important;
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 7px;
  position: absolute;
  top: 13%;
  width: 300px;
  left: 10%;
}

.toolsMenu {
  text-align: right;
  padding: 10px !important;
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 7px;
  position: absolute;
  left: 110%;
  /* bottom: 30px; */
  /* bottom: 130px; */
  z-index: 9999999 !important;
}

.toolsMenu li {
  display: inline-block !important;
  cursor: pointer;
}

.inquiryTool {
  width: 400px;
  text-align: right;
  padding: 10px !important;
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 7px;
  position: relative;
  /* left: 20%; */
  z-index: 99999999 !important;
}

.leftToolMenu {
  /* min-width: 400px; */
  text-align: right;
  padding: 10px !important;
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  /* border-radius: 7px; */
  border-radius: 20px;
  position: absolute !important;
  top: -50px;
  left: 60px;
  overflow: hidden !important;
  z-index: 99999999 !important;
  display: flex;
  flex-direction: column;
  gap: 5px;
  background-color: rgb(51 51 51 / 60%) !important;
}

.leftToolMenu svg,
.leftToolMenu:hover svg {
  color: #fff;
}

.leftToolMenu > div:first-of-type {
  /* height: 100% !important; */
}

.allToolsPage {
  position: absolute;
  inset: 0;
}

.allToolsPage > div {
  /* height: 0 !important; */
}

.layersMenu {
  width: 300px;
}

.layersMenu svg {
  color: #fff;
}

.layersMenu li {
  display: block !important;
  /* font-family: "NeoSansArabic"; */
  /* background-color: #f0ebe5; */
  background-color: #3336;
  border-radius: 5px;
}

.gmnoprint {
  display: none !important;
}

.css-78trlr-MuiButtonBase-root-MuiIconButton-root:focus,
.css-78trlr-MuiButtonBase-root-MuiIconButton-root:hover {
  outline: none !important;
  background-color: transparent !important;
}

/*Traffic*/
.trafficMenu li {
  margin: 2px;
  /* font-family: "NeoSansArabic" !important; */
}

.trafficMenu ul {
  padding: 5px 10px !important;
}

.trafficMenu {
  text-align: right;
  padding: 0px !important;
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  border-radius: 7px;
  position: relative;
  left: 28%;
  bottom: 65px !important;
}

.trafficMenu .red {
  background-color: #e60000;
  width: 20px;
  height: 10px;
}

.trafficMenu .darkRed {
  background-color: #9e1313;
  width: 20px;
  height: 10px;
}

.trafficMenu .orange {
  background-color: #f07d02;
  width: 20px;
  height: 10px;
}

.trafficMenu .green {
  background-color: #84ca50;
  width: 20px;
  height: 10px;
}

/*GeneralSearch*/
.generalSearchResult {
  margin-bottom: 20px;
  height: calc(100vh - 260px);
  overflow: auto;
}

.ant-picker {
  border-radius: 20px;
  background-color: transparent !important;
  color: #fff;
}

.ant-picker:hover,
.ant-picker-focused {
  border-color: #fff !important;
}

.ant-picker .ant-picker-separator {
  color: #fff !important;
}

.ant-picker .ant-picker-suffix {
  color: #fff !important;
}

.ant-picker .ant-picker-active-bar {
  background-color: #b45333 !important;
}

.generalSearchCard,
.generalSearchCardWithoutHover {
  /* width: 400px; */
  border-inline-start: 2px solid #b45333;
  padding: 20px 5px;
  /* background: #fff; */
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  /* border: 1px solid #fff; */
  border-radius: 10px;
  margin: 10px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background-color: rgb(143 143 143 / 70%);
  color: #fff !important;
}

.generalSearchCardWithoutHover {
  margin: 0 !important;
}

.generalSearchCard:hover {
  background-color: #b45333;
  border-color: #b45333;
}

.generalSearchCard div:first-of-type,
.generalSearchCardWithoutHover div:first-of-type {
  /* width: 170px; */
  word-wrap: break-word;
  text-align: start;
  word-break: break-word;
}

.esri-popup__header {
  direction: rtl !important;
}

.esri-popup__header button {
  display: block !important;
}

.esri-popup__content {
  align-items: flex-end !important;
}

.esri-popup__footer {
  flex-direction: row-reverse !important;
}

.esri-popup__inline-actions-container {
  justify-content: flex-end !important;
}

.menuIcons {
  list-style-type: none !important;
}

.menuIcons li {
  background: #fff !important;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  padding: 5px 10px 5px 10px;
  color: #b45333 !important;
  cursor: pointer;
  margin-bottom: 15px;
  text-align: center;
  border-radius: 50px;
  display: inline;
  margin: 5px;
}

.menuIcons li:hover {
  background: #b45333 !important;
  color: #fff !important;
}

.generalSearchCard p,
.generalSearchCardWithoutHover p {
  /* font-family: "NeoSansArabic"; */
  font-size: 16px;
  text-align: right;
}

.generalSearchCard:hover button {
  /* border: 1px solid #fff !important; */
}

.generalSearchCard .munSpan {
  color: #117074;
}

div[id="sideLinks.wireless_devices"],
div[id="sideLinks.add-studies-actor"] {
  display: none !important;
}

.generalSearchCard .distSpan {
  color: #25b6bd;
}

.generalSearchCard .googleIcon,
.generalSearchCard .zoomIcon {
  font-size: 17px;
  color: #fff;
  margin-right: 4px;
  margin-left: 4px;
}

.generalResultDetails table {
  text-align: right;
}

.generalResultDetails .react-tabs__tab-list {
  text-align: center;
  /* border-bottom: none; */
}

.generalResultDetails .react-tabs__tab {
  padding: 4px !important;
}

.generalResultDetails .react-tabs__tab-list svg {
  color: #fff;
  font-size: 17px;
}

.generalResultDetails .react-tabs__tab-list button:hover svg {
  color: #b45333;
}

.generalResultDetails .react-tabs__tab-list li {
  /* margin: 0px 5px; */
}

.search_tab_img:hover {
  filter: brightness(0) saturate(100%) invert(34%) sepia(34%) saturate(1625%)
    hue-rotate(336deg) brightness(99%) contrast(83%);
}

.generalResultDetails .react-tabs__tab--selected {
  border: none !important;
  /* background-color: #b45333 !important; */
  background-color: transparent !important;
}

.generalResultDetails .react-tabs__tab--selected svg {
  /* color: #fff !important; */
  color: #b45333 !important;
}

#outerSVG {
  filter: invert(46%) sepia(11%) saturate(6%) hue-rotate(359deg) brightness(92%)
    contrast(88%) !important;
}

.generalResultDetails .react-tabs__tab--selected svg,
.generalResultDetails .react-tabs__tab--selected #outerSVG,
.identifyScreen div div .activeBtn #outerSVG {
  /* color: #fff !important; */
  filter: unset !important;
}

.generalResultDetails .react-tabs__tab-list {
  /* background: #f7f7f7 !important; */
  padding: 5px;
}

.tooltipButton,
.tooltipButton:hover,
.tooltipButton:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 5px !important;
  background-color: transparent !important;
}

/*breadcrumbs*/
.searchStepsWizard {
  text-align: right !important;
  /* width: 400px;
  max-width: 450px; */
  border-bottom: 1px solid #ddd;
  width: 100%;
}

.breadcrumbs {
  border-radius: 5px;
  padding: 10px;
  border-radius: 0.3rem;
  display: inline-flex;
  overflow: hidden;
  margin-top: 5px;
  width: 100%;
  justify-content: space-between;
}

.breadcrumbs .line {
  height: 1px;
  background-color: #c1c1c1;
  flex: 1;
  margin-top: 16px;
}

.breadcrumbs li {
  list-style-type: none;
}

.breadcrumbs__item {
  /* background: #fff; */
  color: #333;
  outline: none;
  /* padding: 10px 35px; */
  /* padding-inline: 35px; */
  position: relative;
  cursor: pointer;
  text-decoration: none;
  /* clip-path: polygon(100% 0%, 79% 50%, 100% 100%, 25% 100%, 7% 51%, 25% 0%); */
  /* background-color: #edf1f5; */
  text-align: center;
}

.breadcrumbs__item div {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #fff;
  background-color: #c1c1c1;
  margin: auto;
  margin-inline: 5px;
}

.breadcrumbs__item p {
  color: #ececec;
}

.breadcrumbs .first {
  /* clip-path: polygon(
    100% 0%,
    100% 53%,
    100% 100%,
    25% 100%,
    7% 51%,
    25% 0%
  ) !important; */
}

.breadcrumbs .second,
.breadcrumbs .third {
  /* margin-right: -15px !important; */
}

.breadcrumbs__item:hover {
  /* background: #b45333; */
  /* color: #fff; */
}

.breadcrumbs__itemActive {
  /* background: #b45333; */
  /* color: #fff; */
}

.breadcrumbs__itemActive div {
  background-color: #b45333 !important;
}

.breadcrumbs__itemActive p {
  color: #fff !important;
}

/*Help*/

/*ReactJoyride*/

.react-joyride__tooltip {
  background-color: rgb(87 86 86 / 40%) !important;
}

.__floater__arrow polygon {
  fill: rgb(87 86 86 / 40%) !important;
}

.react-joyride__tooltip button[title="التالي"],
.react-joyride__tooltip button[title="الأخيرة"],
.react-joyride__tooltip button[title="السابق"] {
  background-color: #b45333 !important;
  font-size: 0 !important;
  color: #fff !important;
  z-index: 99999999 !important;
}

.react-joyride__tooltip button[title="الأخيرة"] {
  animation: float 2s ease-out infinite;
}

.react-joyride__tooltip button[title="إغلاق"] {
  animation: float 2s ease-out infinite;
}

.react-joyride__tooltip button[title="إغلاق"] svg {
  color: #fff !important;
  filter: brightness(10);
}

.close {
  display: none !important;
}

.react-joyride__tooltip button[title="Skip"],
.react-joyride__tooltip button[title="Open the dialog"] {
  font-size: 0 !important;
  display: none !important;
  z-index: 0 !important;
}

.react-joyride__beacon,
.react-joyride__beacon > span {
  font-size: 0 !important;
  display: none !important;
  z-index: 0 !important;
}

.react-joyride__tooltip button[title="التالي"]::after {
  content: "\f061" !important;
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
  font-size: 15px !important;
}

.react-joyride__tooltip button[title="السابق"]::after {
  content: "\f060" !important;
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
  font-size: 15px !important;
}

.react-joyride__tooltip button[title="الأخيرة"]::after {
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 15px;
}

.react-joyride__tooltip button[title="إغلاق"] {
  right: auto !important;
  /* color: #b45333 !important; */
}

.react-joyride__tooltip button[title="التالي"],
.react-joyride__tooltip button[title="السابق"] {
  transform: scale(-1) !important;
}

.react-joyride__tooltip button[title="السابق"] {
  position: absolute;
  right: 10px;
}

.helpFirstTime #react-joyride-step-0 .__floater {
  left: 90px !important;
}

.__floater__body {
  /*margin-left: 20px !important;*/
}

.react-joyride__tooltip button[title="التالي"],
.react-joyride__tooltip button[title="الأخيرة"] {
  position: absolute;
  left: 10px;
}

.react-joyride__tooltip > div > div {
  padding: 20px 10px 0px !important;
  /* font-family: "NeoSansArabic" !important; */
  color: #fff !important;
}

.react-joyride__beacon,
.react-joyride__beacon > span {
  font-size: 0 !important;
  display: none !important;
  z-index: 0 !important;
}

.react-joyride__overlay {
  background-color: rgb(255 255 255 / 10%) !important;
  z-index: unset !important;
}

.react-joyride__spotlight {
  /* border: 1px solid #b45333 !important;
  background-color: #b45333 !important; */
  opacity: 0.3 !important;
}

/* .__floater {
  top: -12px !important;
} */
.ant-tooltip-inner {
  /* font-family: "NeoSansArabic" !important; */
}

.generalSearchCard:hover svg,
.generalSearchCard:hover p,
.generalSearchCard:hover h5,
.generalSearchCard:hover span {
  color: #fff !important;
}

.SideMenuOpenArrow {
  position: absolute;
  z-index: 99;
  top: 10px !important;
  right: -30px !important;
  z-index: 1000;
}

.SideMenuOpenArrow svg,
.closeMenuIcon svg {
  font-size: 25px !important;
  /* color: #25b6bd !important; */
}

.closeMenuIcon {
  margin-left: 0px !important;
}

.SideMenuOpenArrow button,
.closeMenuIcon {
  padding: 8px !important;
  /* background: #000 !important; */
  top: 10px !important;
  width: 40px;
  cursor: pointer;
}

/* start open-close-map-tool-menu */
.open-close-map-tool-menu {
  text-align: right;
  position: absolute;
  top: 22px;
  right: -20px;
}

.open-close-map-tool-menu button {
  background-color: rgb(51 51 51 / 60%) !important;
  border: none;
}

.open-close-map-tool-menu svg {
  color: #fff !important;
}

.open-close-side-menu {
  position: absolute;
  top: 22px;
  left: -27px;
}

.open-close-side-menu button {
  background-color: rgb(51 51 51 / 60%) !important;
  border: none;
}

.open-close-side-menu svg {
  color: #fff !important;
}

/* .open-close-map-tool-menu button {
  padding: 5px !important;
  background: #fff !important;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid #ddd;
} */

/* end open-close-map-tool-menu */

/* .SideMenuOpenArrow button:hover svg,
.SideMenuOpenArrow button:hover,
.closeMenuIcon:hover {
  color: #fff !important;
  background-color: #25b6bd !important;
} */

/* .closeMenuIcon:hover svg {
  color: #fff !important;
} */

/*Animations*/

/* .generalSearchCard:after , .generalSearchCardWithoutHover:after {
  content: "";
  position: absolute;
  top: 0;
  left: 100px;
  width: 500%;
  height: 1000%;
  background: #b45333 !important;
  z-index: -1;
  transform-origin: 0% 0%;
  transform: translateX(calc(20% - 25px)) translateY(10%) rotate(-45deg);
  transform: translateY(10%) translateX(16%) rotate(-45deg);
  transition: transform 0.3s;
}

.generalSearchCard:hover::after {
  transform: translateY(10%) translateX(-200px) rotate(-45deg) !important;
} */

.SearchBtn,
.esri-distance-measurement-2d__clear-button,
.esri-area-measurement-2d__clear-button {
  /* margin: 1em; */
  /* font-family: "NeoSansArabic" !important; */
  letter-spacing: 1px;
  height: 2.5em !important;
  width: 100% !important;
  background: #b45333;
  color: #fff;
  font-size: 1.05em;
  border: 1px solid #b45333;
  border-radius: 5px;
  transition: all 1s ease-out;
  box-shadow: inset 0 0 #b45333;
  border-radius: 30px;
  margin: auto;
  display: block;
}

/* .SearchBtn.bookmark_searchBtn {
  width: 100% !important;
} */

.SearchBtn {
  /* width: 200px !important; */
  max-width: 100%;
}

.SearchBtn:disabled {
  background-color: #ccc !important;
  border-color: #ccc !important;
  color: #fff !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
}

.SearchBtn:disabled:hover {
  color: #fff !important;
}

.seeMoreBtn,
.printBtn {
  margin: 1em;
  /* font-family: "NeoSansArabic" !important; */
  letter-spacing: 1px;

  background: #b45333 !important;
  color: #fff !important;
  font-size: 1.05em;
  border: 1px solid #b45333 !important;
  border-radius: 5px;
}

.seeMoreBtn:hover,
.seeMoreBtn:focus,
.printBtn:hover,
.printBtn:focus {
  background-color: #fff !important;
  border: 1px solid #b45333 !important;
  color: #b45333 !important;
}

.seeMoreBtn {
  margin: 0 !important;
  margin-bottom: 2px !important;
}

.tableStatBtn {
  background-color: #b45333 !important;
  color: #fff !important;
  float: left !important;
  /* font-family: "NeoSansArabic" !important; */
}

.ant-select-selection-overflow-item-suffix {
  margin-inline-start: -15px;
}

.ant-select-selection-overflow-item {
  padding-left: 10px;
}

.tableStatClose {
  /* background-color: #ff4d4f !important; */
  color: #fff !important;
  float: left !important;
  /* font-family: "NeoSansArabic" !important; */
  border: none !important;
  color: #fff !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

:where(.css-dev-only-do-not-override-j9bb5n).ant-pagination
  .ant-pagination-item-active {
  border-color: #b45333 !important;
}

:where(.css-dev-only-do-not-override-j9bb5n).ant-pagination
  .ant-pagination-item-active
  a {
  color: #b45333 !important;
}

.openedToolsMenuLi:hover img,
.openedservicesMenu li:hover img {
  /* filter: unset !important; */
}

.ant-pagination-item {
  background-color: rgba(51, 51, 51, 0.6) !important;
}

.ant-pagination-item a {
  color: #fff !important;
}

.ant-pagination-item-active {
  border-color: #b45333 !important;
}

/* #openedToolsMenuLi:hover,
.openedservicesMenu li:hover {
  transform: scale(1.2);
} */

/* .SearchBtn:hover,
.esri-distance-measurement-2d__clear-button:hover,
.esri-area-measurement-2d__clear-button:hover,
#openedToolsMenuLi:hover,
.openedservicesMenu li:hover {
  box-shadow: inset 16em 0 #fff;
  cursor: pointer;
  color: #000 !important;
} */

/* .SearchBtn.saveSearchBtn:hover,
.esri-distance-measurement-2d__clear-button:hover,
.esri-area-measurement-2d__clear-button:hover,
#openedToolsMenuLi:hover,
.openedservicesMenu li:hover {
  box-shadow: inset 20em 0 #b45333;
  cursor: pointer;
  color: #fff !important;
  border-color: #b45333 !important;
} */

.openedservicesMenu li:not(.layers_li):hover,
.openedToolsMenuLi:not(.layers_li):hover {
  box-shadow: inset 20em 0 #b45333;
  cursor: pointer;
  color: #fff !important;
  border-color: #b45333 !important;
}

.esri-distance-measurement-2d__clear-buttonو.esri-area-measurement-2d__clear-button {
  margin: 10px auto;
}

.toc {
  overflow-y: scroll;
  /*width: 266px !important;*/
  /* height: 340px !important; */
  height: fit-content !important;
  max-height: 340px;
}

.toc li {
  list-style: none;
  background-color: transparent !important;
}

.toc li div {
  color: #fff !important;
  text-wrap: wrap;
}

.toc-map {
  margin-top: 20px;
  padding: 2%;
  background: white;
  overflow-x: hidden;
  direction: rtl;
  background: transparent;
}

.toc-result {
  padding: 2%;
  display: grid;
  grid-template-rows: 50px 65vh;
  background: white;
  /* overflow-x: hidden; */
  direction: rtl;
  overflow-x: hidden;
  background: transparent;
  overflow-y: scroll;
}

.disableLabel {
  color: #707070;
  border-radius: 15px;
  padding: 5px;
  margin-bottom: 5px;
}

.enableLabel {
  /* color: #fff; */
  /* background: #00619b; */
  color: black;
  border-radius: 15px;
  padding: 5px;
  /* margin-bottom: 5px; */
}

.toc-gallery {
  display: grid;
  padding: 0%;
  grid-template-columns: 10px 15px auto 25px;
  grid-gap: 10px;
  align-items: center;
}

.toc-gallery label {
  width: 150px;
  text-wrap: wrap;
}

.gallery {
  padding-top: 10px;
  padding-right: 16px;
  width: 258px;
}

.gallery-img {
  float: right;
  width: 29%;
  margin-left: 10px;
  border-radius: 5px;
  text-align: center;
  margin-bottom: 10px;
  background: white;
  box-shadow: 0px 0px 4px 0px rgba(4, 4, 4, 0.26);
  font-size: 13px;
  overflow-wrap: break-word;
}

.gallery-img img {
  width: 100%;
}

.ant-spin-dot-item {
  background-color: #099655 !important;
}

.galleryHead {
  color: #fff;
  text-align: right;
  font-weight: bold;
  margin-bottom: 0;
}

.identifyScreen {
  overflow-y: auto;
  height: auto;
  max-height: 400px;
  direction: rtl;
}

.identifyTableStyle {
  margin-top: 10px;
  direction: rtl;
  /* border-bottom: 7px solid #25b6bd; */
}

.identifyTR {
  border-bottom: 1px dashed #fff !important;
  border-top: none !important;
  height: 35px;
}

.infoTableTd {
  /* font-weight: bold; */
  color: #b45333;
  width: 120px;
}

.infoTableData {
  white-space: pre-line;
}

.print-box {
  background: rgba(0, 0, 0, 0.16);
  position: fixed;
  z-index: 1;
  border: 2px dashed #757575;
  fill-opacity: 0;
  pointer-events: none;
  cursor: pointer;
}

.printStyle {
  /* font-family: "NeoSansArabic"; */
  padding: 10px;
  width: 300px;
  text-align: right;
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
}

.spinStyle {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  background-color: rgb(16 16 16 / 20%);
  align-content: center;
  z-index: 100;
}

.loadingPortalStyle {
  margin-top: 20px;
  width: 100% !important;
  height: 100% !important;
  text-align: center;
  z-index: 100;
}

.spinStyleConatiner {
  text-align: center;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
}

.selectLabelStyle {
  margin-top: 10px;
  margin-bottom: 10px;
  float: right;
  color: #fff;
}

.coordinateData {
  font-weight: bold;
  text-align: center;
  margin-top: 10px;
  color: #fff;
}

.esri-measurement .esri-widget {
  background-color: transparent !important;
}

.resultTitle {
  /* font-family: "NeoSansArabic"; */
  font-size: 16x;
  padding: 10px;
  display: flex;
  /* background: #edf1f5; */
  justify-content: space-between;
  align-items: center;
  /* position: sticky;
  top: 0;
  z-index: 100; */
}

.resultTitle > div:not(:last-of-type) {
  background-color: rgb(143 143 143 / 70%);
  padding: 10px;
  border-radius: 30px;
  display: flex;
  align-items: center;
}

.resultTitle > button:only-child {
  margin-inline-start: auto;
}

.ant-popover {
  inset: auto auto 169px -223px !important;
}

.ant-modal-content .ant-popover {
  inset: auto auto -68px -244px !important;
}

.searchInfoStyle {
  margin-top: 20px;
  text-align: center;
  color: red;
  /* font-family: "NeoSansArabic"; */
  font-size: 16px;
  white-space: break-spaces;
}

.ant-pagination-options {
  display: none !important;
}

.noDataStyle {
  text-align: center;
  margin-top: 50px;
  font-size: 17px;
}

.searchLocationInfo {
  margin-right: 5px;
  color: lightslategray;
  white-space: pre;
}

.showMoreModal {
  color: #fff;
  font-size: large;
}

.showMoreModal.ar {
  direction: rtl;
}

.showMoreModal.en {
  direction: ltr;
}

.filterModal .ant-modal-title,
.filterModal button {
  text-align: center !important;
  /* font-family: "NeoSansArabic" !important; */
}

/* .filterModal .ant-modal-body button {
  width: 200px !important;
} */

.filterModal .ant-col {
  padding: 5px !important;
}

.tableActionsUl {
  display: flex !important;
  list-style-type: none !important;
}

/*LOADER*/
.spinner-bk {
  background: rgba(240, 243, 244, 0.4);
  position: fixed;
  z-index: 9999999 !important;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.sk-cube-grid {
  width: 40px;
  height: 40px;
  margin: 20% auto;
}

.sk-cube-grid .sk-cube {
  width: 33%;
  height: 33%;
  background-color: #b45333;
  float: left;
  -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
  animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
}

:where(.css-dev-only-do-not-override-kghr11).ant-input-outlined:hover {
  border-color: #b45333;
}

.sk-cube-grid .sk-cube1 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.sk-cube-grid .sk-cube2 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.sk-cube-grid .sk-cube3 {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

.sk-cube-grid .sk-cube4 {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.sk-cube-grid .sk-cube5 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.sk-cube-grid .sk-cube6 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.sk-cube-grid .sk-cube7 {
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}

.sk-cube-grid .sk-cube8 {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.sk-cube-grid .sk-cube9 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

@-webkit-keyframes sk-cubeGridScaleDelay {
  0%,
  70%,
  100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }

  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}

@keyframes sk-cubeGridScaleDelay {
  0%,
  70%,
  100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }

  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}

.ant-message {
  /* font-family: "NeoSansArabic" !important; */
}

.canvas-line-zoom {
  border: 1px solid red;
  position: fixed;
  top: 0;
  z-index: 100000;
  pointer-events: none;
  border: none;
}

.ant-select-dropdown {
  direction: ltr !important;
}

.ant-table-filter-dropdown .ant-select-dropdown {
  top: 40px !important;
}

.esri-select {
  font-size: 1rem !important;
  padding: 10px 15px !important;
  height: auto;
  background-color: rgb(0 0 0 / 40%) !important;
  color: #fff;
  border: 1px solid #ced4da;
  border-radius: 20px;
  background-position: left;
}

.esri-select option {
  background-color: rgb(51 51 51 / 60%) !important;
  color: #fff !important;
}

.esri-select option:hover {
  background-color: #b45333 !important;
}

.esri-distance-measurement-2d__measurement {
  background-color: transparent !important;
  color: #fff !important;
}

.esri-distance-measurement-2d__measurement-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.esri-area-measurement-2d__measurement {
  padding: 0;
  background-color: transparent;
}

.esri-area-measurement-2d__settings {
  padding: 0 !important;
}

.esri-area-measurement-2d__measurement-item {
  margin-bottom: 10px !important;
  border-bottom: 1.5px dashed #fff !important;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.esri-area-measurement-2d__measurement-item-title {
  font-weight: bold;
  /* color: #b45333; */
  color: #fff;
  font-size: 0.8rem;
}

.measurePage p {
  text-align: center;
  color: #fff;
  /* font-family: "NeoSansArabic"; */
  white-space: normal;
  text-align: right;
  font-size: 0.8rem;
}

.esri-area-measurement-2d__measurement-item-value {
  font-size: 0.75rem;
  color: #fff;
}

.esri-area-measurement-2d__units-label {
  font-size: 18px;
}

.ant-notification.ant-notification-topLeft.ant-notification-rtl {
  text-align: right;
  color: white;
}

.ant-notification-notice-description {
  font-size: 18px !important;
  font-weight: bold;
}

.ant-notification-notice.ant-notification-notice-closable {
  background-color: #f0ebe5;
}

.ant-notification-notice-description {
  color: #b45333 !important;
}

.ant-notification-notice-close-icon {
  color: #b45333 !important;
}

.no-chosen-layer {
  height: 200px;
  width: 100%;
  flex-direction: column;
  display: flex;
  text-align: center;
  justify-content: center;
  margin-right: 25%;
  border: dotted #fff;
  /* font-family: "NeoSansArabic" !important; */
}

.exportMenu span {
  /* font-family: "NeoSansArabic" !important; */
}

.exportPdful {
  list-style-type: none;
  display: flex;
  text-align: right;
  direction: rtl;
  padding-top: 15px;
  align-items: center;
}

.exportPdfRightLi {
  text-align: right;
  border-left: 3px solid #000;
  padding-left: 5px;
  margin-left: 7px;
  height: fit-content;
}

.exportPdful h6 {
  font-weight: bold;
  /* font-family: "NeoSansArabic" !important; */
}

.exportPdfPage table {
  padding: 10px;
}

.exportPdfPage th,
.exportPdfPage td {
  width: 100px !important;
  word-break: break-all;
}

.exportPdfPage {
  overflow-y: scroll;
  height: 100vh !important;
  /* border: 1px solid #000; */
  /* margin: 10px; */
  padding-top: 20px;
  background-color: rgb(51 51 51 / 60%) !important;
}

@media only print {
  .reportStyle2 td {
    font-size: 17px !important;
  }

  .printBtn,
  .printBtnDisplay {
    display: none !important;
  }

  html {
    /* overflow: hidden; */
  }

  .colorBall {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  .exportPdfPage {
    display: block;
    width: auto;
    height: auto;
    overflow: visible;
  }

  .mapDiv {
    padding: 10px !important;
  }

  body {
    box-sizing: border-box;
    /* border: 1px solid black !important; */
  }

  .one-page {
    /* padding: 10px;
    margin: 10px; */
    /* margin: 0;
    padding: 10px;
    border: initial;
    border-radius: initial;
    width: initial;
    min-height: initial;
    box-shadow: initial;
    background: initial; */
    page-break-before: always !important;
    zoom: 0.95;
  }

  .print-button {
    display: none;
  }

  .underlineStyle {
    margin-top: 20px;
    box-shadow: 0 0 2px 1px black;
    text-align: center;
    padding: 5px;
  }
}

.chartInfo:hover {
  -ms-transform: scale(2);
  -webkit-transform: scale(2);
  transform: scale(2);
}

.barChartTootltip {
  margin: 0px;
  padding: 10px;
  font-size: 11px;
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(204, 204, 204);
  white-space: nowrap;
  position: absolute;
  margin-right: -100px;
}

.generalDataTableMin {
  right: 510px;
  z-index: 10000 !important;
  direction: rtl;
}

.generalDataTableMax {
  right: 376px;
  left: 190px;
  /* right: 365px;
  left: 365px; */
  z-index: 10000 !important;
  direction: rtl;
}

.centerPrintChart {
  width: 200px;
  display: block;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.centerPrintChart label {
  white-space: pre-wrap;
  margin-top: 30px;
  font-size: 23px;
}

.tableTitle {
  text-align: right;
  width: 50%;
  margin-right: 10px;
  font-size: 16px;
  margin-bottom: 10px;
  color: #fff !important;
}

.fullscreen {
  display: contents !important;
}

.topBar {
  position: absolute;
  top: 20px;
  /* right: 50%;
  transform: translateX(50%); */
  z-index: 99999;
  background-color: rgb(51 51 51 / 60%) !important;
  border-radius: 30px;
  padding: 10px;
}

.outerSearchZoomAll:hover {
  color: #b55433 !important;
}

.ant-message {
  z-index: 9999999999;
}

/* .esri-popup__main-container {
  background-color: rgb(51 51 51 / 60%) !important !important;
}

.esri-popup__main-container * {
  color: #fff !important;
}

.esri-popup__main-container *:hover {
  background-color: transparent !important;
}

.esri-feature {
  background-color: transparent !important;
}

.esri-popup__pointer-direction {
  background-color: rgb(51 51 51 / 60%) !important !important;
} */

/*Meta Statistics*/
.metaStatModal {
  z-index: 99999;
  width: 100% !important;
  background-color: rgba(0, 0, 0, 0.52);
  top: 0 !important;
  right: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  position: fixed !important;
  height: 100% !important;
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  /* font-family: "arabicNums"; */
}

.metaStatModal .ant-modal-content,
.metaStatModal .ant-modal-header {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
  background-color: transparent !important;
}

.study-actor-modal .ant-modal-content {
  border-radius: 24px;
  padding-inline: 14px !important;
}

.study-actor-modal .ant-modal-body {
  max-height: 500px;
  overflow: auto;
  padding-inline: 10px;
}

.metaStatModal .statTitle,
.metaStatModal th,
.metaStatModal td {
  /* font-family: "NeoSansArabic"; */
  color: #fff;
  text-align: center;
}

.metaStatModal .colorBall {
  width: 20px;
  height: 20px;
  padding: 10px;
  border-radius: 50%;
  margin: auto;
}

/* .metaStatModal  */
.ant-modal-footer {
  display: none !important;
}

.filterModal .ant-modal-footer,
.logoutConfirmModal .ant-modal-footer {
  display: flex !important;
  align-items: center;
  gap: 10px;
  flex-direction: row-reverse;
}

/* .logoutConfirmModal .ant-modal-footer {
  display: flex !important;
  align-items: center;
  gap: 10px;
  flex-direction: row-reverse;
} */

.filterModal .add-new-row:hover,
.filterModal .remove-group:hover,
.filterModal .remove-row:hover {
  color: #b45333 !important;
  border-color: #b45333 !important;
}

.ant-modal-footer button:first-of-type:hover {
  color: #b45333 !important;
  border-color: #b45333 !important;
}

.ant-modal-footer button:nth-of-type(2) {
  background: #b45333 !important;
}

.marsedStyle button:disabled {
  cursor: not-allowed;
  color: #00000040 !important;
  border-color: #d9d9d9;
  background: #f5f5f5;
  text-shadow: none;
  box-shadow: none;
}

.identifyBuildTitle {
  text-align: center;
  width: 100%;
  /* font-family: "NeoSansArabic"; */
  font-size: 18px;
  padding: 10px;
  background: #edf1f5;
  font-weight: bold;
}

.metaStatModal .ant-modal-close-icon svg {
  color: #fff !important;
  font-size: 45px;
  border: 2px solid #fff;
  padding: 5px;
}

.tableFiltersButtons {
  float: left;
  display: flex;
  align-items: center;
  flex-direction: row;
}

.tableFiltersButtons svg {
  /* color: #707070; */
  /* width: 14px !important;
  height: 14px !important; */
}

.tableFiltersButtons svg:hover {
  color: #b45333;
}

/*land Data*/

.landDataIconsModal {
  border-radius: 15px;
  background-color: #fff;
  box-shadow: 0px 0px 4px 0px rgba(4, 4, 4, 0.26);
  position: absolute;
  z-index: 999;
}

.landmodalTitle {
  text-align: right;
  width: 100%;
  /* font-family: "NeoSansArabic"; */
  font-size: 18px;
  padding: 5px;
  padding-right: 10px;
  color: #edf1f5;
  background-color: #b45333;
  /* font-weight: bold; */
}

.landIconsUl {
  list-style-type: none;
  display: flex;
  padding: 5px;
  justify-content: space-between;
}

.landIconsLi {
  margin: 0px 5px;
  width: 45px;
  height: 45px;
  border-radius: 50px;
  box-shadow: 0 0 6px 0 rgb(0 0 0 / 35%);
  background-color: #b45333;
  padding: 8px;
  padding-top: 4px;
}

.closeIconsIcon {
  float: left;
  margin-top: 5px;
}

.arrow-popup-modal {
  position: absolute;
  left: 49%;
  top: 37%;
  font-size: 54px;
  color: #fffdfd;
  z-index: 101;
}

.serviceIcon {
  text-shadow: 0px 1px 0px #000;
}

.landDetails {
  color: #000;
  /* font-family: "NeoSansArabic"; */
  text-align: right;
  padding: 5px 10px;
  font-size: 16px;
  white-space: break-spaces;
}

.landDetailClose {
  position: absolute;
  left: 10px;
  top: 10px;
  color: #fff;
  font-size: 20px;
}

.landDetailsModal li {
  text-align: right;
  /* font-family: "NeoSansArabic"; */
  padding: 5px;
  color: #000;
}

.landDetailsModal ul {
  direction: rtl;
}

.metaStatModal .ant-modal-body {
  height: 100vh;
}

.gallery-modal {
  max-width: 100% !important;
}

.gallery-modal .ant-modal-close {
  top: 20px !important;
}

.gallery-modal .ant-modal-header,
.shareModal .ant-modal-header {
  margin-top: auto !important;
}

.shareModal .ant-modal-content {
  padding-top: 10px !important;
}

.shareModal .MuiFormGroup-root {
  gap: 10px;
}

.shareModal .MuiFormControlLabel-root {
  width: 100%;
  margin-right: 0 !important;
  margin-left: 0 !important;
  margin-bottom: 0 !important;
}

.shareModal .MuiFormGroup-root > div {
  border: 1px solid #fff;
  border-radius: 20px;
  padding: 2px 11px;
}

.shareModal .MuiButtonBase-root {
  padding: 5px !important;
}

.ant-modal-close {
  /* top: 5px !important; */
  top: 18px !important;
  left: 5px !important;
}

/* .metaStatModal  */
.ant-modal-close-x {
  /* display: none !important; */
  /* color: #b45333; */
  color: #fff !important;
}

.liveBroadcastModal video {
  border-radius: 10px;
  border: 2px solid #fff;
}

.ant-modal-header {
  text-align: right;
  background-color: transparent !important;
  /* padding: 7px !important; */
  border-radius: 5px !important;
  margin-bottom: 5px;
  /* margin-top: 20px; */
}

.ant-modal-header .ant-modal-title {
  color: #fff !important;
}

.login-modal .ant-modal-header {
  text-align: center;
}

.ant-table-filter-dropdown {
  background-color: rgba(51, 51, 51, 0.7) !important;
  border: 1px solid #fff;
  width: 220px;
}

.ant-table-filter-dropdown .ant-select {
  position: absolute;
  width: 200px !important;
}

.ant-table-filter-dropdown .ant-space {
  margin-top: 40px;
  align-items: normal;
}

.metaTableIcons .tableHeaderBtn svg {
  /* color: dimgrey !important; */
  color: #fff !important;
}

.metaTableIcons .tableHeaderBtn svg:hover {
  /* color: #b45333 !important; */
}

.searchTableHidden th svg,
.searchTableShown th svg,
.searchTableShown .ant-table-column-sorter-up svg,
.searchTableHidden .ant-table-column-sorter-down svg {
  color: dimgrey;
}

.marsadDeleteBtn {
  width: 100px !important;
}

.englishFont div {
  /* font-family: sans-serif !important; */
}

.englishFont span {
  /* font-family: sans-serif !important; */
}

.ant-table-column-sorter-down.active svg,
.ant-table-column-sorter-up.active svg,
.ant-table-filter-trigger.active svg {
  color: #b45333 !important;
}

.searchTableShown .ant-table-row {
  position: relative;
  cursor: pointer;
}

.pagination-container {
  /* position: absolute; */
  bottom: 0%;
  right: 50%;
  position: -webkit-sticky;
  /* Safari */
  position: sticky;
}

#outerFormWidthFit {
  /*width: 500px;*/
}

#outerFormWidth {
  width: 300px;
}

.metastatTable {
  background-color: rgba(0, 0, 0, 0.3);
  width: 80%;
}

.metastatTable2 {
  background-color: rgba(0, 0, 0, 0.3);
}

.metastatTable th {
  border: none !important;
  text-align: right !important;
}

.metastatTable2 th,
.metastatTable2 td {
  border-top: none !important;
  border-right: none !important;
  border-left: none !important;
}

.metastatTable td {
  border-left: none !important;
  border-right: none !important;
  border-bottom: none !important;
  text-align: right !important;
}

.ant-space {
  flex-direction: column !important;
  width: 100%;
}

.ant-space-item {
  /* width: 100px; */
}

.metaStatBtns {
  position: absolute;
  left: 15px;
  bottom: 60px;
}

.plan-lands-statistics-tbl td,
.plan-lands-statistics-tbl th {
  padding: 0.1rem 0.5rem;
  text-align: center !important;
}

/*Translate*/

.translateIcon {
  color: #00619b;
  font-weight: bold;
  font-size: 17px;
}

.translateIcon:hover {
  color: #fff;
}

.metaHeaderSpacePadding {
  margin-right: 40px;
}

.breadcrumbs__item p {
  margin: 0 !important;
}

/*Header Styles*/
.Dashboard-Header {
  /* background: #b45333 !important; */
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 32%), 0 0 2px 0 rgb(0 0 0 / 20%);
  height: 7vh;
  position: absolute;
  top: 11vh;
  width: 100vw;
}

.Dashboard-Header .navLink {
  width: 200px;
  font-style: normal;
  font-weight: 500 !important;
  font-size: 20px !important;
  line-height: 20px;
  padding: 10px;
  text-decoration: none !important;
  color: #d4d6de !important;
  /* font-family: "NeoSansArabic" !important; */
  margin: 2px 0px 2px 20px;
  letter-spacing: normal;
  text-align: center;
  border-radius: 0 !important;
  margin: 0 !important;
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  -ms-border-radius: 0 !important;
  -o-border-radius: 0 !important;
}

.Dashboard-Header .nav-link.active {
  color: #fff !important;
  font-weight: bold !important;
  border-bottom: 1px solid #fff !important;
}

.Header.navbar.fixed-top {
  z-index: 10 !important;
}

@media (max-width: 992px) {
  .Dashboard-Header {
    height: 100px !important;
  }

  .Dashboard-Header .userDropDown {
    margin-bottom: 37px !important;
  }

  .Dashboard-Header .navbar-nav {
    padding-top: 60px !important;
  }

  .Dashboard-Header .container-fluid {
    padding: 0 !important;
  }

  .Dashboard-Header .navLink {
    width: fit-content !important;
  }

  .Dashboard-Header .iconLink {
    margin-top: 20px;
  }
}

@media (max-width: 500px) {
  .Dashboard-Header {
    height: 145px !important;
  }

  .Dashboard-Header .navbar-nav {
    padding-top: 80px !important;
  }

  .Dashboard-Header .iconLink {
    margin-bottom: 50px !important;
  }
}

.pTextAlign {
  text-align: right;
  color: #fff;
}

/* .css-11o2879-MuiDrawer-docked .MuiListItemText-root {display: none;} */
.css-11o2879-MuiDrawer-docked .sideLinkDiv {
  /* height: 60px; */
  padding-top: 0;
}

.css-11o2879-MuiDrawer-docked img {
  width: 100% !important;
  padding-top: 10px;
}

/***********Dashboard*********************/
.dashboardPage {
  overflow-y: scroll;
  overflow-x: hidden;
  width: 100vw;
  /* font-family: "arabicNums" !important; */
}

.ant-modal-body {
  /* font-family: "arabicNums" !important; */
}

.statSquare {
  background-color: #ffebb0;
  margin: 0 3px 3px 0;
  height: 29.5vh;
}

.statSquare h6,
.mapSquaresGrid div h6 {
  /* font-family: "NeoSansArabic"; */
  font-size: 0.7rem;
  line-height: 1.93;
  text-align: center;
  font-weight: bold;
  color: #000;
  padding-bottom: 3px;
  margin: auto;
  padding-top: 10px;
}

.charts-below-map div h6 {
  /* font-family: "NeoSansArabic"; */
  font-size: 1.2rem;
  line-height: 1.93;
  text-align: center;
  font-weight: bold;
  color: #000;
  padding-bottom: 3px;
  margin: auto;
  /* padding-top: 10px; */
}

.statSquare h2,
.mapSquaresGrid div h2,
.charts-below-map div h2 {
  font-size: 40px;
  line-height: 2.1;
  text-align: center;
  font-weight: bold;
  color: #000;
}

.dashAfterHead {
  margin-top: 70px;
}

.select-wrap .ant-input {
  padding: 10px 12px 4px 11px !important;
}

.select-wrap .ant-select .ant-select-selector {
  padding: 10px 10px 4px 11px !important;
}

.select-wrap
  .ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector {
  padding: 10px 10px 4px 11px !important;
  height: 7vh !important;
}

.select-wrap
  .ant-select-single
  .ant-select-selector
  .ant-select-selection-search {
  top: 16px !important;
}

#dashMapHeightDefault {
  height: 89vh;
}

#dashMapHeight,
.dashMap {
  height: 55vh;
}

.mapSquaresGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

.mapSquaresGrid .mapSquare,
.charts-below-map .mapSquare,
.normal-chart-item.mapSquare,
.single-chart-item.mapSquare,
.last-chart-item.mapSquare {
  background-color: #fff;
  color: #000;
  border: 1px solid #d9d9d9;
  height: 35vh;
  flex-grow: 0.5;
}

.dashboardMapTable1 td {
  width: 50%;
  /* font-family: "NeoSansArabic"; */
  text-align: center;
}

.dashTableTitle {
  margin: auto;
  text-align: center;
  /* font-family: "NeoSansArabic"; */
  font-weight: bold;
  padding: 10px;
}

.dashTable1 tbody {
  max-height: 30vh;
  display: block;
  overflow: auto;
}

.dashTable2 table,
.dashTable1 table {
  display: block;
  overflow: auto;
}

.dashTable2 tbody {
  display: block;
  max-height: 84.5vh;
  overflow: auto;
}

.dashTable2 tr,
.dashTable1 tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.dashTable1,
.dashTable2 {
  margin-left: 5px;
}

.dashTable2 tr:nth-child(even) {
  background-color: #f2f2f2;
}

.dashDataTable th {
  text-align: center;
  color: #000;
  font-weight: bold;
  /* font-family: "NeoSansArabic" !important; */
}

.dashDataTable td {
  text-align: center;
  color: #00619b;
  font-size: 15px;
  /* font-family: "NeoSansArabic" !important; */
}

.dashDataPage h4 {
  /* font-family: "NeoSansArabic" !important; */
  float: right;
  text-align: right;
  margin: 20px;
  font-weight: bold;
  width: fit-content;
  border-bottom: 1px solid #000;
  padding-bottom: 4px;
}

.dashDataTable {
  width: 95%;
  margin: auto;
}

.dashHeaderSelectDiv {
  /* width: 190px; */
  margin: 0 10px;
  text-align: right;
}

.dashHeaderSelectDiv > div,
.select-year-month > div {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.dashLabel {
  color: #000;
  padding-left: 2px;
  /* padding-right: 5px; */
  margin-top: 13px;
  width: fit-content;
}

.dashHeaderDates {
  display: flex;
  list-style-type: none !important;
  padding-top: 1px;
  padding-left: 5px;
}

.dashHeaderDates li {
  padding-top: 10px;
  color: #fff;
  /* font-family: "NeoSansArabic" !important; */
}

.dashHeaderDates .rmdp-container {
  height: 7vh !important;
}

.dashHeaderDates .rmdp-container .rmdp-input {
  height: 99% !important;
  width: 100px;
}

.apexcharts-toolbar {
  z-index: 1 !important;
}

/* .languageSideDiv .MuiButtonBase-root {
  padding: 0 !important;
} */

.closeMenuIcon {
  margin-bottom: 20px !important;
}

.esri-distance-measurement-2d__clear-button,
.esri-area-measurement-2d__clear-button {
  margin: 10px auto !important;
}

/**************************************************/
/*Mobile media*/
.appStoreScreen {
  background-color: #fbfcfe;
  width: 100vw !important;
  padding-top: 100px;
  position: relative;
  height: 100vh !important;
  /* overflow: hidden; */
}

.appStoreUL h5 {
  font-weight: bold;
  /* font-family: "NeoSansArabic" !important; */
}

.Appfooter {
  background-image: url("./footerBG.jpg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
}

.smallfooter .smallfooterYear {
  /* font-family: "NeoSansArabic"; */
  font-size: 13px;
  text-align: center;
  color: #abafbe;
}

.Appfooter ul {
  list-style: none;
  text-align: right;
  padding-right: 0 !important;
}

.Appfooter h4 {
  text-align: right;
  /* font-family: "NeoSansArabic"; */
  font-size: 18px;
  font-weight: bold;
  line-height: 2.28;
  color: #364464;
}

.Appfooter li,
.Appfooter a {
  /* font-family: "NeoSansArabic"; */
  font-size: 16px;
  line-height: 2.5;
  text-align: right;
  color: #83899f;
  text-decoration: none !important;
}

.Appfooter a:hover {
  /* color: #fff;
  background-color: #00726f;
  padding: 0 20px;
  text-shadow: none;
  transition: 1s; */
}

.footerTopI i {
  color: #83899f;
}

.footerTopI {
  text-align: left;
}

.Appfooter .conditions {
  background-color: #e7e9f1;
  padding: 10px;
  text-align: right;
}

.conditions h6 {
  font-size: 14px;
  font-weight: bold;
  line-height: 2.92;
  text-align: right;
  color: #5c6581;
  /* font-family: "NeoSansArabic"; */
}

.conditions p {
  font-size: 13px;
  line-height: 1.92;
  text-align: right;
  color: #83899f;
  /* font-family: "NeoSansArabic"; */
}

.conditions i,
.footIcon {
  color: #5c6581;
  padding: 0 5px;
  font-size: 30px !important;
}

.footerYear {
  /* font-family: "NeoSansArabic"; */
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: #364464;
}

.portalNavbar1 {
  box-shadow: 0 1px 0 0 #e7e9f1;
  background-color: #fbfcfe;
  height: 60px;
  padding-top: 0 !important;
  z-index: 120 !important;
}

.portalNavbar1 ul {
  list-style-type: none;
}

.portalNavbar1 i {
  font-size: 20px !important;
}

.leftUl {
  padding-left: 0 !important;
  padding-right: 5px !important;
  direction: ltr;
}

.portalNavbar1 li {
  display: inline-table;
  /* font-weight: 600; */
  /* font-family: "NeoSansArabic"; */
  font-size: 15px;
  /* padding-right: 20px; */
  line-height: 1.17;
  text-align: center;
  /* margin: 0 10px; */
  color: #364464 !important;
  letter-spacing: 1px;
  cursor: pointer;
  padding-top: 5px;
  padding-bottom: 5px;
}

.portalNavbar1 a {
  /* font-weight: 600; */
  /* font-family: "NeoSansArabic"; */
  font-size: 14px;
  line-height: 1.17;
  text-align: center;
  color: #364464 !important;
  letter-spacing: 1px;
  cursor: pointer;
}

.twitterIcon:hover {
  color: #1da1f2 !important;
}

.youtubeIcon:hover {
  color: #ff0000 !important;
}

.portalNavbar1 .navbar-nav {
  width: 100%;
  direction: rtl;
  display: block;
}

.appStoreUL li {
  display: inline;
}

.mobileLogo {
  float: right;
}

.AppfooterColLogo {
  text-align: right;
}

.footerYearContainer {
  background-image: url("./footerBG.jpg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
}

.leftUl li {
  padding-top: 15px;
}

.closeMenuIcon {
  position: absolute !important;
  left: 6px;
  top: 10px;
}

.drawerHeder {
  display: block !important;
}

.backBtn {
  display: grid !important;
  grid-template-columns: 1fr 2fr 1fr !important;
}

.editionNo {
  color: #e60000;
}

.dashboard-page-layout {
  /* width: 99vw; */
  display: flex;
  flex-direction: row;
  /* position: relative;
  height: 91vh; */
  direction: rtl;
  margin-top: 7vh;
  margin-bottom: 10vh;
}

#layotHiddenDrops {
  margin-top: 1vh;
}

.dashboard-page-layout .apexcharts-menu-item {
  direction: rtl;
}

.dashboard-page-layout
  .left-side-chart-container
  .apexcharts-menu-item.exportCSV,
.dashboard-page-layout .Columnchart1 .apexcharts-menu-item.exportCSV,
.dashModalZoom .apexcharts-menu-item.exportCSV,
#Columnchart1 .apexcharts-menu-item.exportCSV {
  display: none;
}

.dashboard-page-layout .tbl-beside-map {
  width: 24vw;
  display: flex;
  flex-direction: column;
  border: 2px solid rgb(183, 181, 181);
  overflow-y: auto;
}

.dashboard-page-layout .map-wrapper {
  display: flex;
  flex-direction: column;
}

.dashboard-page-layout .charts-layout-row {
  display: flex;
  flex-direction: row;
  width: 100%;
  /* height: 65%; */
  flex-wrap: wrap;
  align-self: stretch;
  text-align: center;
  justify-content: center;
}

.dashboard-page-layout .charts-layout-col {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex-wrap: wrap;
  align-self: stretch;
  text-align: center;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.dashboard-page-layout .charts-layout-row .normal-chart-item {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: space-evenly; */
  height: auto;
  padding: 5px;
  min-height: 30%;
}

.dashboard-page-layout .charts-layout-col .normal-chart-item {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  height: auto;
}

.dashboard-page-layout .charts-layout-col .single-chart-item {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  height: auto;
}

.dashboard-page-layout .charts-layout-col .normal-chart-item .ape-chart {
  width: 60%;
}

.last-chart-item.mapSquare .ape-chart,
.single-chart-item .ape-chart {
  width: 100%;
}

.last-chart-item.mapSquare .ape-chart {
  /* width: 85%; */
}

.last-chart-item.mapSquare .ape-chart.bar {
  /* width: 350px; */
}

.last-chart-item.mapSquare {
  overflow: scroll;
  height: 35%;
}

.dashboard-page-layout .last-chart-item {
  align-items: center;
  display: flex;
  /* justify-content: center; */
  flex-direction: column;
  height: auto;
  padding: 10px;
}

.dashboard-page-layout .last-chart-item.text-count-info {
  justify-content: center;
}

.charts-below-map {
  display: flex;
  align-content: center;
  flex-direction: row;
  justify-content: space-evenly;
  flex-wrap: wrap;
  flex-grow: 1;
  border: 2px solid rgb(183, 181, 181);
}

.left-side-chart-container {
  border: 2px solid rgb(183, 181, 181);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 26vw;
}

.apexcharts-tooltip.apexcharts-theme-light {
  direction: ltr;
}

#Columnchart1 .apexcharts-tooltip.apexcharts-theme-light {
  direction: rtl;
  text-align: center;
}

#Columnchart1 .apexcharts-tooltip-series-group.apexcharts-active {
  justify-content: space-around;
}

/* clear calendar icon*/
.clear-calendar-icon {
  cursor: no-drop;
  font-size: large;
  height: min-content;
  margin: auto;
  display: flex;
  position: absolute;
  top: 40%;
  color: cadetblue;
  width: 34px;
  flex-direction: row;
  justify-content: center;
}

.generalResultDetails .react-tabs__tab-list {
  overflow: auto !important;
}

.tableFiltersButtons .splitKrokyMetaSVG:hover {
  filter: invert(35%) sepia(84%) saturate(1302%) hue-rotate(166deg)
    brightness(94%) contrast(92%) !important;
}

.updaeContractImgClass {
  width: 25px !important;
  padding-top: 10px !important;
}

.select-year-month {
  /* width: 120px; */
  margin: 0 3px;
}

.identifyScreen div div .activeBtn {
  border: none !important;
  background-color: #b45333 !important;
}

.tooltipButton.InqueryTool {
  margin: 6px 0;
  /* padding: 0 10px !important; */
}

/* .tooltipButton.archiveIcon{
  padding-top: 1rem !important;
} */
.identifyScreen div div .activeBtn svg {
  color: #fff !important;
}

.identifyScreen div div .activeBtn:hover {
  color: #b45333;
  background-color: #b45333 !important;
}

.inqBTN,
.inqBTN:focus,
.inqBTN:hover {
  padding: 5px 10px !important;
}

.inqBTN svg {
  font-size: 17px !important;
}

/*map logo pointer*/
.map-pointer {
  cursor: pointer;
  width: 35px;
  margin: 4px;
}

.active-title {
  /* border: 2px dashed #f60101; */
  margin: 0.03em 0.25em;
}

/**********Navbar**************/
.portalNavbar1 {
  box-shadow: 0 1px 0 0 #e7e9f1;
  background-color: #fbfcfe;
  height: 60px;
}

.ant-dropdown-menu {
  background-color: rgba(51, 51, 51, 0.6) !important;
  backdrop-filter: blur(4px);
}

.ant-dropdown-menu span {
  color: #fff !important;
}

.ant-dropdown-menu span:hover {
  color: #b45333 !important;
}

.ant-dropdown-menu.ant-dropdown-menu-rtl {
  padding: 20px !important;
  border-radius: 10px !important;
}

.portalNavbar a:hover,
.portalNavbar1 a:hover {
  color: #00726f;
}

.ant-dropdown-menu-item:hover {
  background-color: transparent !important;
  color: #00726f !important;
}

.portalNavbar button,
.portalNavbar button:hover,
.portalNavbar button:active,
.portalNavbar button:focus {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  /* font-family: kufiBold; */
  color: #0b2548 !important;
  text-align: right !important;
  outline: none !important;
}

.portalNavbar1 .btn,
.portalNavbar .btn {
  padding: 0 !important;
}

.portalNavbar1 button,
.portalNavbar1 button:hover,
.portalNavbar1 button:active,
.portalNavbar1 button:focus {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  font-size: 12px !important;
  /* padding: 5px !important; */
  /* margin: 0 10px; */
  /* font-family: kufiBold; */
  color: #0b2548 !important;
  text-align: right !important;
  outline: none !important;
}

.portalNavbar {
  box-shadow: 0 2px 0 0 #e8e8ed;
  background-color: #f7fafd;
  margin-top: 60px;
  padding-top: 30px;
  z-index: 999 !important;
  padding-bottom: 30px;
}

.portalNavbar1 ul {
  list-style-type: none;
}

.portalNavbar1 i {
  font-size: 20px !important;
}

.portalNavbar1 li {
  display: inline-table;
  /* font-weight: 600; */
  /* font-family: "NeoSansArabic"; */
  font-size: 15px;
  line-height: 1.17;
  text-align: center;
  /* margin: 0 10px; */
  color: #364464 !important;
  letter-spacing: 1px;
  cursor: pointer;
  padding-top: 5px;
  padding-bottom: 5px;
}

.portalNavbar1 a {
  /* font-weight: 600; */
  /* font-family: "NeoSansArabic"; */
  font-size: 14px;
  line-height: 1.17;
  text-align: center;
  color: #364464 !important;
  letter-spacing: 1px;
  cursor: pointer;
}

.twitterIcon:hover {
  color: #1da1f2 !important;
}

.youtubeIcon:hover {
  color: #ff0000 !important;
}

.rightUl {
  position: absolute;
  right: 0;
  top: 10px !important;
}

.navitemBorder {
  content: "";
  width: 2px;
  height: 10px;
  background-color: #e8e8ed;
  padding: 4px 0.7px;
  margin: 0 15px;
  border-radius: 3px;
}

.LogoCenter {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 45%;
  text-align: center;
  font-size: 18px;
}

img[alt="logo"] {
  width: 50px !important;
  height: auto !important;
  filter: brightness(0) invert(1) !important;
}

img[alt="logo"].logoPrint {
  width: 100px !important;
}

#chartDiv .recharts-wrapper {
  margin: auto;
}

.dropdown-toggle::after {
  display: none !important;
}

.dropdown-toggle::before {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
  margin-right: 5px;
}

.ant-select-dropdown {
  text-align: right !important;
  /* font-family: "NeoSansArabic" !important; */
  /* background-color: rgb(66 65 65 / 50%); */
  /* background-color: rgb(66 65 65 / 80%); */
  background-color: rgb(66 65 65);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  overflow: visible !important;
  max-height: 200px;
  z-index: 9999 !important;
}

.SideMenu .ant-select-dropdown {
  max-height: 150px;
  overflow: visible !important;
  z-index: 9999 !important;
}

/* Fix for virtual list scrolling issues */
.ant-select-dropdown .rc-virtual-list {
  overflow: visible !important;
}

.ant-select-dropdown .rc-virtual-list-holder {
  overflow-y: auto !important;
  max-height: 200px !important;
}

.ant-select-dropdown .rc-virtual-list-holder-inner {
  position: relative !important;
}

.SideMenu .ant-select-dropdown .rc-virtual-list-holder {
  max-height: 150px !important;
}

/* .rc-virtual-list-holder-inner {
  transform: translateY(0px) !important;
} */
/* .rc-virtual-list-holder {
  overflow: auto !important;
} */
.rc-virtual-list-holder {
  overflow-y: auto !important;
  max-height: unset !important;
}

.rc-virtual-list-holder > div {
  overflow: auto !important;
  /* scrollbar-color: transparent transparent !important;
  scrollbar-width: none !important; */
}

.rc-virtual-list-scrollbar.rc-virtual-list-scrollbar-vertical {
  display: none !important;
}

.mynavitem {
  padding: 0 20px !important;
}

.nav-link,
.navitem,
.navitem a {
  color: #0b2548 !important;
  text-decoration: none;
  font-size: 16px;
  /* font-family: "NeoSansArabic"; */
}

.navitem:hover {
  color: #025358 !important;
  font-weight: bold;
}

.userDropDown {
  color: #025358 !important;
  text-decoration: none;
}

.navitem-active {
  /* border-bottom: 3px solid #00726f; */
  font-weight: bold !important;
  color: #00726f !important;
}

.navbar-brand {
  position: absolute;
  top: 0;
  right: 0;
}

.navbar-brand img {
  width: auto;
  height: 70px;
}

.searchHeader {
  background-color: #223973;
  height: 60px;
  top: 60px;
  position: absolute;
  width: 100%;
}

.searchHeader .headTitle {
  color: #fff;
  text-align: right;
  padding-top: 20px;
  padding-right: 25px;
  font-weight: bold;
}

.headCenter {
  background-color: #fff;
  top: 8px;
  left: 40%;
  position: absolute;
  border-radius: 5px !important;
}

.headCenter .ant-input {
  width: 400px;
  border: none !important;
}

.ant-picker-cell-inner::before {
  border-color: #b45333 !important;
}

/* .ant-radio-inner {
  background-color: #b45333 !important;
  border-color: #b45333 !important;
} */

.ant-radio-checked .ant-radio-inner {
  border-color: #b45333 !important;
  background-color: #b45333 !important;
}

.ant-radio-checked .ant-radio-inner::after {
  background-color: #fff !important;
}

.headBtnSearch {
  position: absolute !important;
  top: 8px !important;
  height: 45px !important;
  background: transparent !important;
  color: #fff !important;
  width: 100px !important;
  left: 33% !important;
  border-radius: 6px !important;
}

/*Archive page*/
.archivePage {
  /* font-family: "NeoSansArabic" !important; */
  overflow: hidden !important;
  height: 100vh;
}

.archiveData {
  margin-top: 120px;
}

.ant-tree {
  background: #f7fafd !important;
  /* height: 100vh; */
  border-left: 1px solid #e0e0e0 !important;
  overflow-y: scroll;
  max-height: 80vh;
}

.archiveData .ant-table {
  background-color: #f7fafd !important;
}

.archiveData .ant-tree input {
  height: 0 !important;
}

.ant-tree {
  height: 100%;
}

.ant-tree.ant-tree-directory .ant-tree-treenode {
  text-align: right !important;
  /* height: 32px !important; */
  margin-bottom: 8px;
}

.ant-tree-title {
  word-break: break-word;
}

.ant-tree-list-holder-inner {
  padding-top: 10px;
}

.ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {
  background: #d8e0f3 !important;
}

.ant-tree-title {
  color: #707070 !important;
  margin-right: 15px !important;
  font-size: 16px;
}

.anticon-folder-open,
.anticon-folder {
  font-size: 25px !important;
}

.anticon-folder-open svg,
.anticon-folder svg {
  color: #ffca28 !important;
}

.anticon-down-circle {
  pointer-events: none !important;
}

.ant-tree-switcher-icon svg {
  color: #707070 !important;
  font-size: 20px !important;
}

.ant-table-wrapper.ant-table-wrapper-rtl .ant-table-pagination-left {
  justify-content: center !important;
  margin-top: 20px;
}

.ant-table-column-sorters {
  /* display: block !important; */
  color: #707070 !important;
  display: flex !important;
  align-items: center;
  flex-direction: row-reverse;
  gap: 10px;
}

.ant-table-column-title {
  font-style: normal;
  font-weight: 700;
  font-size: 12px;
  line-height: 30px;
  margin-left: 10px !important;
  color: #707070;
  vertical-align: middle !important;
}

.ant-table-column-sorter {
  color: #707070 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ant-table-column-sorter-up,
.ant-table-column-sorter-down {
  font-size: 15px !important;
}

.ant-table-tbody > tr > td {
  color: #fff !important;
  font-style: normal;
  background-color: transparent !important;
  font-weight: 500 !important;
  font-size: 16px;
  line-height: 30px;
  text-align: right;
  border: none !important;
  border-inline-end: 1px solid #fff !important;
}

.tableBtn {
  font-style: normal;
  font-weight: bold !important;
  font-size: 14px;
  line-height: 27px;
  text-align: right;
  width: 80px !important;
  border: 1px solid #223973 !important;
  border-radius: 5px !important;
  color: #223973 !important;
  background-color: transparent !important;
  -webkit-border-radius: 5px !important;
  -moz-border-radius: 5px !important;
  -ms-border-radius: 5px !important;
  -o-border-radius: 5px !important;
}

.tableBtn:hover {
  background-color: #223973 !important;
  color: #fff !important;
}

.archiveData .ant-pagination-item-active a {
  color: #223973 !important;
}

.archiveData .ant-pagination-item-active {
  border-color: #223973 !important;
  border-radius: 4px !important;
}

.archiveFooter {
  background: #f7fafd;
  width: 100vw;
  position: absolute;
  bottom: 0;
}

.archiveFooter h6 {
  text-align: center;
  font-weight: 700;
  font-style: normal;
  padding: 15px;
  font-size: 16px;
  margin-bottom: 0 !important;
}

.archivePage .ant-table-content {
  padding-right: 22px;
}

.ant-table-wrapper::-webkit-scrollbar-thumb {
  background-color: #b45333 !important;
}

.ant-table-wrapper {
  overflow: scroll !important;
  height: 70vh !important;
}

.ant-select-item-option-content {
  direction: rtl !important;
  font-size: 0.8rem;
}

td {
  /* font-family: "arabicNums" !important; */
}

.rtl-direction {
  direction: rtl !important;
}

.ltr-direction {
  direction: ltr !important;
}

.exportPdfPage .ant-table-wrapper {
  height: auto !important;
  overflow: auto !important;
}

.sunburst-chart.planLandsStat {
  margin-top: 4rem;
}

.dashCalendarWithLabel .rmdp-container {
  margin-top: 30px;
}

.apexcharts-text tspan {
  font-size: 11px !important;
}

.printOnly {
  display: none;
}

@media print {
  .reportStyle2 {
    overflow: visible !important;
    padding: 1%;
    margin: 1%;
  }

  .dashDataTable table {
    page-break-inside: auto;
  }

  .dashDataTable table tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  .dashDataTable table thead {
    display: table-header-group;
  }

  .dashDataTable table tfoot {
    display: table-footer-group;
  }

  .printOnly {
    display: block;
  }

  .no-print {
    display: none;
  }

  td {
    color: #000 !important;
  }

  .dashDataPage h4 {
    float: unset;
    margin: 10px auto;
  }

  .reportRow {
    /* height: 90px;
          font-size: 30px; */
    display: grid;
    /* width: 100%; */
    direction: rtl;
    border: 1px solid #e4e4e4 !important;
    /* height: 30px; */
    white-space: nowrap;
    color: black;
    grid-gap: 5px;
    padding: 2px;
    text-align: center;
    grid-template-columns: 1fr 1fr;
    font-size: 17px;
    border-top: 1px solid;
    border-bottom: 1px solid;
  }

  .reportRow {
    border: 1px solid;
  }

  /* .dashDataTable td{
    font-size: 18px ;
  } */
}

.printNav {
  width: 100vw;
  height: 120px !important;
  background-color: #025358;
  position: relative;
}

.routesData {
  position: absolute;
  width: 99vw;
}

.faplusChart {
  margin-left: auto;
  cursor: pointer;
  color: #00619b;
}

.dashModalZoom .Columnchart1,
.dashModalZoom .donut-chart {
  max-width: unset !important;
}

#headerHidden {
  position: absolute;
  top: -10vh;
  width: 100%;
  background-color: transparent;
  transition: top 1s;
}

#headerShown {
  width: 100%;
  position: absolute;
  /* top: 55px; */
  z-index: 99 !important;
  transition: top 1s;
  padding: 0;
}

.dashLogosNav {
  position: relative;
  height: 11vh;
}

.dashLogosNav .openCloseArrow {
  position: absolute;
  top: 85%;
  left: 50%;
  color: #00619b;
  font-size: 20px;
  cursor: pointer;
  z-index: 999;
}

.dashLogosNav #closeArrow {
  top: 75%;
}

.Columnchart1 {
  margin-top: 2px !important;
}

body {
  overflow-x: hidden;
}

.ant-select-dropdown {
  /* width: fit-content !important; */
}

.white-color {
  color: white;
}

.text-center {
  text-align: center !important;
}

/*archive gallery modal*/
.archiveGalleryModal .ant-modal-content .ant-btn-primary {
  display: none;
}

.ant-modal-content {
  background-color: rgb(51 51 51 / 60%) !important;
}

/*archive gallery modal*/

.dash-bottom-border {
  border-bottom: dashed;
}

/* 
it is style of akar report
*/

.reportStyle2,
.reportStyle-Suggestion {
  /* border: solid; */
  direction: rtl;
  color: black;
  overflow: auto;
  height: 92vh;
}

.ant-slider-handle::after {
  box-shadow: 0 0 0 2px #9d4223 !important;
}

.ant-slider-rail {
  background-color: #fff !important;
}

.reportStyle2,
.reportStyle-Suggestion {
  /* font-family: "NeoSansArabic" !important; */
  background: url("./reportBG.png");
}

.investment_report_header {
  justify-self: center;
  align-self: center;
  text-align: center;
  width: 60%;
}

.investment_report_header h4 {
  font-size: 18px;
}

.underlineStyle {
  margin-top: 20px;
  box-shadow: 0 0 3px 1px black !important;
  text-align: center;
  padding: 5px;
  font-size: 20px;
}

.divBorder {
  display: grid;
  grid-template-rows: 5vh auto;
  align-items: center;
  padding: 5px;
  /* border: 1px solid; */
  box-shadow: 0 0 0.5px -0.5px black;
}

.reportRow {
  display: grid;
  /* width: 100%; */
  direction: rtl;
  box-shadow: 1px solid #dbdbdb !important;
  /* height: 30px; */
  white-space: nowrap;
  color: black;
  grid-gap: 5px;
  text-align: center;
  grid-template-columns: 1fr 1fr;
  font-size: 17px;
}

.reportRow div {
  direction: ltr;
}

.labelReport {
  width: 100%;
  border: 1px solid;
  font-size: 20px;
  text-align: center;
  background-color: bisque;
  background-color: #e8e2c6;
  box-shadow: 0 0 6px black;
  margin-top: 2%;
}

.footer-report-print {
  position: fixed;
  display: flex;
  justify-content: space-between;
  direction: ltr;
  padding: 5px;
  bottom: 0;
  width: 100%;
}

.scaleBarStyle {
  bottom: 5px;
  /* left: 20px; */
  left: 55px;
  position: absolute;
  z-index: 1;
}

.xyStyle {
  position: absolute;
  bottom: 0px;
  left: 205px;
  z-index: 1;
  text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff,
    1px 1px 0 #fff;
  font-size: 14px;
  font-weight: bold;
  color: #000 !important;
}

.esri-scale-bar__label {
  direction: rtl !important;
}

#smallMap {
  padding: 0;
  margin: 0;
  height: 100%;
  width: 100%;
}

.legendlst {
  display: grid;
  padding: 0%;
  grid-template-columns: 10px 20px auto;
  grid-gap: 10px;
  margin-bottom: 10px;
  margin-top: 10px;
}

.__floater {
  visibility: visible !important;
  opacity: 1 !important;
}

#react-joyride-portal {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  background-color: rgb(0 0 0 / 12%) !important;
}

.esri-widget__table tr th {
  text-align: right;
  font-weight: bold;
}

.esri-widget__table {
  direction: rtl;
}

.esri-widget__table tr td {
  text-align: right;
}

.esri-popup__header-buttons > div:first-child {
  display: none;
}

.esri-popup__content {
  display: block !important;
}

.swipelabelStyle {
  padding: 6px;
  border-radius: 16px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  z-index: 2;
  pointer-events: none;
  white-space: nowrap;
  background: #b45333;
  color: white;
}

.iframeliveCamera {
  border: 2px solid #ccc;
  border-radius: 8px;
  width: 100%;
  height: 420px;
}

.hovertooltip {
  position: absolute;
  background: white;
  padding: 5px;
  border-radius: 5px;
  font-size: 14px;
  border: 1px solid #ccc;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  pointer-events: none;
  display: none;
}

.ant-select-selection-item-remove {
  color: #fff !important;
}

.legendchildlst {
  margin-right: 20px;
}

.esri-basemap-gallery__item-title {
  unicode-bidi: plaintext !important;
}

.copmassStyle {
  bottom: 15px;
  /* right: 221px; */
  background-color: rgb(51 51 51 / 60%) !important;
  position: absolute;
  z-index: 1;
}

.esri-compass .esri-compass__icon {
  color: white !important;
}
