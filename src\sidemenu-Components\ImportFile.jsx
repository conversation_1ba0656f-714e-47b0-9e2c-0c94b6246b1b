import { useTranslation } from "react-i18next";
import ImportFiles from "./ImportFilesComponents/ImportFiles";
import MyFiles from "./ImportFilesComponents/MyFiles";
import tools from "../assets/images/sidemenu/tools.svg";
import maps from "../assets/images/sidemenu/maps.svg";
import { Tooltip } from "@mui/material";
import { Button } from "antd";
import usePersistentState from "../helper/usePersistentState";

const componentName = "ImportFile";
export default function ImportFile(props) {
  const { t } = useTranslation("common");

  /***
   * selected tab
   */
  const [selectedTab, setSelectedTab] = usePersistentState("selectedTab","import_files", componentName); // import_files - my_files

  return (
    <div
      className="import_file"
      style={{
        overflow: "auto",
        height: "calc(-85px + 100vh)",
        // paddingLeft: "10px",
        // paddingRight: "10px",
      }}
    >
      <div className="container">
        {/* start tabs */}
        <div
          className="tabs"
          style={{
            display: "flex",
            gap: "10px",
            marginBlock: "20px",
            alignItems: "center",
          }}
        >
          <Tooltip
            placement="top"
            title={t("tools")}
            className="MuiTooltipStyle"
          >
            <Button
              style={{
                background:
                  selectedTab === "import_files" ? "#F4DFD9" : "#EEE7E1",
                borderRadius: selectedTab === "import_files" ? "20px" : "50%",
                color: "#B55433",
                padding: "8px",
                border: "none",
                boxShadow: "none",
              }}
              onClick={() => setSelectedTab("import_files")}
            >
              <img
                src={tools}
                alt="import_files"
                style={{
                  filter:
                    selectedTab === "import_files"
                      ? "brightness(0) saturate(100%) invert(40%) sepia(9%) saturate(3593%) hue-rotate(329deg) brightness(97%) contrast(96%)"
                      : "",
                }}
              />
              {selectedTab === "import_files" && (
                <span>{t("import_files")}</span>
              )}
            </Button>
          </Tooltip>

          <Tooltip placement="top" title={t("my_files")}>
            <Button
              style={{
                background: selectedTab === "my_files" ? "#F4DFD9" : "#EEE7E1",
                borderRadius: selectedTab === "my_files" ? "20px" : "50%",
                padding: "8px",
                color: "#B55433",
                border: "none",
                boxShadow: "none",
              }}
              onClick={() => setSelectedTab("my_files")}
            >
              <img
                src={maps}
                alt="my_files"
                style={{
                  filter:
                    selectedTab === "my_files"
                      ? "brightness(0) saturate(100%) invert(40%) sepia(9%) saturate(3593%) hue-rotate(329deg) brightness(97%) contrast(96%)"
                      : "",
                }}
              />
              {selectedTab === "my_files" && <span>{t("my_files")}</span>}
            </Button>
          </Tooltip>
        </div>
        {/* end tabs */}

        {selectedTab === "import_files" ? (
          <ImportFiles map={props.map} setFile={props.setFile} />
        ) : (
          <MyFiles map={props.map} />
        )}
      </div>
    </div>
  );
}
