import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/material";
import { <PERSON><PERSON>, message } from "antd";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import tools from "../../assets/images/sidemenu/tools.svg";
import maps from "../../assets/images/sidemenu/maps.svg";
import study_areas from "../../assets/images/sidemenu/study-areas.svg";
import areas from "../../assets/images/sidemenu/areas.svg";
import InteractiveMapTools from "./InteractiveMapTools";
import InteractiveMapMaps from "./InteractiveMapMaps";
import InteractiveMapStudyAreas from "./InteractiveMapStudyAreas";
import InteractiveMapAreas from "./InteractiveMapAreas";
import axios from "axios";
import eventBus from "../../helper/EventBus";

export default function InteractiveMap(props) {
  const { t } = useTranslation("sidemenu");

  /***
   * selected tab
   */
  const [selectedTab, setSelectedTab] = useState("tools"); // tools - my_maps - study_areas - areas
  const [regionsData, setRegionsData] = useState([]);
  const [editDrawingData, setEditDrawingData] = useState(undefined);

  useEffect(() => {
    if (!editDrawingData && selectedTab == "tools") {
      getAllRegions();
    } else if (editDrawingData && selectedTab != "tools") {
      setEditDrawingData(undefined);
    }
  }, [selectedTab]);

  useEffect(() => {
    window.__speedFactor = 5;
    return () => {
      // resetMap();
      eventBus.dispatch("setShowInteractivEditBar", { message: false });
    };
  }, []);

  const resetMap = () => {
    props.map.view.goTo(window.__fullExtent);
    props.map.findLayerById("InteractiveMapGraphicLayer").graphics.removeAll();
  };

  const defaultMapView = () => {
    props.map.view.extent = window.__fullExtent;
    props.map.findLayerById("InteractiveMapGraphicLayer").graphics.removeAll();
  };

  const changeSelectedTab = (name) => {
    eventBus.dispatch("setShowInteractivEditBar", {
      message: name == "tools" ? true : false,
    });
    setSelectedTab(name);
  };

  const getAllRegions = async () => {
    await axios
      .get(`${window.ApiUrl}StudyRegion/GetAll?pageSize=100`)
      .then((response) => {
        let regions = response.data.results.map((region) => {
          return {
            id: region.id,
            name: region.name,
            zones: region.zones,
          };
        });
        setRegionsData([...regions]);
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  const drawSavedGraphicsRef = useRef();

  const calldrawSavedGraphicsRef = (drawing) => {
    if (drawSavedGraphicsRef.current) {
      drawSavedGraphicsRef.current(drawing);
    }
  };

  const [sliderValue, setSliderValue] = useState(1);

  const handleChangeSliderValue = (event, newValue) => {
    window.__speedFactor = newValue * 5;
    setSliderValue(newValue);
  };

  return (
    <div
      style={{
        paddingRight: "10px",
        paddingLeft: "10px",
        // height: "calc(100vh - 60px)",
        overflow: "auto",
      }}
      className="interactiveMap"
    >
      <div
        style={{ height: "1.5px", background: "#fff", marginBlock: "10px" }}
      />

      {/* start tabs */}
      <div
        className="tabs"
        style={{
          display: "flex",
          gap: "10px",
          marginBlock: "20px",
          alignItems: "center",
        }}
      >
        <Tooltip placement="top" title={t("tools")} className="MuiTooltipStyle">
          <Button
            style={{
              background: selectedTab === "tools" ? "#F4DFD9" : "#EEE7E1",
              borderRadius: selectedTab === "tools" ? "20px" : "50%",
              color: "#B55433",
              padding: "8px",
              border: "none",
              boxShadow: "none",
            }}
            onClick={() => changeSelectedTab("tools")}
          >
            <img
              src={tools}
              alt="tools"
              style={{
                filter:
                  selectedTab === "tools"
                    ? "brightness(0) saturate(100%) invert(40%) sepia(9%) saturate(3593%) hue-rotate(329deg) brightness(97%) contrast(96%)"
                    : "",
              }}
            />
            {selectedTab === "tools" && <span>{t("tools")}</span>}
          </Button>
        </Tooltip>

        <Tooltip placement="top" title={t("my_maps")}>
          <Button
            style={{
              background: selectedTab === "my_maps" ? "#F4DFD9" : "#EEE7E1",
              borderRadius: selectedTab === "my_maps" ? "20px" : "50%",
              padding: "8px",
              color: "#B55433",
              border: "none",
              boxShadow: "none",
            }}
            onClick={() => changeSelectedTab("my_maps")}
          >
            <img
              src={maps}
              alt="my_maps"
              style={{
                filter:
                  selectedTab === "my_maps"
                    ? "brightness(0) saturate(100%) invert(40%) sepia(9%) saturate(3593%) hue-rotate(329deg) brightness(97%) contrast(96%)"
                    : "",
              }}
            />
            {selectedTab === "my_maps" && <span>{t("my_maps")}</span>}
          </Button>
        </Tooltip>

        <Tooltip placement="top" title={t("study_areas")}>
          <Button
            style={{
              background: selectedTab === "study_areas" ? "#F4DFD9" : "#EEE7E1",
              borderRadius: selectedTab === "study_areas" ? "20px" : "50%",
              padding: "8px",
              color: "#B55433",
              border: "none",
              boxShadow: "none",
            }}
            onClick={() => changeSelectedTab("study_areas")}
          >
            <img
              src={study_areas}
              alt="study_areas"
              style={{
                filter:
                  selectedTab === "study_areas"
                    ? "brightness(0) saturate(100%) invert(40%) sepia(9%) saturate(3593%) hue-rotate(329deg) brightness(97%) contrast(96%)"
                    : "",
              }}
            />
            {selectedTab === "study_areas" && <span>{t("study_areas")}</span>}
          </Button>
        </Tooltip>

        <Tooltip placement="top" title={t("areas")}>
          <Button
            style={{
              background: selectedTab === "areas" ? "#F4DFD9" : "#EEE7E1",
              borderRadius: selectedTab === "areas" ? "20px" : "50%",
              padding: "8px",
              color: "#B55433",
              border: "none",
              boxShadow: "none",
            }}
            onClick={() => changeSelectedTab("areas")}
          >
            <img
              src={areas}
              alt="areas"
              style={{
                filter:
                  selectedTab === "areas"
                    ? "brightness(0) saturate(100%) invert(40%) sepia(9%) saturate(3593%) hue-rotate(329deg) brightness(97%) contrast(96%)"
                    : "",
              }}
            />
            {selectedTab === "areas" && <span>{t("areas")}</span>}
          </Button>
        </Tooltip>

        <Slider
          value={sliderValue}
          onChange={handleChangeSliderValue}
          min={0}
          max={5}
          step={1}
          marks
          valueLabelDisplay="auto"
          sx={{
            marginInline: "10px",
            "& .MuiSlider-thumb": {
              backgroundColor: "#b55433",
              "&:hover": {
                boxShadow: "none",
              },
            },
            "& .MuiSlider-track": {
              backgroundColor: "#b55433",
              borderColor: "transparent",
            },
            "& .MuiSlider-rail ": {
              backgroundColor: "#fff",
            },
            "& .MuiSlider-valueLabel": {
              backgroundColor: "#b55433",
              color: "white",
              padding: "5px 10px",
              borderRadius: "5px",
              fontStyle: "italic",
            },
          }}
        />
      </div>
      {/* end tabs */}

      {selectedTab === "tools" && (
        <InteractiveMapTools
          map={props.map}
          regionsData={regionsData}
          editDrawingData={editDrawingData}
          setEditDrawingData={setEditDrawingData}
          callDrawSavedGraphics={calldrawSavedGraphicsRef}
          defaultMapView={defaultMapView}
        />
      )}
      {selectedTab === "my_maps" && (
        <InteractiveMapMaps
          map={props.map}
          defaultMapView={defaultMapView}
          setSelectedTab={setSelectedTab}
          setEditDrawingData={setEditDrawingData}
          setDrawGraphicsRef={(func) => (drawSavedGraphicsRef.current = func)}
        />
      )}
      {selectedTab === "study_areas" && <InteractiveMapStudyAreas />}
      {selectedTab === "areas" && <InteractiveMapAreas map={props.map} />}
    </div>
  );
}
