import { Col, Row } from "antd";
import React, { useState, useEffect } from "react";
import { toArabic } from "arabic-digits";
import axios from "axios";
import {
  useSearchParams,
  useLocation,
  useParams,
  useNavigate,
} from "react-router-dom";
import { useTranslation } from "react-i18next";

import NavBar from "../../containers/NavBar";
import FilesTable from "./FilesTable";
import SearchHeader from "./SearchHeader";
import SideTree from "./SideTree";
import { getUniqeID, notificationMessage } from "../../helper/utilsFunc";
import Loader from "../../containers/Loader";
import { getFileSize } from "./helper";

export default function ArchivePage(props) {
  const [selectedNode, setSelectNode] = useState([]);
  const [archiveTreeElements, setArchiveTreeElements] = useState([]);
  const [archiveFiles, setArchiveFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [noSearchResult, setNoSearchResult] = useState(false);
  const [isGeoExplorer, setIsGeoExplorer] = React.useState(false);

  //ref
  const selectedNodeTreePrev = React.useRef();
  const { t } = useTranslation("common");
  let [urlSearchParams] = useSearchParams();
  const navigate = useNavigate();
  useEffect(() => {
    if (!props.user) window.open(window.hostURL + "/home/<USER>", "_self");
    else {
      let ProvinceName = urlSearchParams.get("ProvinceName");
      let DistrictName = urlSearchParams.get("DistrictName");
      let PlanNo = urlSearchParams.get("PlanNo");
      let LandNo = urlSearchParams.get("LandNo");
      // get from map - expolorer
      if (ProvinceName || DistrictName || PlanNo || LandNo) {
        let params = {};
        if (ProvinceName && !["null", "Null", "NULL"].includes(ProvinceName))
          params.ProvinceName = ProvinceName;
        if (DistrictName && !["null", "Null", "NULL"].includes(DistrictName))
          params.DistrictName = DistrictName;
        if (PlanNo && !["null", "Null", "NULL"].includes(PlanNo))
          params.PlanNo = PlanNo;
        if (LandNo && !["null", "Null", "NULL"].includes(LandNo))
          params.LandNo = LandNo;
        getArchiveTree(params);
      }
      //normal case in archive
      else getArchiveTree();
    }
    return () => {
      localStorage.removeItem("archiveParams");
    };
  }, []);
  const onSelectNode = async (keys, info) => {
    if (isGeoExplorer) setIsGeoExplorer(false);
    if (selectedNodeTreePrev.current !== info?.node?.Path) {
      setSelectNode([info?.node?.Path]);
      await handleGetFiles(info?.node?.Path);
      await getArchiveTree(null, null, info?.node?.Path, true);
      selectedNodeTreePrev.current = info?.node?.Path;
    }
  };
  const onSearchFilesOrFolders = (type, searchWord) => {
    if (isGeoExplorer) setIsGeoExplorer(false);
    selectedNodeTreePrev.current = "";
    if (searchWord && type === "file") {
      handleGetFiles(selectedNode, searchWord);
    } else if (searchWord && type === "folder") {
      getArchiveTree(null, searchWord, selectedNode);
      // setArchiveFiles([])
    } else if (!searchWord) {
      getArchiveTree(null, "", selectedNode);
      setSelectNode([]);
      setArchiveFiles([]);
      // if (selectedNode) handleGetFiles(selectedNode)
    }
  };

  const getArchiveTree = async (params, searchQuery, path, isSelectNode) => {
    try {
      setIsLoading(true);
      let url = window.archiveAPIUrl;
      if (params) {
        setIsGeoExplorer(true);
        url += `geo-explorer/?`;
        url += Object.entries(params)
          .map((item) => {
            return `${item[0]}=${item[1] ? item[1] : ""}`;
          })
          .join("&");
      } else if (searchQuery && path) {
        url += `folders/?searchQuery=${encodeURIComponent(
          searchQuery
        )}&path=${encodeURIComponent(path)}`;
      } else if (searchQuery && !path) {
        url += `folders/?searchQuery=${encodeURIComponent(searchQuery)}`;
      } else {
        url +=
          path && typeof path == "string"
            ? `/folders?path=${encodeURIComponent(path)}`
            : `/folders?`;
      }
      let optimizedUrl = url.replaceAll("\\", "/");

      let res = await axios.get(optimizedUrl, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });
      setIsLoading(false);

      if (params && !res?.data?.length) {
        // res = await axios.get(window.archiveAPIUrl + "/folders");
        setNoSearchResult(true);
        // setArchiveTreeElements(res.data);
      } else if (params && res?.data?.length) {
        let filteredData = [];
        if (props?.user?.is_workflow_admin === 1) filteredData = [...res?.data];
        else
          filteredData = res?.data.filter((item) => {
            let splitPath = item.Path.split("\\");
            return splitPath.find(
              (p) =>
                p.replaceAll(" ", "") ===
                props?.user?.departments?.name?.replaceAll(" ", "")
            );
          });
        setArchiveTreeElements(filteredData);
        handleGetFiles(filteredData[0]?.Path);
        setSelectNode([filteredData[0]?.Path]);
        setNoSearchResult(false);
      } else {
        if (!res?.data?.length && !isSelectNode) {
          if (archiveFiles.length)
            notificationMessage(
              t("archive:noResultsSelectFromTree", "", "bottomRight")
            );
          else setNoSearchResult(true);
        } else {
          setNoSearchResult(false);
        }
        if (res.data) {
          if (!searchQuery) {
            setArchiveTreeElements(res.data);
          } else {
            let filteredData = [...res.data];

            if (props?.user?.is_workflow_admin === 1)
              filteredData = [...res?.data];
            else if (props?.user?.departments)
              filteredData = res?.data.filter((item) => {
                let splitPath = item.Path.split("\\");
                return splitPath.find(
                  (p) =>
                    p.replaceAll(" ", "") ===
                    props?.user?.departments?.name?.replaceAll(" ", "")
                );
              });
            setArchiveTreeElements(filteredData);
            if (filteredData.length === 1)
              handleGetFiles(filteredData[0]?.Path);
            else setArchiveFiles([]);
            let pathsToSelect = filteredData.map((item) => item.Path);
            setSelectNode(pathsToSelect);
          }
        }
      }
    } catch (err) {
      console.log(err);
      if (err?.response?.status === 401) {
        notificationMessage(t("common:sessionFinished"));
        localStorage.removeItem("user");
        localStorage.removeItem("token");
        window.open(window.hostURL + "/home/<USER>", "_self");
      } else {
        notificationMessage(t("common:retrievError"));
      }
      setIsLoading(false);
    }
  };
  const handleGetFiles = async (path, searchQuery) => {
    let url = window.archiveAPIUrl + `/files/?`;
    if (path) url += `path=${encodeURIComponent(path)}&`;
    if (searchQuery) url += `searchQuery=${encodeURIComponent(searchQuery)}`;
    try {
      setIsLoading(true);
      let optimizedUrl = url.replaceAll("\\", "/");
      let res = await axios.get(optimizedUrl, {
        headers: { Authorization: `Bearer ${localStorage.getItem("token")}` },
      });
      setIsLoading(false);

      if (res?.data?.length) {
        let filteredData = [];
        if (props?.user?.is_workflow_admin === 1) filteredData = [...res?.data];
        else
          filteredData = res?.data.filter((item) => {
            let splitPath = item.Path.split("\\");
            return splitPath.find(
              (p) =>
                p.replaceAll(" ", "") ===
                props?.user?.departments?.name?.replaceAll(" ", "")
            );
          });
        setArchiveFiles(
          filteredData?.map((item) => {
            let copiedItem = { ...item };
            copiedItem.Extension = copiedItem?.Extension?.split(".")[1];
            copiedItem.SizeInBytes = getFileSize(
              parseFloat(copiedItem.SizeInBytes)
            );
            return { ...copiedItem, key: getUniqeID() };
          })
        );
        if (filteredData.length && searchQuery) {
          let pathsToDrawTree = filteredData.map((item) => {
            item.Path = item.Path.split("\\" + item.Name)[0];
            return item;
          });
          let pathsToSelect = pathsToDrawTree.map((item) => item.Path);
          setArchiveTreeElements(pathsToDrawTree);
          setSelectNode(pathsToSelect);
        } else if (searchQuery) setSelectNode([]);
        setNoSearchResult(false);
      } else {
        setNoSearchResult(true);
        setArchiveFiles([]);
      }
    } catch (err) {
      if (err?.response?.status === 401) {
        notificationMessage(t("common:sessionFinished"));
        localStorage.removeItem("user");
        localStorage.removeItem("token");
        window.open(window.hostURL + "/home/<USER>", "_self");
      } else {
        notificationMessage(t("common:retrievError"));
      }
      setIsLoading(false);
    }
  };
  return (
    <div className="archivePage">
      <NavBar setMainData={props.setMainData} />
      <SearchHeader
        onSearchFilesOrFolders={onSearchFilesOrFolders}
        isGeoExplorer={isGeoExplorer}
        archiveTreeElements={archiveTreeElements}
        selectedNode={selectedNode}
      />
      {isLoading ? <Loader /> : null}
      <Row className="archiveData">
        {props?.user &&
          (props?.user?.is_workflow_admin === 1 ||
            props?.user?.departments) && (
            <>
              <Col span={6}>
                <SideTree
                  user={props.user}
                  onSelectNode={onSelectNode}
                  archiveTreeElements={archiveTreeElements}
                  selectedNode={selectedNode}
                />
              </Col>
              <Col span={18}>
                <FilesTable
                  noSearchResult={noSearchResult}
                  isGeoExplorer={isGeoExplorer}
                  selectedNode={selectedNode}
                  archiveFiles={archiveFiles}
                  setArchiveFiles={setArchiveFiles}
                />
              </Col>
            </>
          )}
      </Row>
      <footer className="archiveFooter">
        <h6>
          جميع الحقوق محفوظة - لأمانة المنطقة الشرقية &nbsp;
          {toArabic(new Date().getFullYear())}
        </h6>
      </footer>
    </div>
  );
}
