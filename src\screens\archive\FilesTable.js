import React from "react";
import { <PERSON><PERSON>, Space, Table } from "antd";
import { useTranslation } from "react-i18next";
import FileSaver from "file-saver";
import TableModal from "./TableModal";

export default function FilesTable(props) {
  const { t } = useTranslation('common', 'map', 'archive')
  const [modalShow, setModalShow] = React.useState(null);
  const openModal = (id) => {
    setModalShow(id);
  };
  const closeModal = () => {
    setModalShow(null);
  };

  //todo: try this package
  //https://www.npmjs.com/package/react-file-viewer
  const handleDownloadPreview = (record, isPreview) => {
    if (!isPreview || (isPreview && !['pdf', 'png', 'jpeg', 'jpg', 'gif', 'txt'].includes(record?.Extension?.toLowerCase()))) {
      let path = record?.Path;
      let downloadFilePath = window?.archiveFilesUrl + path.replaceAll("\\", "/");
      let fileName = `${record?.Name||'downloadedFile'}.` + (record?.Extension).toLowerCase(); // Set the file name.
      FileSaver.saveAs(downloadFilePath, fileName);
    } else {
      let path = record?.Path;
      let previewFilePath = window?.archiveFilesUrl + path.replaceAll("\\", "/");
      window.open(previewFilePath,'_blank');
    }
  }


  const columns = [
    {
      title: t("archive:fileName"),
      dataIndex: "Name",
      sorter: {
        compare: (a, b) => (a.Name || '').localeCompare(b.Name || ''),
        // multiple: 3,
      },
    },
    {
      title: t("archive:createdOn"),
      dataIndex: "CreatedOn",
      sorter: {
        compare: (a, b) => (a.CreatedOn || '').localeCompare(b.CreatedOn || ''),
        // multiple: 3,
      },
    },
    {
      title: t("archive:fileType"),
      dataIndex: "Extension",

      sorter: {
        compare: (a, b) => (a.Extension || '').localeCompare(b.Extension || ''),
        // multiple: 2,
      },
    },
    {
      title: t("archive:fileSizeInKiloBytes"),
      dataIndex: "SizeInBytes",
      render:(text)=>{
        return (text?.size?.toFixed(2)) +" "+t(`archive:${text?.type}`)
      },
      sorter: {
        compare: (a, b) => a.SizeInBytes.size - b.SizeInBytes.size,
        multiple: 1,
      },
    },
    {
      title: "",
      key: "action",
      render: (_, record) => (
        <Space size="middle">
          <Button className="tableBtn" onClick={() => handleDownloadPreview(record)}>تحميل</Button>
          <Button
            onClick={() => handleDownloadPreview(record, true)}
            id={record.key}
            className="tableBtn">
            {t("archive:open")}
          </Button>
          <TableModal
            modalShow={modalShow}
            rowID={record.key}
            record={record}
            openModal={openModal}
            closeModal={closeModal}
          />
        </Space>
      ),
    },
  ];
  return (
    <div>
      <Table columns={columns}
        locale={{

          emptyText: (
            <h4 style={{ textAlign: "center" }}>{
              (props.noSearchResult&& props.isGeoExplorer)?t('archive:noFiledRelatedToFile'):
              props.noSearchResult?t("archive:noResultsSelectFromTree"):
              t("archive:selectFromTree")} </h4>
          ),

          triggerDesc: t("map:clickDescSort"),
          triggerAsc: t("map:clickAscSort"),
          cancelSort: t("map:cancelSort"),
        }}
        dataSource={props?.archiveFiles} />
    </div>
  );
}
