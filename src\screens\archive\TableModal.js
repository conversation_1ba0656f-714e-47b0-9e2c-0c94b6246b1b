import { Mo<PERSON>, But<PERSON> } from "react-bootstrap";
import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";

export default function TableModal({
  modalShow,
  rowID,
  openModal,
  closeModal,
  record
}) {
  return (
    <Modal show={modalShow == rowID} onHide={closeModal}
      size='lg'
      centered

    >
      <Modal.Header>
        <FontAwesomeIcon icon={faTimes} onClick={closeModal} style={{ cursor: "pointer" }} />
      </Modal.Header>
      <Modal.Body>
        <iframe className='png' width="100%" height="600"
          frameborder="0"
          src={`${window.archiveFilesUrl + record.Path.toLowerCase()}`}></iframe>

      </Modal.Body>
      <Modal.Footer></Modal.Footer>
    </Modal>
  );
}
