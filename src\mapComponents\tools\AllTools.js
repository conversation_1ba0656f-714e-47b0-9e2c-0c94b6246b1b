import React from "react";
import GoogleMaps from "./GoogleMaps";
import InquiryTool from "./InquiryTool";
import LayersMenu from "./LayersMenu";
import SmallMap from "./SmallMap";
import Traffic from "./Traffic";
import ToolsMenu from "./ToolsMenu";
import BaseMap from "./BaseMap";
import CompareLayers from "./CompareLayers";
import MyLocation from "./MyLocation";
import Print from "./Print";
// import TocComponent from "./TocComponent/index"

export default function AllTools(props) {
  return (
    <div className="allToolsPage">
      {props.activeTool === "menu" ? (
        <ToolsMenu
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "inquiry" ? (
        props.map && (
          <InquiryTool
            map={props.map}
            mainData={props.mainData}
            languageState={props.languageState}
            setPopupInfo={props.setPopupInfo}
            popupInfo={props.popupInfo}
            activeTool={props.activeTool}
            closeToolsData={props.closeToolsData}
            openToolsData={props.openToolsData}
            openToolData={props.openToolData}
          />
        )
      ) : props.activeTool === "smallMap" ? (
        <SmallMap
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "generalSiteMap" ? (
        <MyLocation
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "layersMenu" ? (
        <LayersMenu
          languageState={props.languageState}
          mainData={props.mainData}
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "compareLayers" ? (
        <CompareLayers
          languageState={props.languageState}
          mainData={props.mainData}
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "googleMaps" ? (
        <GoogleMaps
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "print" ? (
        <Print
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "traffic" ? (
        <Traffic
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "Basemap" ? (
        <BaseMap map={props.map} closeToolsData={props.closeToolsData}  
        languageState={props.languageState}/>
      ) : null}
    </div>
  );
}
