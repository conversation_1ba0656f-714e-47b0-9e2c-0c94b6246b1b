import React, { Component } from "react";
import { LayerComponent, LayerInputProvider } from "./layer_component";
import { Slider } from "antd";

import * as watchUtils from "@arcgis/core/core/watchUtils";
// import { layersSetting } from "../../../helper/layers";
import { withTranslation } from "react-i18next";
import i18n from "../../../i18n";

class TocComponent extends Component {
  state = {};

  constructor(props) {
    super(props);

    this.rootLayers = props.map.__mapInfo.info.mapInfo.layers.filter(
      (layer) => layer.parentLayerId == -1
    );

    this.childLayers = props.map.__mapInfo.info.mapInfo.layers.filter(
      (layer) => layer.parentLayerId != -1
    );

    this.state = {
      layers: props.map.__mapInfo.info.mapInfo.layers,
      // layers: this.rootLayers,
    };
  }

  startOperation() {
    this.setState({
      showPopUp: !this.state.showPopUp,
    });
  }

  componentDidMount() {
    var self = this;
    let { layers } = this.state;
    watchUtils.when(this.props.map.view, "stationary", (evt) => {
      if (evt) {
        var mapScale = this.props.map.view.scale;
        // visable layers in thier scale

        //layers.$legends.forEach((layer) => {
        layers.forEach((layer) => {
          let minScale = layer.minScale;
          let maxScale = layer.maxScale;

          if (
            (maxScale <= mapScale || maxScale == 0) &&
            (minScale >= mapScale || minScale == 0)
          ) {
            layer.disable = "enableLabel";
          } else {
            layer.disable = "disableLabel";
          }
        });

        self.setState({ layers });
      }
    });
  }

  onSliderChange = (value) => {
    this.props.map.findLayerById("baseMap").opacity = value / 100;
  };

  getRootParent = (tocLayer) => {
    let parentExists = true;
    let parentLayer;
    //const AllLayers = this.state.layers;
    let layer = this.state.layers.find((lay) => lay.name == tocLayer.name);
    while (parentExists) {
      parentLayer = this.state.layers.find(
        (lay) => lay.id === layer.parentLayerId
      );
      if (parentLayer.parentLayerId !== -1) {
        layer = parentLayer;
      } else {
        parentExists = false;
      }
    }
    return parentLayer;
  };

  render() {
    const { t } = this.props;

    const AllLayers = this.state.layers;
    const rootLayers = this.rootLayers;
    const tocLayerNames = Object.keys(this.props.mainData.tocLayers);
    const filteredRootLayers = rootLayers.filter((layer) =>
      tocLayerNames.includes(layer.name)
    );

    const childTocLayers = this.childLayers.filter((layer) =>
      tocLayerNames.includes(layer.name)
    );

    // for testing

    // const childTocLayers = ["MANGROVE", "SEA_GRASS", "WET_LAND", "ECO_REGION"];

    if (childTocLayers.length !== 0) {
      childTocLayers.forEach((lay) => {
        let rootParent = this.getRootParent(lay);
        let existedRoot = filteredRootLayers.find(
          (root) => root.name == rootParent.name
        );
        if (!existedRoot) filteredRootLayers.push(rootParent);
      });
    }
    return (
      <section className="toc" style={{ overflow: "hidden" }}>
        <div>
          <div style={{ position: "sticky", top: "0" }}>
            <label style={{ marginRight: "10px", marginBottom: 0 }}>
              {t("Transparency")}
            </label>
            <div
              style={{
                paddingLeft: "8px",
                paddingRight: "8px",
              }}
            >
              <Slider
                defaultValue={100}
                onChange={this.onSliderChange}
                trackStyle={{ backgroundColor: "#9d4223" }} // Change to project primary color
                handleStyle={{
                  borderColor: "#9d4223", // Change to project primary color
                  backgroundColor: "#9d4223", // Change to project primary color
                }}
              />
            </div>
          </div>
          <ul style={{ overflow: "auto", height: "280px" }}>
            {filteredRootLayers.map((layer, key) => {
              return (
                <li
                  style={{
                    direction: i18n.language === "ar" ? "rtl" : "ltr",
                  }}
                  className="layers_li"
                >
                  <LayerInputProvider>
                    <LayerComponent
                      mainData={this.props.mainData}
                      map={this.props.map}
                      languageState={this.props.languageState}
                      childLayers={this.childLayers}
                      legends={this.props.map.__mapInfo.info.$legends}
                      AllLayers={AllLayers}
                      galleryHead
                      {...{
                        layer,
                        // changeLayer: this.changeLayer.bind(this, layer, key),
                        // zoomToLayer: this.zoomToLayer.bind(this, layer, key),
                        // expand: this.expand.bind(this, layer, key),
                      }}
                    />
                  </LayerInputProvider>
                </li>
              );
            })}
          </ul>
          {/* <ul style={{ padding: "5px" }}>
            {legends
              .filter((lay) =>
                Object.keys(this.props.mainData.tocLayers).includes(
                  lay.layerName
                )
              )
              .filter(
                (lay) =>
                  !lay.isHidden && this.props.mainData.tocLayers[lay?.layerName]
              )
              .map((layer, key) => {
                return (
                  <li
                    style={{
                      direction: i18n.language === "ar" ? "rtl" : "ltr",
                    }}
                  >
                    <LayerComponent
                      mainData={this.props.mainData}
                      languageState={this.props.languageState}
                      galleryHead
                      {...{
                        layer,
                        changeLayer: this.changeLayer.bind(this, layer, key),
                        zoomToLayer: this.zoomToLayer.bind(this, layer, key),
                        expand: this.expand.bind(this, layer, key),
                      }}
                    />
                  </li>
                );
              })}
          </ul> */}
        </div>
      </section>
    );
  }
}
export default withTranslation("common")(TocComponent);
