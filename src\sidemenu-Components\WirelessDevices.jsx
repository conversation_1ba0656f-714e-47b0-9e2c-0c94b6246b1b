import { Button } from "antd";
import play_icon from "../assets/images/sidemenu/mdi_play.svg";
import { useTranslation } from "react-i18next";
import call_icon from "../assets/images/sidemenu/material-symbols_call.svg";

export default function WirelessDevices() {
  const { t } = useTranslation("layersmenu");

  return (
    <div>
      <div
        style={{ height: "1.5px", background: "#fff", marginBlock: "10px" }}
      />

      <div style={{ overflow: "auto", maxHeight: "85vh" }}>
        {[...new Array(3)].map(() => {
          return (
            <div
              className="generalSearchCard"
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "10px",
                alignItems: "flex-start",
                padding: "15px",
              }}
            >
              <div
                style={{ display: "flex", gap: "10px", alignItems: "start" }}
              >
                <img src={call_icon} alt="" style={{ marginTop: "5px" }} />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "10px",
                  }}
                >
                  <div>{t("device_number")} : 123456</div>
                  <div>{t("date")} : 1/2/2024</div>
                </div>
              </div>

              <Button
                style={{
                  borderRadius: "20px",
                  padding: "20px",
                  backgroundColor: "#b45333",
                  border: "none",
                  color: "#fff",
                  width: "100%",
                }}
              >
                <img src={play_icon} alt="" />
                {t("run_record")}
              </Button>
            </div>
          );
        })}
      </div>
    </div>
  );
}
