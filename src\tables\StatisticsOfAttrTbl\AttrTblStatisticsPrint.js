import React, { useEffect, useRef, useState } from "react";
//import { useNavigate } from "react-router-dom";
import moment from "moment-hijri";
import { toArabic } from "arabic-digits";
//import Sunburst from "sunburst-chart";
import { Table } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPrint } from "@fortawesome/free-solid-svg-icons";
import logoPrint from "../../assets/images/logo.svg";
import generateRandomColor from "../../helper/utilsFunc";
import Sunburst from "sunburst-chart";
// import { getFromEsriRequest, showLoading } from "../../helper/common_func";
// import generateRandomColor, {
//   notificationMessage,
// } from "../../helper/utilsFunc";

// import { getStatisticsForChart } from "../tableFunctions";
// import { layersSetting } from "../../helper/layers";
import LoadingComponent from "../../screens/LoadingComponent";
import { useTranslation } from "react-i18next";
import PieChartComponent from "./PieChartComponent";
import { typeOf } from "hijri-date-picker";
import { getLayerId, queryTask } from "../../helper/common_func";

export default function AttrTblStatisticsPrint(props) {
  const { t, i18n } = useTranslation("print");
  const [chartData, setChartData] = useState([]);
  const [layerData, setLayerData] = useState([]);
  const [totalCount, setTotalCount] = useState();
  const [statisticsFieldName, setStatisticsFieldName] = useState();
  const [statColorsTable, setStatColorsTable] = useState([]);
  const sunBurstChartRef = useRef(null);

  useEffect(() => {
    let DataForChart = localStorage.getItem("ChartData");
    let dataForChartInJson = JSON.parse(DataForChart);
    let ColorsForChart = localStorage.getItem("ChartColors");
    let colorsForChartInJson = JSON.parse(ColorsForChart);
    let layerDataForChart = localStorage.getItem("layerData");
    let layerDataForChartInJson = JSON.parse(layerDataForChart);
    let statisticsFieldForChart = localStorage.getItem("statisticsFieldName");
    let statisticsFieldForChartInJson = JSON.parse(statisticsFieldForChart);
    let statisticstotalCountForChart = localStorage.getItem("totalCount");

    setChartData(dataForChartInJson);
    setStatColorsTable(colorsForChartInJson);
    setLayerData(layerDataForChartInJson);
    setStatisticsFieldName(statisticsFieldForChartInJson);
    setTotalCount(Number(statisticstotalCountForChart));

    if (layerDataForChartInJson.layerName == "SPECIE") {
      let sunBurstData = [];
      let layerId = localStorage.getItem("layerId");
      let queryPromises = dataForChartInJson.map((item, index) => {
        return new Promise((resolve) => {
          queryTask({
            url: window.mapUrl + "/" + layerId,
            where: `${layerDataForChartInJson.layerMetadata.appliedStatisticsField.name}='${item.name}'`,
            groupByFields: ["SPECIE_ALIAS"],
            statistics: [
              {
                type: "count",
                field: "SPECIE_ALIAS",
                name: "SPECIE_COUNT",
              },
            ],
            callbackResult: ({ features }) => {
              let childs = features
                .filter((item) => item.attributes.SPECIE_ALIAS)
                .map((feat) => ({
                  name: feat.attributes.SPECIE_ALIAS,
                  value: feat.attributes.SPECIE_COUNT,
                  color: generateRandomColor(),
                  size: feat.attributes.SPECIE_COUNT,
                }));

              let childrenObject = {
                name: item.name,
                value: item.value,
                color: colorsForChartInJson[index],
                children: childs,
                size: item.value,
              };

              sunBurstData.push(childrenObject);
              resolve();
            },
            callbackError: (error) => {
              console.error("Error in queryTask:", error);
              resolve();
            },
          });
        });
      });

      Promise.all(queryPromises).then(() => {
        let data = {
          name: t("totalNumber"),
          color: "#65DD91",
          children: sunBurstData,
        };
        let chartData = sunBurstData.map((item) => {
          return {
            name: item.name,
            value: item.value,
          };
        });

        let childrenData = sunBurstData.flatMap((obj) =>
          obj.children.map((item) => {
            return {
              name: item.name,
              value: item.value,
            };
          })
        );

        let chartColors = sunBurstData.map((item) => item.color);
        let childColors = sunBurstData.flatMap((obj) =>
          obj.children.map((item) => item.color)
        );

        chartData.push(...childrenData);
        chartColors.push(...childColors);

        setChartData(chartData);
        setStatColorsTable(chartColors);
        Sunburst()
          .data(data)
          .label("name")
          .size("size")
          .color("color")
          .showLabels(false)
          .width(400)
          .radiusScaleExponent(0.8)
          .tooltipContent((d, node) => `<i>${node.value}</i>`)(
          sunBurstChartRef.current
        );
      });
    }
  }, []);
  return (
    <div className="exportPdfPage">
      <LoadingComponent />

      <button
        className="SearchBtn mt-3 w-25"
        size="large"
        htmlType="submit"
        onClick={() => {
          window.print();
        }}
      >
        <FontAwesomeIcon
          icon={faPrint}
          className="mx-2"
          style={{ fontSize: "15px" }}
        />
        {t("print")}
      </button>
      <div
        style={{
          borderBottom: "solid",
          marginBottom: "3em",
        }}
      >
        <ul className="exportPdful">
          <li>
            <img alt="logo" src={logoPrint} className="logoPrint" />
          </li>
          <li className="exportPdfRightLi mt-4 mr-3">
            <h6>{t("sudiArabia")}</h6>
            <h6>{t("wildlife")}</h6>{" "}
          </li>
          <li className="mt-4">
            <h6 style={{ fontSize: "18px", marginBottom: "7px" }}>
              {t("Geograph")}{" "}
            </h6>

            <h6> {t("firstEdition")} </h6>
          </li>
        </ul>
        <p style={{ paddingLeft: "30px" }}>
          {i18n.language == "en"
            ? moment().format("iD/iM/iYYYY")
            : toArabic(moment().format("iYYYY/iM/iD"))}
        </p>
      </div>
      <div style={{ direction: "rtl", textAlign: "center" }}>
        {statColorsTable && (
          <div>
            <Table className="table table-bordered">
              <thead>
                <th>{t("statement")}</th>

                <th> {t("drawKey")} </th>
              </thead>

              <tbody>
                {statColorsTable.map((s, index) => (
                  <tr key={index}>
                    <td>{chartData[index].name}</td>
                    <td>
                      <p
                        className="colorBall"
                        style={{
                          background: s,
                          width: "20px",
                          height: "20px",
                          padding: "10px",
                          borderRadius: "50%",
                          margin: "auto",
                        }}
                      ></p>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
        )}

        {layerData && (
          <div className="m-4">
            <h2 className="text-center">
              {layerData.layerMetadata ? layerData.layerMetadata.arname : ""}
            </h2>
          </div>
        )}
        {chartData && layerData && layerData.layerName != "SPECIE" && (
          <div>
            <PieChartComponent
              chartData={chartData}
              chartColors={statColorsTable}
              setHoverData={() => {}}
            />
          </div>
        )}
        {chartData && layerData && layerData.layerName == "SPECIE" && (
          <div ref={sunBurstChartRef}></div>
        )}
        {chartData.length ? (
          <Table className="table table-bordered">
            <thead>
              <th> {t(statisticsFieldName)}</th>
              <th>{t("totalNumber")}</th>
              {layerData && layerData.layerName != "SPECIE" && (
                <th>{t("ratio")}</th>
              )}
            </thead>

            <tbody>
              {chartData.map((item, index) => (
                <tr key={index}>
                  <td>{item.name}</td>
                  <td>{item.value}</td>
                  {layerData && layerData.layerName != "SPECIE" && (
                    <td>
                      {" "}
                      {parseFloat(
                        ((item.value / Number(totalCount)) * 100).toFixed(2)
                      )}{" "}
                      %{" "}
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </Table>
        ) : null}
      </div>
    </div>
  );
}
