import React, { useState, useEffect, useRef } from "react";
import {
  Button,
  // Tooltip,
  Row,
  Col,
  Input,
  Table,
  Space,
  Select,
  Form,
  Modal,
  message,
  Switch,
} from "antd";
import { toArabic } from "arabic-digits";
import axios from "axios";
import moment from "moment";
import { Container, ModalHeader } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import { FaMapMarkerAlt } from "react-icons/fa";
import { GrGallery } from "react-icons/gr";
import { IoCloseSharp, IoSearch } from "react-icons/io5";
import {
  MdKeyboardDoubleArrowLeft,
  MdKeyboardDoubleArrowRight,
} from "react-icons/md";
import { useNavigate } from "react-router-dom";

import HijriDatePicker from "../components/hijriDatePicker/components/HijriDatePicker";
import {
  getStatisticsForChart,
  getStatisticsForFeatsLayer,
  getSubtypes,
  wrappedColRender,
} from "./tableFunctions";
import PaginationComp from "./TablePagination/paginationComp";
import { DownCircleFilled } from "@ant-design/icons";
import { SearchOutlined } from "@ant-design/icons";
import {
  faChevronCircleRight,
  faChevronCircleLeft,
  faExpandArrowsAlt,
  faFilter,
  faSearchPlus,
  faChartPie,
  faAddressBook,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { Tooltip } from "@mui/material";
import ExportFilesComp from "./ExportsFeatures/ExportFilesComp";
import FilteRModal from "./FiltersComps/FilterByAttr/FilterModal";
import GalleryModal from "./Modals/GalleryModal";
import PlanDataModal from "./Modals/PlanLandsData/PlanDataModal";
import MetaDataStatistics from "./StatisticsOfAttrTbl/MetaDataStatistics";
//common_func
import {
  queryTask,
  getLayerId,
  getFeatureDomainName,
  showLoading,
  zoomToFeatureDefault,
  clearGraphics,
  highlightFeature,
  drawLine,
  clearCanvasLine,
  convertHirjiDateToTimeSpan,
  convertToArabic,
  checkIfHiddenField,
  getFieldsMediaUrls,
  navigateToGoogle,
  convertTimeStampToDate,
  removeHighlightedAreasWithDimming,
  highlightAreaWithDimming,
} from "../helper/common_func";
import {
  PARCEL_LANDS_LAYER_NAME,
  externalBtnsForTblData,
} from "../helper/constants";
import { randomPlanNumbers } from "../helper/layers";
import {
  convertNumbersToEnglish,
  isNumber,
  notificationMessage,
  showDataSplittedBySlash,
  getDateFromConcatNumbers,
} from "../helper/utilsFunc";
import { RiArrowDropDownFill } from "react-icons/ri";
import GregorianDatePicker from "../components/hijriDatePicker/components/GregorianDatePicker";

// const { Column } = Table;
export default function SearchByMetaData(props) {
  const { i18n, t } = useTranslation("common", "map", "layers");
  const navigate = useNavigate();
  const [planDataModal, setPlanDataModal] = React.useState();

  //for pagination
  const [currentPageNum, setCurrentPageNum] = useState({
    current: 1,
    dep: 1,
  });
  const [layersNames, setLayersNames] = useState([]);
  //these are filtered layer names on the right list by searching (write a word)
  const [searchText, setSearchLayerNameText] = useState("");
  const [displayedLinks, setDisplayedLinks] = useState([]);
  //main layer data
  const [tableData, setTableData] = useState([]);
  const [tblColumns, setTblColumns] = useState([]);
  //dependencties data
  const [depTableData, setDepTableData] = useState({
    data: [],
    layerData: "",
    layerMetadata: {},
  });
  const [depTblColumns, setDepTblColumns] = useState();

  const [activeLayer, setActiveLayer] = useState({
    value: "", //layerName
    layerData: "",
  });

  const [showFilterModal, seFilteRModal] = useState(false);
  const [sideDisplay, setSide] = useState(true);

  //for filters
  const filterInput = useRef(null);
  // filter in table column
  const [colFilterWhere, setColFilterWhere] = useState({
    current: [],
    dep: [],
  });
  //filter by attribute
  const filterWhereClauseRef = useRef({
    current: "1=1",
    dep: {
      default: "",
      filtered: "",
    },
  });
  //Count and Area
  let countNumberRef = useRef({});
  let totalAreaRef = useRef({});
  const [countNumber, setCountNumber] = useState({
    current: 0,
    dep: 0,
  });
  const [totalArea, setTotalArea] = useState({
    current: null,
    dep: null,
  });
  const [currentTotalArea, setCurrentTotalArea] = useState(null);
  // flag to display statistics screen for features on table for land parcel layer only
  const [showMetaStat, setMetaStat] = useState(false);

  const [openGalleryModal, setOpenGalleryModal] = useState(false);
  const [galleryData, setGalleryData] = useState(undefined);
  const [showAllInfoTableData, setShowAllInfoTableData] = useState(false);
  const [showTextFullTxt, setShowTextFullTxt] = useState("");

  const [isToggled, setIsToggled] = useState(true);

  const onChangeSwitchButton = (checked) => {
    if (checked) {
      if (tableData.length) {
        let features = tableData.filter((d) => {
          if (d.geometry?.rings || d.geometry?.paths || d.geometry?.x) return d;
          else return undefined;
        });
        highlightFeature(features, props.map, {
          layerName: "highlightGraphicLayer",
        });
      }
    } else {
      props.map.findLayerById("highlightGraphicLayer").removeAll();
    }
    setIsToggled(checked);
  };

  //useEffects

  useEffect(() => {
    if (!props.mainData.logged)
      window.open(window.hostURL + "/home/<USER>", "_self");
    //intialize layersNames
    let mapAllLayers = props.map.__mapInfo.info.$layers.layers;
    // let mapAllTbls = props.map.__mapInfo.info.$layers.tables;
    let layersSetting = props.mainData.layers;
    let layersNames = Object.entries(layersSetting)
      ?.filter((l) => {
        if (layersSetting[l[0]].hideFromSearchs) return undefined;
        let mapLayerNames = mapAllLayers.map((lay) => lay.name);
        if (mapLayerNames.includes(l[1].name)) return l;
        else return undefined;
      })
      ?.map((l) => {
        return {
          layerName: l[0],
          layerMetadata: l[1],
        };
      });
    window.moment = moment;
    setLayersNames(layersNames);
    return () => {
      //clear highlightGraphicLayer + ZoomGraphicLayer
      // clearGraphics(["ZoomGraphicLayer", "highlightGraphicLayer"], props.map);
      //reset filtered layer list
      setDisplayedLinks([]);
      //reset tbl columns
      setTblColumns([]);
      //reset tbl data
      setTableData([]);
      //reset active layer
      setActiveLayer({
        layerData: null,
        value: null,
      });
      //reset filter modal opening //I think it is unnecessary
      seFilteRModal(false);
      //reset filter by attribute where clause
      filterWhereClauseRef.current = {
        current: "1=1",
        dep: {
          default: "",
          filtered: "",
        },
      };
      setSide(true);
      //reset layer name shown on the list
      setLayersNames([]);
      //reset search text used for filter layer list
      setSearchLayerNameText("");
      //reset count of results
      setCountNumber({
        current: 0,
        dep: 0,
      });
      countNumberRef.current = {};
      //reset total area result shown on table
      setTotalArea({
        current: null,
        dep: null,
      });
      totalAreaRef.current = null;

      //for pagination
      setCurrentPageNum({
        current: 1,
        dep: 1,
      });
      //for filters per column in table
      setColFilterWhere({
        current: [],
        dep: [],
      });
      // reset statistics data
      // flag to display statistics screen for features on table

      //setMetaStat(null);
      navigate("/");
    };
  }, []);
  // getFeatures based on currentPageNum (pagination)
  useEffect(() => {
    console.log("filterWhereClauseRef", filterWhereClauseRef);
    console.log("colFilterWhere", colFilterWhere);
    if (currentPageNum.current > 1 || currentPageNum.dep > 1) {
      showLoading(true);
      // let queriedLayerData = depTblColumns
      //   ? depTableData.layerData
      //   : activeLayer.layerData;
      let queriedLayerData = activeLayer.layerData;
      // let startIndex = depTblColumns
      //   ? (currentPageNum.dep - 1) * window.paginationCount
      //   : (currentPageNum.current - 1) * window.paginationCount;
      let startIndex = (currentPageNum.current - 1) * window.paginationCount;
      let where = "";
      if (colFilterWhere.current.length > 0) {
        let whereForFilteredColumns = colFilterWhere.current.join(" AND ");
        where = whereForFilteredColumns;
      } else if (filterWhereClauseRef?.current?.current) {
        where = filterWhereClauseRef.current.current;
      }

      console.log("where is :", where);
      //let whereClause = getWhereForStatistics(depTblColumns);
      getLayerFeatures(queriedLayerData, false, {
        num: window.paginationCount,
        start: startIndex,
        where: where,
      }).then((data) => {
        // settingTableData(
        //   depTblColumns ? depTblColumns : tblColumns,
        //   data.tableData,
        //   {
        //     isNewTbl: false,
        //     colsIsNeeded: false,
        //     isDependData: depTblColumns ? true : false,
        //   }
        // );
        settingTableData(tblColumns, data.tableData, {
          isNewTbl: false,
          colsIsNeeded: false,
          isDependData: false,
        });
        showLoading(false);
      });
    }
  }, [currentPageNum]);

  //useEffect for language state showing table column header in arabic if 'ar' and english if 'en'
  useEffect(() => {
    if (!props.languageState) return;
    else {
      //Arabic column header
      // if (
      //   props.languageState === "ar" &&
      //   (depTblColumns || tblColumns).length
      // )
      if (props.languageState === "ar" && tblColumns.length) {
        //in case of dependent tbl
        // if (depTblColumns && depTblColumns?.length) {
        //   let cloneDepTblCol = [...depTblColumns];
        //   for (let i = 0; i < cloneDepTblCol.length; i++) {
        //     let item = cloneDepTblCol[i];
        //     item.title = item.arAlias;
        //   }
        //   setDepTblColumns(cloneDepTblCol);
        // }
        if (tblColumns?.length) {
          let cloneTblCol = [...tblColumns];
          for (let i = 0; i < cloneTblCol.length; i++) {
            let item = cloneTblCol[i];
            item.title = item.arAlias;
          }
          setTblColumns(cloneTblCol);
        }
      }
      //English column header
      // else if (
      //   props.languageState === "en" &&
      //   (depTblColumns || tblColumns).length
      // )
      else if (props.languageState === "en" && tblColumns.length) {
        // if (depTblColumns && depTblColumns?.length) {
        //   let cloneDepTblCol = [...depTblColumns];
        //   for (let i = 0; i < cloneDepTblCol.length; i++) {
        //     let item = cloneDepTblCol[i];
        //     item.title = item.enAlias;
        //   }
        //   setDepTblColumns(cloneDepTblCol);
        // }
        if (tblColumns?.length) {
          let cloneTblCol = [...tblColumns];
          for (let i = 0; i < cloneTblCol.length; i++) {
            let item = cloneTblCol[i];
            item.title = item.enAlias;
          }
          setTblColumns(cloneTblCol);
        }
      }
    }
  }, [props.languageState]);

  useEffect(() => {
    removeHighlightedAreasWithDimming(props.map);
    if (activeLayer.layerData && tableData && tblColumns) {
      console.log("active layer", activeLayer);
      filterInput.current = null;
      let defaultWhereClause = "1=1";
      let layerName = activeLayer.value;
      let layerID = getLayerId(props.map.__mapInfo, layerName);
      let params = {
        layerID,
        pagCount: window.paginationCount,
        layerData: activeLayer.layerData,
        isNewTbl: true,
        colsIsNeeded: true,
      };
      resetPagination();
      setColFilterWhere({
        current: [],
        dep: [],
      });
      setCountNumber({ ...countNumber, dep: 0 }); //Q1: why current is not setted by 0 ???
      countNumberRef.current = {
        ...countNumberRef.current,
        dep: 0,
      };
      setTotalArea({ ...totalArea, dep: null }); //Q2: why current is not setted by null ???
      totalAreaRef.current = {
        ...totalAreaRef.current,
        dep: null,
      };
      setCurrentPageNum({ ...currentPageNum, dep: 1 }); //Q3: why current is not setted by 1 ???
      // the answers of Q1, Q2, Q3 are here in the next line "getLayerDataForTable" in this func: they will be set again by actual data
      getLayerDataForTable(params);
      //reset any filters
      //reset filter by attribute
      filterWhereClauseRef.current = {
        current: defaultWhereClause,
        dep: {
          default: "",
          filtered: "",
        },
      };
      //reset filter by column
      setColFilterWhere({
        dep: [],
        current: [],
      });
    }
  }, [activeLayer]);

  // handle google navigation button
  const handleGoogleNavigationButton = (data) => {
    if (data.geometry) {
      if (data.geometry.centroid) {
        navigateToGoogle(
          data.geometry.centroid.latitude,
          data.geometry.centroid.longitude
        );
      } else {
        navigateToGoogle(data.geometry.latitude, data.geometry.longitude);
      }
    }
  };
  ///////////////////////////////////////////////////////////////////////////
  //handlers land parcels statistics
  //handler to open statistics UI screen  ---> it is for just parcels layer

  const openMetaStat = async () => {
    if (showMetaStat) setMetaStat(false);
    else {
      if (activeLayer.layerData.layerMetadata.statFromDifferentLayer) {
        console.log("differen layer stats");
        let layerName =
          activeLayer.layerData.layerMetadata.statFromDifferentLayer
            .diffLayerName;
        let layerID = getLayerId(props.map.__mapInfo, layerName);
        let layerData = layersNames.find(
          (f) => f.layerName === activeLayer.value
        );
        console.log("layers names", layersNames);

        let isDepNotCurrent = depTblColumns ? true : false;
        let whereClause = getWhereForStatistics(isDepNotCurrent);
        console.log("layer id", layerID);
        console.log("layer data", layerData);
        console.log("where clause", whereClause);
        showLoading(true);
        try {
          let { data } = await getStatisticsForChart(
            layerID,
            layerData,
            whereClause
          );
          if (data.length) {
            console.log("get statistics for chart data", data);
            let appliedStatisticsField = props.map.__mapInfo.info.$layers.layers
              .find((l) => l.name === layerName)
              .fields.find(
                (f) =>
                  f.name ===
                  activeLayer.layerData.layerMetadata.statFromDifferentLayer
                    .appliedStatisticsField.name
              );

            let isFromDomain =
              activeLayer.layerData.layerMetadata.statFromDifferentLayer
                .appliedStatisticsField.isFromDomain;
            setMetaStat({
              totalCount: isDepNotCurrent
                ? countNumber.dep
                : countNumber.current,
              totalArea: isDepNotCurrent ? totalArea.dep : totalArea.current,
              appliedStatisticsField,
              data: data?.map((f) => f.attributes),
              where: whereClause,
              layerData: activeLayer.layerData,
              isFromDomain,
            });
          } else {
            notificationMessage(t("common:NoDataAvailForStatistics"));
          }
          showLoading(false);
        } catch (err) {
          console.log("err ", err);
          showLoading(false);
          if (err?.response?.status === 401) {
            //logut
            notificationMessage(t("common:sessionFinished"), 5);
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else notificationMessage(t("common:retrievError"), 4);
        }
      } else {
        console.log("active layer", activeLayer);
        let layerName = activeLayer.value;
        let layerID = getLayerId(props.map.__mapInfo, layerName);
        let layerData = layersNames.find((f) => f.layerName === layerName);
        let isDepNotCurrent = depTblColumns ? true : false;
        let whereClause = getWhereForStatistics(isDepNotCurrent);
        console.log("layer id", layerID);
        console.log("layer data", layerData);
        console.log("where clause", whereClause);
        showLoading(true);
        try {
          let { data } = await getStatisticsForChart(
            layerID,
            layerData,
            whereClause
          );
          if (data.length) {
            console.log("get statistics for chart data", data);
            let appliedStatisticsField = props.map.__mapInfo.info.$layers.layers
              .find((l) => l.name === layerName)
              .fields.find(
                (f) =>
                  f.name ===
                  activeLayer.layerData.layerMetadata.appliedStatisticsField
                    .name
              );

            let isFromDomain =
              activeLayer.layerData.layerMetadata.appliedStatisticsField
                .isFromDomain;
            setMetaStat({
              totalCount: isDepNotCurrent
                ? countNumber.dep
                : countNumber.current,
              totalArea: isDepNotCurrent ? totalArea.dep : totalArea.current,
              appliedStatisticsField,
              data: data?.map((f) => f.attributes),
              where: whereClause,
              layerData: activeLayer.layerData,
              isFromDomain,
            });
          } else {
            notificationMessage(t("common:NoDataAvailForStatistics"));
          }
          showLoading(false);
        } catch (err) {
          console.log("err ", err);
          showLoading(false);
          if (err?.response?.status === 401) {
            //logut
            notificationMessage(t("common:sessionFinished"), 5);
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else notificationMessage(t("common:retrievError"), 4);
        }
      }
    }
  };

  //handle export PDF for statistics UI

  const exportPDFStatistics = () => {
    let isDepNotCurrent = depTblColumns ? true : false;
    let whereClause = showMetaStat.where;
    let count = isDepNotCurrent ? countNumber.dep : countNumber.current;
    let area = isDepNotCurrent ? totalArea.dep : totalArea.current;
    localStorage.setItem(
      "attrTblChart",
      whereClause + ";" + area + ";" + count
    );
    window.open(process.env.PUBLIC_URL + "/PrintPdfAttrTbl", "_blank");
  };

  ////////////////////////////////////////////////////////////////////////

  //////////////// open side list + activate a selected layer /////////////////////////
  const openSideLinkData = (e, queriedLayerData) => {
    //1- check if it is the same active link
    let layerName = e.target.id;

    // if (layerName !== activeLayer.value) {
    //   setActiveLayer({
    //     layerData: queriedLayerData,
    //     value: layerName,
    //   });
    // }
    setActiveLayer({
      layerData: queriedLayerData,
      value: layerName,
    });
  };

  ///////////////////////////////////////////////////////////////////////////////////////

  // const openSideLinkData = (e, queriedLayerData) => {
  //   //1- check if it is the same active link
  //   let layerName = e.target.id;
  //   if (layerName !== activeLayer.value) {
  //     //reset filter columns
  //     filterInput.current = null;
  //     let defaultWhereClause = "1=1";
  //     //2- highlight the item in list
  //     setActiveLayer({
  //       layerData: queriedLayerData,
  //       value: layerName,
  //     });
  //     //3- get the layer data
  //     let layerID = getLayerId(props.map.__mapInfo, layerName);
  //     let params = {
  //       layerID,
  //       pagCount: window.paginationCount,
  //       layerData: queriedLayerData,
  //       isNewTbl: true,
  //       colsIsNeeded: true,
  //     };
  //     resetPagination();
  //     setColFilterWhere({
  //       current: [],
  //       dep: [],
  //     });
  //     // setDepTableData({
  //     //   data: [],
  //     //   layerData: "",
  //     // });
  //     // setDepTblColumns();
  //     setCountNumber({ ...countNumber, dep: 0 }); //Q1: why current is not setted by 0 ???
  //     countNumberRef.current = {
  //       ...countNumberRef.current,
  //       dep: 0,
  //     };
  //     setTotalArea({ ...totalArea, dep: null }); //Q2: why current is not setted by null ???
  //     totalAreaRef.current = {
  //       ...totalAreaRef.current,
  //       dep: null,
  //     };
  //     setCurrentPageNum({ ...currentPageNum, dep: 1 }); //Q3: why current is not setted by 1 ???
  //     // the answers of Q1, Q2, Q3 are here in the next line "getLayerDataForTable" in this func: they will be set again by actual data
  //     getLayerDataForTable(params);
  //     //reset any filters
  //     //reset filter by attribute
  //     filterWhereClauseRef.current = {
  //       current: defaultWhereClause,
  //       dep: {
  //         default: "",
  //         filtered: "",
  //       },
  //     };
  //     //reset filter by column
  //     setColFilterWhere({
  //       dep: [],
  //       current: [],
  //     });
  //   }
  // };

  const openSide = (e) => {
    setSide(true);
  };
  const closeSide = (e) => {
    setSide(false);
  };

  //filter layer list by search

  const handleSearchInput = (e) => {
    console.log("layers names", layersNames);
    if (props.languageState !== "ar") {
      let searchInput = e.target.value.toLowerCase();
      setSearchLayerNameText(e.target.value.toLowerCase());
      let displayed = layersNames.filter((el) => {
        let searchValue = el.layerMetadata.name.toLowerCase();
        return searchValue.indexOf(searchInput) !== -1;
      });
      setDisplayedLinks(displayed);
    } else {
      let searchInput = e.target.value.toLowerCase();
      setSearchLayerNameText(e.target.value.toLowerCase());
      let displayed = layersNames.filter((el) => {
        let searchValue = el.layerMetadata.arname.toLowerCase();
        return searchValue.indexOf(searchInput) !== -1;
      });
      setDisplayedLinks(displayed);
    }
  };
  //////////////////////////////////////////////////////////////////////////

  ///////////////////////////////////////////////////////////////////////////////////////
  //open search/filter modal by attribute
  const openFilterModal = () => {
    seFilteRModal(!showFilterModal);
  };
  /////////////////////////////////////////////////////////////////////////////////////////

  ////////////////////////////////////////////////////////////////////////////////////////

  //helpers functions
  //get where clause for using in statistics UI ---> it is for just parcels layer
  const getWhereForStatistics = (isDepNotCurrent) => {
    let where = "";
    //in case of dependent layer

    if (isDepNotCurrent) {
      let isFilteredByCol = colFilterWhere.dep.length ? colFilterWhere.dep : [];
      let isFilteredByAttr =
        filterWhereClauseRef.current.dep.filtered ||
        filterWhereClauseRef.current.dep.default;
      if (isFilteredByAttr) where = isFilteredByAttr;
      if (isFilteredByCol.length)
        where = isFilteredByAttr
          ? where + " AND " + isFilteredByCol.join(" AND ")
          : isFilteredByCol.join(" AND ");
    } else {
      console.log("colFilterWhere", colFilterWhere);
      console.log("FilterWhereclause ref", filterWhereClauseRef);
      console.log("active layer", activeLayer);
      if (activeLayer.layerData.layerMetadata.statFromDifferentLayer) {
        let isFilteredByCol = colFilterWhere.current.length
          ? colFilterWhere.current
          : [];
        let isFilteredByAttr = filterWhereClauseRef.current.current;
        if (isFilteredByAttr) where = isFilteredByAttr;
        if (isFilteredByCol.length)
          where = isFilteredByAttr
            ? where + " AND " + isFilteredByCol.join(" AND ")
            : isFilteredByCol.join(" AND ");
        where = where.replace(
          new RegExp(activeLayer.layerData.layerMetadata.displayField, "g"),
          activeLayer.layerData.layerMetadata.statFromDifferentLayer
            .appliedStatisticsField.name
        );
      } else {
        //current
        let isFilteredByCol = colFilterWhere.current.length
          ? colFilterWhere.current
          : [];
        let isFilteredByAttr = filterWhereClauseRef.current.current;
        if (isFilteredByAttr) where = isFilteredByAttr;
        if (isFilteredByCol.length)
          where = isFilteredByAttr
            ? where + " AND " + isFilteredByCol.join(" AND ")
            : isFilteredByCol.join(" AND ");
      }
    }
    return where || "1=1";
  };

  //get table data to display
  const getLayerDataForTable = (params) => {
    const {
      layerID,
      pagCount,
      layerData,
      isNewTbl,
      callBackFunc,
      where,
      orderByFields,
      colsIsNeeded,
      isDependData,
    } = params;

    showLoading(false);
    showLoading(true);

    Promise.all([
      getStatisticsForFeatsLayer(layerID, layerData, where),
      getLayerFeatures(layerData, colsIsNeeded, {
        where: where || "1=1",
        num: pagCount || window.paginationCount,
        start: 0,
        orderByFields,
      }),
    ])
      .then((data) => {
        setCurrentTotalArea(
          data[0].countPlusArea.sumAreaResult
            ? data[0].countPlusArea.sumAreaResult
            : null
        );
        setCountNumber(
          !isDependData
            ? {
                ...countNumberRef.current,
                current: data[0].countPlusArea.COUNT,
              }
            : {
                ...countNumberRef.current,
                dep: data[0].countPlusArea.COUNT,
              }
        );
        setTotalArea(
          !isDependData
            ? {
                ...totalAreaRef.current,
                current: data[0].countPlusArea.AREA
                  ? parseFloat(data[0].countPlusArea.AREA)
                  : null,
              }
            : {
                ...totalAreaRef.current,
                dep: data[0].countPlusArea.AREA
                  ? parseFloat(data[0].countPlusArea.AREA)
                  : null,
              }
        );
        countNumberRef.current = !isDependData
          ? { ...countNumberRef.current, current: data[0].countPlusArea.COUNT }
          : { ...countNumberRef.current, dep: data[0].countPlusArea.COUNT };
        totalAreaRef.current = !isDependData
          ? {
              ...totalAreaRef.current,
              current: data[0].countPlusArea.AREA
                ? parseFloat(data[0].countPlusArea.AREA)
                : null,
            }
          : {
              ...totalAreaRef.current,
              dep: data[0].countPlusArea.AREA
                ? parseFloat(data[0].countPlusArea.AREA)
                : null,
            };
        let tblCols = data[1].tblColumns.length
          ? data[1].tblColumns
          : tblColumns;
        /*tblCols.sort((a, b) => {
          if (a.enAlias < b.enAlias) {
            return -1;
          }
          if (a.enAlias > b.enAlias) {
            return 1;
          }
          return 0;
        });*/
        let updatedData = data[1].tableData.map((item) => {
          return {
            ...item,
            AREA_KM: parseFloat(item.AREA_KM),
            OFFICIAL_AREA: parseFloat(item.OFFICIAL_AREA),
          };
        });
        settingTableData(tblCols, updatedData, {
          isNewTbl,
          colsIsNeeded,
          isDependData,
        });
        showLoading(false);
        callBackFunc && callBackFunc();
      })
      .catch((err) => {
        console.log(err);
        showLoading(false);
        if (err?.response?.status === 401) {
          //logut
          notificationMessage(t("common:sessionFinished"), 5);
          localStorage.removeItem("user");
          localStorage.removeItem("token");
          window.open(window.hostURL + "/home/<USER>", "_self");
        } else notificationMessage(t("common:retrievError"), 5);
      });
  };
  const getLayerFeatures = async (queriedLayerData, colsIsNeeded, params) => {
    let { num, start, ...rest } = params;
    let layerID = getLayerId(props.map.__mapInfo, queriedLayerData.layerName);
    let promise = new Promise(async (resolve, reject) => {
      let queryParams = {
        url: window.mapUrl + "/" + layerID,
        num: num || 20,
        start: start || 0,
        notShowLoading: true,
        returnGeometry: true,
        outFields: queriedLayerData.layerMetadata.outFields,
        ...rest,
      };
      const regionField =
        props.languageState == "ar" ? "AR_REGION" : "EN_REGION";
      const reserveField =
        props.languageState == "ar"
          ? "AR_PROTECTED_AREA_NAME"
          : "EN_PROTECTED_AREA_NAME";
      queryTask({
        ...queryParams,
        callbackResult: async ({ features }) => {
          console.log("query result features", features);
          if (features.length > 0) {
            console.log("query params", queryParams);
            if (queryParams.where && queryParams.where.includes(regionField)) {
              props.map
                .findLayerById("highlightBoundaryGraphicLayer")
                .removeAll();
              showLoading(true);
              const regionValues = extractValues(
                queryParams.where,
                regionField
              );
              const regionLayerId = getLayerId(props.map.__mapInfo, "REGION");
              let where = "";
              const regionNameField =
                props.languageState === "ar"
                  ? "AR_REGION_NAME"
                  : "EN_REGION_NAME";
              regionValues.forEach((value) => {
                where = where.concat(
                  `${regionNameField} LIKE '%${value}%' or `
                );
              });
              where = where.slice(0, -3);
              queryTask({
                url: window.mapUrl + "/" + regionLayerId,
                where: where,
                outFields: [[regionNameField]],
                returnGeometry: true,
                callbackResult: (result) => {
                  highlightFeature(result.features, props.map, {
                    layerName: "highlightRegionsGraphicLayer",
                    isZoom: true,
                    fillColor: [0, 0, 0, 0],
                    strokeColor: [255, 0, 0],
                    isDashStyle: true,
                    highlightWidth: 3,
                    zoomDuration: 1000,
                  });
                  highlightAreaWithDimming(result.features, props.map);
                  showLoading(false);
                },
              });
            }
            if (queryParams.where && queryParams.where.includes(reserveField)) {
              const reserveValues = extractValues(
                queryParams.where,
                reserveField
              );
              const reserveLayerId = getLayerId(
                props.map.__mapInfo,
                "PROTECTED_AREA_BOUNDARY"
              );
              let where = "";
              const reserveNameField =
                props.languageState === "ar"
                  ? "AR_PROTECTED_AREA_NAME"
                  : "EN_PROTECTED_AREA_NAME";
              reserveValues.forEach((value) => {
                where = where.concat(
                  `${reserveNameField} LIKE '%${value}%' or `
                );
              });
              where = where.slice(0, -3);
              let isZoom = queryParams.where.includes(regionField)
                ? false
                : true;
              queryTask({
                url: window.mapUrl + "/" + reserveLayerId,
                where: where,
                outFields: [[reserveNameField]],
                returnGeometry: true,
                callbackResult: (result) => {
                  highlightFeature(result.features, props.map, {
                    layerName: "highlightBoundaryGraphicLayer",
                    isZoom: isZoom,
                    fillColor: [0, 0, 0, 0],
                    strokeColor: [0, 0, 255],
                    isDashStyle: true,
                    highlightWidth: 3,
                    zoomDuration: 1000,
                  });
                  if (!queryParams.where.includes(regionField)) {
                    props.map
                      .findLayerById("highlightRegionsGraphicLayer")
                      .removeAll();
                    highlightAreaWithDimming(result.features, props.map);
                  }
                },
              });
            }
          }
          let reqFieldsWithoutObjectID = [],
            tblData = [],
            tblCols = [];
          if (colsIsNeeded) {
            let layData = [
              ...props.map.__mapInfo.info.$layers.layers,
              ...props.map.__mapInfo.info.$layers.tables,
            ].find((l) => l.name === queriedLayerData.layerName);

            reqFieldsWithoutObjectID = layData.fields.filter((f) => {
              f.is_order = queriedLayerData.layerMetadata.outFields_Db.find(
                (x) =>
                  props.languageState == "en"
                    ? x.enname == f.name
                    : x.name == f.name
              )?.is_order;

              return (
                queriedLayerData.layerMetadata.outFields.includes(f.name) &&
                !["OBJECTID"].includes(f.name) &&
                !f.name?.toString().includes("_SPATIAL_ID")
              );
            });

            reqFieldsWithoutObjectID = reqFieldsWithoutObjectID.sort(
              (a, b) => a.is_order - b.is_order
            );

            tblCols = await reqFieldsWithoutObjectID.map(async (f, index) => {
              let hasSubtype = getSubtypes(
                f.name,
                props.map,
                queriedLayerData.layerName
              );
              //for filter in column
              if (hasSubtype)
                f.domain = hasSubtype.subTypeData.reduce((total, item) => {
                  if (!total.length) {
                    total = item.domains;
                  } else {
                    total = [...total, ...item.domains];
                  }
                  return total;
                }, []);
              let layerIsNotInConfig =
                queriedLayerData.layerMetadata?.notInConfig;
              let fieldFromLayerData =
                queriedLayerData.layerMetadata.fields.find(
                  (ff) => ff.fieldName === f.name
                );

              let arAlias = layerIsNotInConfig
                ? fieldFromLayerData?.alias
                : fieldFromLayerData?.alias &&
                  (fieldFromLayerData?.alias).match(
                    "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
                  )
                ? fieldFromLayerData?.alias
                : fieldFromLayerData?.alias
                ? t(`layers:${fieldFromLayerData?.alias}`)
                : f.alias || f.name;
              let filtersIntoTable = await setFiltersIntoTable(
                f,
                queriedLayerData,
                props.languageState === "ar" ? arAlias : f.name
              );
              let rowProp = {
                filtered: false,
                title: arAlias,
                enAlias: arAlias,
                arAlias: arAlias,
                dataType: f.type,
                withDomain: f.domain,
                dataIndex:
                  f.name.includes("_DATE") || f.name.includes("_date")
                    ? [f.name, "hijri"]
                    : f.name,
                key: f.name,
                sorter: true,
                sortOrder: false,
                filteredValue: null,
                defaultFilteredValue: undefined,
                filterResetToDefaultFilteredValue: true,
                ...filtersIntoTable,
                sortDirections: ["ascend", "descend", "ascend"],
                showSorterTooltip: true,
              };
              if (!index) rowProp.width = "max-content";
              // todo: create column render for long text content
              const MAX_TEXT_LENGTH = 50;
              const SHOWN_TEXT_LENGTH = 15;
              if (f?.type === "esriFieldTypeString") {
                const settings = {
                  maxTextLen: MAX_TEXT_LENGTH,
                  shownTextLen: SHOWN_TEXT_LENGTH,
                  onClickHandler: (text) => setShowTextFullTxt(text),
                  tooltip: t("common:showMore"),
                };
                rowProp.render = (text, record) =>
                  wrappedColRender(text, record, settings);
              }
              return {
                ...rowProp,
              };
            });

            tblCols = await Promise.all(
              tblCols.map(async (promise) => await promise)
            );

            let isLayer = props.map.__mapInfo.info.$layers.layers.find(
              (lay) => lay.name === queriedLayerData.layerName
            );
            let haveDependencies = queriedLayerData.layerMetadata?.dependecies;
            console.log("table columns data are : ", tblCols);
            if ((isLayer || haveDependencies) && features.length)
              tblCols.push({
                title: t("common:procedures"),
                key: "zoom",
                render: (text, record) => {
                  return (
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        flexDirection:
                          i18n.language === "ar" ? "row" : "row-reverse",
                        justifyContent:
                          i18n.language === "ar" ? "flex-start" : "flex-end",
                      }}
                    >
                      {isLayer ? (
                        <>
                          <Button
                            className="tableHeaderBtn "
                            onClick={() => {
                              zoomToFeature(record);
                              clearFeatures();
                            }}
                          >
                            <Tooltip placement="top" title={t("map:zoomMap")}>
                              <span>
                                <FontAwesomeIcon icon={faSearchPlus} />
                              </span>
                            </Tooltip>
                          </Button>

                          <Button
                            className="tableHeaderBtn "
                            onClick={() => {
                              handleGoogleNavigationButton(record);
                              clearFeatures();
                            }}
                          >
                            <Tooltip
                              placement="top"
                              title={t("map:mapToolsServices.googleMaps")}
                            >
                              <span>
                                <FaMapMarkerAlt />
                              </span>
                            </Tooltip>
                          </Button>

                          {activeLayer?.layerData?.layerMetadata?.dependencies?.find(
                            (d) =>
                              d.name === externalBtnsForTblData.showMediaBtn
                          ) && (
                            <Button
                              className="tableHeaderBtn "
                              onClick={() => {
                                let recordMediaUrls = getFieldsMediaUrls(
                                  queriedLayerData.layerName,
                                  props.mainData.layers,
                                  record
                                );
                                console.log(
                                  "record Media Urls",
                                  recordMediaUrls
                                );
                                if (recordMediaUrls.length > 0) {
                                  setOpenGalleryModal(true);
                                  setGalleryData(recordMediaUrls);
                                  clearFeatures();
                                } else {
                                  message.warning(
                                    t("NoDataWarning", {
                                      ns: "common",
                                    })
                                  );
                                }
                              }}
                            >
                              <Tooltip
                                placement="top"
                                title={t("map:mapToolsServices.showImages")}
                              >
                                <span>
                                  <GrGallery />
                                </span>
                              </Tooltip>
                            </Button>
                          )}
                        </>
                      ) : null}

                      {queriedLayerData?.layerName?.toLowerCase() ===
                      "plan_data" ? (
                        <Button
                          className="tableHeaderBtn "
                          onClick={() =>
                            setPlanDataModal(record?.PLAN_SPATIAL_ID)
                          }
                        >
                          <Tooltip
                            placement="top"
                            title={t("common:planNoLandsStatistics")}
                          >
                            <span>
                              <FontAwesomeIcon icon={faAddressBook} />
                            </span>
                          </Tooltip>
                        </Button>
                      ) : null}
                      {haveDependencies
                        ?.filter((dep) => {
                          //filter icons based on owner type [sales, royal, private] lands
                          if (
                            queriedLayerData.layerName ===
                              PARCEL_LANDS_LAYER_NAME &&
                            dep.showingField === "OWNER_TYPE"
                          ) {
                            return dep.codeValue === record?.OWNER_TYPE_Code;
                          }
                          return dep;
                        })
                        ?.map((dep, index) => {
                          let planNoEng = record
                            ? convertNumbersToEnglish(record?.PLAN_NO)
                            : undefined;
                          if (
                            dep.depName === "LandStatistics" &&
                            randomPlanNumbers.includes(planNoEng)
                          )
                            return undefined;
                          // console.log({dep});
                          else if (dep)
                            return (
                              <Button
                                key={index}
                                className="tableHeaderBtn"
                                onClick={() => getDepData(dep, record)}
                              >
                                <Tooltip
                                  placement="top"
                                  title={t(`layers:${dep.tooltip}`)}
                                >
                                  {dep.icon ? (
                                    <span>
                                      <FontAwesomeIcon icon={dep.icon} />
                                    </span>
                                  ) : (
                                    <img
                                      alt="icons"
                                      src={dep.imgIconSrc}
                                      style={{
                                        cursor: "pointer",
                                      }}
                                    />
                                  )}
                                </Tooltip>
                              </Button>
                            );
                          else return undefined;
                        })}
                    </div>
                  );
                },
              });
          }
          if (features.length)
            getFeatureDomainName(features, layerID).then((feats) => {
              tblData = feats.map((f) => {
                return { ...f.attributes, geometry: f.geometry };
              });
              resolve({
                tableData: tblData,
                tblColumns: tblCols,
              });
            });
          else
            resolve({
              tableData: tblData,
              tblColumns: tblCols,
            });
        },
        callbackError: (err) => {
          console.log("err", err);
          notificationMessage(t("common:retrievError"), 4);

          reject(err);
        },
      });
    });
    return promise;
  };
  function extractValues(whereQuery, includedName) {
    const regex = new RegExp(
      `${includedName}\\s*(=|LIKE)\\s*['%]*([^'%]+)['%]*`,
      "gi"
    );
    const matches = new Set();
    let match;
    while ((match = regex.exec(whereQuery)) !== null) {
      matches.add(match[2]);
    }
    return Array.from(matches);
  }

  const getLayerExtext = () => {
    clearGraphics("ZoomGraphicLayer", props.map);
    if (tableData.length && tableData.find((d) => d.geometry)) {
      let features = tableData.filter((d) => {
        if (d.geometry?.rings || d.geometry?.paths || d.geometry?.x) return d;
        else return undefined;
      });
      if (isToggled) {
        highlightFeature(features, props.map, {
          layerName: "highlightGraphicLayer",
          isZoom: true,
          zoomDuration: 1000,
          notExpandLevel: true,
        });
      }
    }
  };
  ////////////////////////table handlers and custom component ////////////////////////////////
  // helper func that munipulate data before display it via table

  const settingTableData = (tblColumns, newTableData, params) => {
    let { isNewTbl, colsIsNeeded, isDependData } = params;
    let totalDataToShow = [];
    //fill all empty values with لا يوجد
    let colData = [...tblColumns];
    console.log("column data", colData);
    let tblDataWithNotFoundValues = newTableData?.map((row) => {
      colData.forEach((col) => {
        if (col.key.includes("_DATE")) {
          let hijriDate =
            row[col.key] && isNumber(row[col.key])
              ? {
                  timeStamp: row[col.key],
                  hijri: convertTimeStampToDate(row[col.key]),
                  // hijri:
                  //   row[col.key]?.toString()?.length > 6
                  //     ? toArabic(moment(row[col.key]).format("iYYYY/iM/iD"))
                  //     : toArabic(moment(row[col.key]).format("iYYYY/iM")),
                }
              : row[col.key] && !isNumber(row[col.key])
              ? {
                  hijri: toArabic(row[col.key]),
                  timeStamp: null,
                }
              : {
                  hijri: t("common:notFoundValue"),
                  timeStamp: null,
                };
          //use convertEngNumbersToArabic method in utilsFunc to show it in arabic numbers
          row[col.key] = hijriDate;
        } else if (col.key == "PLAN_NO" || col.key == "PARCEL_PLAN_NO") {
          row[col.key] = convertToArabic(row[col.key])
            ? convertToArabic(row[col.key])
            : t("common:notAvailable");
        } else if (col.key.includes("DATE")) {
          row[col.key] = row[col.key];
        } else {
          row[col.key] =
            typeof row[col.key] === "string"
              ? // if string
                row[col.key].trim()
                ? toArabic(row[col.key])
                : t("common:notAvailable")
              : //if not string
              row[col.key]
              ? row[col.key]
              : t("common:notAvailable");
        }
      });
      return row;
    });
    let isLayer;
    if (isDependData) {
      isLayer = props.map.__mapInfo.info.$layers.layers.find(
        (lay) => lay.name === isDependData.layerName
      );

      if (isNewTbl) {
        totalDataToShow = [...tblDataWithNotFoundValues];
        setDepTableData(
          typeof isDependData === "object"
            ? {
                data: totalDataToShow,
                layerData: isDependData,
                layerMetadata: props.mainData.layers[isDependData.layerName],
              }
            : {
                ...depTableData,
                data: totalDataToShow,
                layerMetadata: props.mainData.layers[isDependData.layerName],
              }
        );
        if (colsIsNeeded) setDepTblColumns(colData);
      } else {
        //used in case of pagination to add old data with new data
        totalDataToShow = [...depTableData.data, ...tblDataWithNotFoundValues];
        setDepTableData(
          typeof isDependData === "object"
            ? {
                data: totalDataToShow,
                layerData: isDependData,
                layerMetadata: props.mainData.layers[isDependData.layerName],
              }
            : {
                ...depTableData,
                data: totalDataToShow,
                layerMetadata: props.mainData.layers[isDependData.layerName],
              }
        );
      }
    } else {
      isLayer = true;
      if (isNewTbl) {
        totalDataToShow = [...tblDataWithNotFoundValues];
        setTableData(totalDataToShow);
        if (colsIsNeeded) setTblColumns(colData);
        // clearGraphics(["highlightGraphicLayer"], props.map);
      } else {
        //used in case of pagination to add old data with new data
        totalDataToShow = [...tableData, ...tblDataWithNotFoundValues];
        setTableData(totalDataToShow);
      }
    }
    clearGraphics(["ZoomGraphicLayer", "highlightGraphicLayer"], props.map);
    if (isLayer) {
      if (totalDataToShow.length) {
        let features = totalDataToShow.filter((d) => {
          if (d.geometry?.rings || d.geometry?.paths || d.geometry?.x) return d;
          else return;
        });
        if (isToggled) {
          highlightFeature(features, props.map, {
            layerName: "highlightGraphicLayer",
            isZoom: true,
            zoomDuration: 1000,
            notExpandLevel: true,
          });
        }
      }
    }
  };
  //reset filters function (for columns and attribute as well)
  const resetFilters = () => {
    let defaultWhere = "1=1";
    //in case of dependent tbl not main layer
    // if (
    //   colFilterWhere.dep.length ||
    //   (depTblColumns && filterWhereClauseRef.current.dep.filtered)
    // ) {
    //   //dependency data
    //   let layerID = getLayerId(
    //     props.map.__mapInfo,
    //     depTableData.layerData.layerName
    //   );
    //   getLayerDataForTable({
    //     layerID,
    //     layerData: depTableData.layerData,
    //     isNewTbl: true,
    //     where: filterWhereClauseRef.current.dep.default,
    //     colsIsNeeded: true,
    //   });
    //   //reset filter by attribute for dependent tbl
    //   filterWhereClauseRef.current = {
    //     ...filterWhereClauseRef.current,
    //     dep: {
    //       ...filterWhereClauseRef.current.dep,
    //       filtered: "",
    //     },
    //   };
    // }
    //in case of current main laye not dependent tbl
    // else if
    //  if
    //  (
    //   colFilterWhere.current.length ||
    //   (!depTblColumns && filterWhereClauseRef.current.current !== "1=1")
    // )
    if (
      colFilterWhere.current.length ||
      filterWhereClauseRef.current.current !== "1=1"
    ) {
      //currnet layer
      let layerID = getLayerId(props.map.__mapInfo, activeLayer.value);
      getLayerDataForTable({
        layerID,
        layerData: activeLayer.layerData,
        isNewTbl: true,
        where: defaultWhere,
        colsIsNeeded: true,
      });
      //reset only the current main layer
      filterWhereClauseRef.current = {
        ...filterWhereClauseRef.current,
        current: defaultWhere,
      };
    }
    //reset columns filter for both (dep, current layer)
    setColFilterWhere({
      current: [],
      dep: [],
    });
  };
  //handler for reset column filter
  const resetOnlyRestColFilter = () => {
    // in case of dependent tbl

    if (depTblColumns) {
      //dependency data
      let layerID = getLayerId(
        props.map.__mapInfo,
        depTableData.layerData.layerName
      );
      //to reset dep col filter ---> I have to go back to the filter by attr if found --> if not get all data "1=1"-->filterWhereClauseRef.current.dep.default
      //get the filter by attribute and get data based on it
      let depWhereClause = filterWhereClauseRef.current.dep.filtered
        ? filterWhereClauseRef.current.dep.filtered
        : filterWhereClauseRef.current.dep.default;
      getLayerDataForTable({
        layerID,
        layerData: depTableData.layerData,
        isNewTbl: true,
        where: depWhereClause,
        colsIsNeeded: true,
      });
      //?? I think filterWhereClauseRef is used for filter/search by attribute only not for tbl column
      //I comment the below lines of 'filterWhereClauseRef.current'
      // filterWhereClauseRef.current = {
      //   ...filterWhereClauseRef.current,
      //   dep: {
      //     ...filterWhereClauseRef.current.dep,
      //     filtered: "",
      //   },
      // };
    }

    //in case of currnet layer
    else {
      let layerID = getLayerId(props.map.__mapInfo, activeLayer.value);
      //to reset current col filter ---> I have to go back to the filter by attr if found -->
      //
      //get the filter by attribute and get data based on it
      let currWhereClause = filterWhereClauseRef.current.current || "1=1";
      getLayerDataForTable({
        layerID,
        layerData: activeLayer.layerData,
        isNewTbl: true,
        where: currWhereClause,
        colsIsNeeded: true,
      });
    }
    //reset tbl col filters for both (dep, current)
    setColFilterWhere({
      current: [],
      dep: [],
    });
  };
  //for filter in server side
  const setFiltersIntoTable = async (rowData, layerData, placeholder) => {
    let layerID = getLayerId(props.map.__mapInfo, layerData.layerName);
    let queriedData = [];
    const getData = () => {
      return new Promise(async (resolve, reject) => {
        queryTask({
          url: window.mapUrl + "/" + layerID,
          where: "1=1",
          outFields: [rowData.name],
          returnDistinctValues: true,
          returnGeometry: false,
          callbackResult: (features) => {
            let data = features.features.map((feat) => {
              if (feat.attributes[rowData.name]) {
                return {
                  name: feat.attributes[rowData.name],
                  code: feat.attributes[rowData.name],
                };
              }
            });
            resolve(data);
          },
          callbackError(error) {
            reject();
          },
        });
      });
    };
    if (rowData.domain || rowData.domain?.codedValues) {
      queriedData = await getData();
      queriedData = queriedData.filter((q) => q?.name && q?.code);
      console.log("queried data are", queriedData);
      if (
        rowData.domain &&
        rowData.domain.codedValues &&
        queriedData.length > 0
      ) {
        rowData.domain.codedValues = queriedData.filter(
          (c) => c.name && c.code
        );
      }
    }
    console.log("row data are", rowData);
    // let hasDropDownList = rowData.domain || getSubtypes(rowData.name,props.map,layerData.layerName );
    return {
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => {
        return (
          <div
            style={{
              padding: 8,
            }}
          >
            {rowData.domain?.codedValues || rowData.domain ? (
              <Select
                virtual={false}
                // suffixIcon={<DownCircleFilled />}
                suffixIcon={<RiArrowDropDownFill size={30} />}
                showSearch
                allowClear
                onSelect={(value, input) => {
                  setSelectedKeys([
                    {
                      name: input.name,
                      value,
                    },
                  ]);
                }}
                ref={filterInput}
                placeholder={`${t(`common:searchWith`)}${placeholder}`}
                value={selectedKeys[0]?.name}
                // onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                style={{
                  marginBottom: 8,
                  display: "block",
                }}
                // placeholder="القيمة"
                getPopupContainer={(trigger) => trigger.parentNode}
                optionFilterProp="name"
                filterOption={(input, option) => {
                  return option.name && option.name.indexOf(input) >= 0;
                }}
              >
                {(rowData.domain?.codedValues || rowData.domain)
                  .filter((c) => c.name && c.code)
                  .map((domain, index) => {
                    return (
                      <Select.Option
                        key={index}
                        name={domain.name}
                        value={domain.code}
                      >
                        {domain.name}
                      </Select.Option>
                    );
                  })}
              </Select>
            ) : rowData.type === "esriFieldTypeDate" ? (
              <GregorianDatePicker
                disableOnClickOutside
                placeholder={t("common:selectDate")}
                input={{
                  ref: { filterInput },
                  // id:"value" + row.id,
                  value: selectedKeys,
                  onChange: (e) => {
                    setSelectedKeys(e);
                  },
                }}
              />
            ) : (
              // <HijriDatePicker
              //   disableOnClickOutside
              //   placeholder={t("common:selectDate")}
              //   input={{
              //     ref: { filterInput },
              //     // id:"value" + row.id,
              //     value: selectedKeys,
              //     onChange: (e) => setSelectedKeys(e),
              //   }}
              // />
              <Input
                ref={filterInput}
                placeholder={`${t(`common:searchWith`)}${placeholder}`}
                value={selectedKeys[0]}
                onChange={(e) =>
                  setSelectedKeys(e.target.value ? [e.target.value] : [])
                }
                onPressEnter={() =>
                  handleTblFilter(selectedKeys, confirm, rowData.name)
                }
                style={{
                  marginBottom: 8,
                  display: "block",
                }}
              />
            )}

            <Space>
              <button
                type="primary"
                className="SearchBtn mt-3 w-25"
                size="large"
                htmlType="submit"
                onClick={() => handleTblFilter(selectedKeys, confirm, rowData)}
                icon={<SearchOutlined />}
                style={{
                  width: 90,
                }}
              >
                {t("common:search")}
              </button>
              <button
                className="SearchBtn mt-3 w-25"
                size="large"
                htmlType="submit"
                onClick={() =>
                  clearFilters && handleResetTblFilter(clearFilters, confirm)
                }
                style={{
                  width: 90,
                }}
              >
                {t("common:cancelSearch")}{" "}
              </button>
            </Space>
          </div>
        );
      },
      filterIcon: (filtered) => (
        <SearchOutlined
          // onClick={(e)=>handleClickingOnfilterIcon(e, rowData, layerData)}
          style={{
            color: filtered ? "#1890ff" : undefined,
          }}
        />
      ),
      onFilterDropdownVisibleChange: (visible) => {
        if (visible) {
          setTimeout(() => filterInput.current?.select?.(), 100);
        }
      },
      render: (text) => text,
    };
  };

  const handleTblFilter = (selectedKeys, confirm, rowData) => {
    resetPagination();
    confirm(); //confirm will fire onTableChange
  };

  const handleResetTblFilter = (clearFilters, confirm) => {
    removeHighlightedAreasWithDimming(props.map);
    clearFilters();
    confirm(); //confirm will fire onTableChange
  };

  const onTableChange = (newPagination, filters, sorter, actionObj, e) => {
    //layer/table data from map object
    console.log("on table change clicked");

    let layData = [
      ...props.map.__mapInfo.info.$layers.layers,
      ...props.map.__mapInfo.info.$layers.tables,
    ].find(
      (l) =>
        l.name ===
        (depTblColumns ? depTableData.layerData.layerName : activeLayer.value)
    );

    let layerFields = layData.fields;

    let reqLayerData = depTblColumns
      ? depTableData.layerData
      : activeLayer.layerData;

    let layerID = getLayerId(props.map.__mapInfo, reqLayerData.layerName);
    //in case of sort
    if (actionObj.action === "sort" && actionObj?.currentDataSource?.length) {
      let tblColsEdited = [...tblColumns];
      //1- set sortOrder to highlight sort icon on table
      tblColsEdited.forEach((c) => {
        if (c.key === sorter.columnKey) c.sortOrder = sorter.order;
        else c.sortOrder = false;
      });
      let filterColWhere = [];
      //copy filters fields from filters object to filter dictionary
      let filterDic = {};
      for (let f in filters) {
        if (filters[f]) {
          filterDic[f] =
            typeof filters[f] === "object" ? filters[f][0] : filters[f];
        }
      }
      //set table columns to reflect all changes on it
      setTblColumns(tblColsEdited);
      //whereClause of previous filters
      let whereClause = depTblColumns
        ? filterWhereClauseRef.current.dep.filtered
          ? filterWhereClauseRef.current.dep.filtered
          : filterWhereClauseRef.current.dep.default
        : filterWhereClauseRef.current.current;
      //if there is a new implemented filter rather than prevois one ---> update the whereClause to contain prev + new filters
      if (Object.values(filterDic).length) {
        Object.entries(filterDic).forEach((f) => {
          let fLayer = layerFields.find((fl) => fl.name === f[0]);

          if (fLayer.type === "esriFieldTypeString") {
            filterColWhere.push(` ${fLayer.name} LIKE '%${f[1]}%'`);
          } else if (
            [
              "esriFieldTypeInteger",
              "esriFieldTypeDouble",
              "esriFieldTypeSmallInteger",
            ].includes(fLayer.type)
          ) {
            let value = typeof f[1] === "object" ? f[1].value : f[1];
            filterColWhere.push(` ${fLayer.name} = ${value}`);
          } else {
            //date
            filterColWhere.push(
              `(${fLayer.name} >= ${convertHirjiDateToTimeSpan(f[1])})`
            );
            filterColWhere.push(
              `(${fLayer.name} <= ${convertHirjiDateToTimeSpan(f[1], true)})`
            );
          }
        });
        if (filterColWhere.length) {
          whereClause =
            whereClause !== "1=1"
              ? "(" +
                whereClause +
                ") AND (" +
                filterColWhere.join(" AND ") +
                ")"
              : "(" + filterColWhere.join(" AND ") + ")";
        }
      }
      showLoading(true);
      getLayerDataForTable({
        layerID,
        layerData: reqLayerData,
        isNewTbl: true,
        callBackFunc: () => notificationMessage(t("orderSucc")),
        where: whereClause,
        orderByFields: [
          `${sorter.columnKey} ${sorter.order === "ascend" ? "ASC" : "DESC"}`,
        ],
        colsIsNeeded: false,
      });
    } else if (
      actionObj.action === "sort" &&
      !actionObj?.currentDataSource?.length
    ) {
      notificationMessage(t("common:noDataForSort"));
    } else if (actionObj.action === "filter") {
      let filterColWhere = [];
      let filterDic = {};
      for (let f in filters) {
        if (filters[f]) {
          filterDic[f] =
            typeof filters[f] === "object"
              ? typeof filters[f][0] !== "object"
                ? filters[f][0]
                : filters[f][0]?.value
              : filters[f];
        }
      }
      //1- set filtered to highlight filter icon on table
      let tblColsEdited = depTblColumns ? [...depTblColumns] : [...tblColumns];

      tblColsEdited.forEach((col) => {
        if (col.key === "zoom") return;
        col.filtered = Object.keys(filterDic).includes(col.key);
        if (!col.filtered) col.filteredValue = null;
        else col.filteredValue = [filterDic[col.key]];
      });

      depTblColumns
        ? setDepTblColumns(tblColsEdited)
        : setTblColumns(tblColsEdited);

      let whereClauseToGetData;
      //check if there is a column filter --> set all col filters into filter dictionary and push them to filterColWhere array
      if (Object.values(filterDic).length) {
        Object.entries(filterDic).forEach((f) => {
          let fLayer = layerFields.find((fl) => fl.name === f[0]);

          if (fLayer.type === "esriFieldTypeString") {
            filterColWhere.push(` ${fLayer.name} LIKE '%${f[1]}%'`);
          } else if (
            [
              "esriFieldTypeInteger",
              "esriFieldTypeDouble",
              "esriFieldTypeSmallInteger",
            ].includes(fLayer.type)
          ) {
            let value = typeof f[1] === "object" ? f[1].value : f[1];
            filterColWhere.push(` ${fLayer.name} = ${value}`);
          } else {
            //date

            filterColWhere.push(
              `(${fLayer.name} >= ${convertHirjiDateToTimeSpan(f[1])})`
            );
            filterColWhere.push(
              `(${fLayer.name} <= ${convertHirjiDateToTimeSpan(f[1], true)})`
            );
          }
        });
        if (filterColWhere.length) {
          let attrTblWhereClause = depTblColumns
            ? filterWhereClauseRef.current.dep.filtered
              ? filterWhereClauseRef.current.dep.filtered
              : filterWhereClauseRef.current.dep.default
            : filterWhereClauseRef.current.current;

          whereClauseToGetData =
            attrTblWhereClause !== "1=1"
              ? "(" +
                attrTblWhereClause +
                ") AND (" +
                filterColWhere.join(" AND ") +
                ")"
              : "(" + filterColWhere.join(" AND ") + ")";

          if (whereClauseToGetData !== attrTblWhereClause) {
            showLoading(true);
            getLayerDataForTable({
              layerID,
              layerData: reqLayerData,
              isNewTbl: true,
              callBackFunc: () => notificationMessage(t("common:filteredSucc")),
              where: whereClauseToGetData,
              colsIsNeeded: false,
              isDependData: depTblColumns ? depTableData.layerData : false,
            });
            let settedFilterWhere = depTblColumns
              ? {
                  ...colFilterWhere,
                  dep: filterColWhere,
                }
              : {
                  ...colFilterWhere,
                  current: filterColWhere,
                };

            setColFilterWhere(settedFilterWhere);
          } else showLoading(false);
        }
      } else {
        //in case of reset filter if just one column was filtered

        // if (colFilterWhere.length) {
        //   let attrTblWhereClause = depTblColumns
        //     ? filterWhereClauseRef.current.dep.filtered
        //       ? filterWhereClauseRef.current.dep.filtered
        //       : filterWhereClauseRef.current.dep.default
        //     : filterWhereClauseRef.current.current;
        //   showLoading(true);
        //   getLayerDataForTable({
        //     layerID,
        //     layerData: reqLayerData,
        //     isNewTbl: true,
        //     callBackFunc: () => notificationMessage("تم الفلترة بنجاح"),
        //     where: attrTblWhereClause,
        //     colsIsNeeded: false,
        //     isDependData: depTblColumns ? depTableData.layerData : false,
        //   });
        //   setColFilterWhere({
        //     current: [],
        //     dep: [],
        //   });
        // } else {
        resetOnlyRestColFilter();
        // }
      }
    }
  };

  const getDepData = (dependencyData, record) => {
    clearFeatures();
    let attributes = record;
    let filteredValue = "";
    let isDepChanged =
      depTableData.layerData?.layerName &&
      dependencyData.name &&
      depTableData.layerData?.layerName === dependencyData.name;
    if (isDepChanged) {
      setCurrentPageNum({ ...currentPageNum, dep: 1 });
      setColFilterWhere({ ...colFilterWhere, dep: [] });
    }

    if (
      Object.keys(attributes).includes(`${dependencyData.filter}` + "_Code")
    ) {
      filteredValue = convertNumbersToEnglish(
        attributes[`${dependencyData.filter}` + "_Code"]
      );
    } else
      filteredValue = convertNumbersToEnglish(
        attributes[dependencyData.filter]
      );

    //put condition if(LGR_ROYAL or PARCEL_PRIVACY,KROKY_SUBMISSIONS,FARZ_SUBMISSIONS,CONTRACT_UPDATE_SUBMISSIONS)
    if (
      [
        "LGR_ROYAL",
        "PARCEL_PRIVACY",
        "SALES_LANDS",
        "KROKY_SUBMISSIONS",
        "FARZ_SUBMISSIONS",
        "CONTRACT_UPDATE_SUBMISSIONS",
        "ZAWAYED_SUBMISSIONS",
        "SERVICE_PROJECTS_SUBMISSIONS",
      ].includes(dependencyData.name)
    ) {
      showLoading(true);
      let token = localStorage.getItem("token");
      let requestURL = "";
      if (
        ["LGR_ROYAL", "PARCEL_PRIVACY", "SALES_LANDS"].includes(
          dependencyData.name
        )
      ) {
        requestURL = dependencyData.url + filteredValue;
      } else {
        let parcel_no = record["PARCEL_PLAN_NO"];
        let mun_no = record["MUNICIPALITY_NAME_Code"];
        let plan_no =
          record["PLAN_NO"] && record["PLAN_NO"] !== t("common:notFoundValue")
            ? record["PLAN_NO"]
            : "";
        let block_no =
          record["PARCEL_BLOCK_NO"] &&
          record["PARCEL_BLOCK_NO"] !== t("common:notFoundValue")
            ? record["PARCEL_BLOCK_NO"]
            : "" || "";
        let subdivision_type = record["SUBDIVISION_TYPE_Code"] || "";
        let subdivision_Desc =
          record["SUBDIVISION_DESCRIPTION"] &&
          record["SUBDIVISION_DESCRIPTION"] !== t("common:notFoundValue")
            ? record["SUBDIVISION_DESCRIPTION"]
            : "" || "";

        requestURL =
          dependencyData.url +
          `?Parcel_no=${parcel_no}&Mun_code=${mun_no}&plan_no=${plan_no}&block_no=${block_no}&subdivision_Desc=${subdivision_Desc}&subdivision_type=${subdivision_type}`;
      }
      axios
        .get(requestURL, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })
        .then((res) => {
          let data = res.data || [];
          if (["PARCEL_PRIVACY", "SALES_LANDS"].includes(dependencyData.name))
            data = data?.results || [];

          setCountNumber({
            ...countNumberRef.current,
            dep: data.length,
          });
          setTotalArea({
            ...totalAreaRef.current,
            dep: null,
          });
          countNumberRef.current = {
            ...countNumberRef.current,
            dep: data.length,
          };
          totalAreaRef.current = {
            ...totalAreaRef.current,
            dep: null,
          };
          let isDepPrivateRoyalSales = [
            "LGR_ROYAL",
            "PARCEL_PRIVACY",
            "SALES_LANDS",
          ].includes(dependencyData.name);
          let outFields =
            isDepPrivateRoyalSales &&
            props.mainData.layers[dependencyData.name].fields
              ? props.mainData.layers[dependencyData.name].fields
              : props.mainData.layers[dependencyData.name].outFields;
          let tblCols = outFields
            .filter(
              (item) =>
                item !== "OBJECTID" &&
                !item?.toString()?.includes("_SPATIAL_ID")
            )
            .map((f, index) => {
              let rowProp = {
                filtered: false,
                title: t(`layers:${f.alias}`),
                dataType: "esriFieldTypeString",
                // withDomain: f.domain,
                dataIndex: f.name,
                key: f.name,
                sorter: true,
                sortOrder: false,
                filteredValue: undefined,
                defaultFilteredValue: undefined,
                filterResetToDefaultFilteredValue: true,
                // ...setFiltersIntoTable(f, queriedLayerData),
                sortDirections: ["ascend", "descend", "ascend"],
                showSorterTooltip: true,
              };
              if (f.isAnchor) {
                rowProp.render = (txt, { id }) => {
                  return (
                    <a
                      href={dependencyData.workflowUrl + id + `?tk=${token}`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      {txt}
                    </a>
                  );
                };
              } else if (f.isHijriDateFormat) {
                rowProp.render = (txt) => getDateFromConcatNumbers(txt);
              }
              if (!index) rowProp.width = "max-content";
              return {
                ...rowProp,
              };
            });

          let tblDataWithNotFoundValues =
            data?.map((row) => {
              tblCols.forEach((col) => {
                if (col.key.includes("_dateh")) {
                  let hijriDate =
                    row[col.key] &&
                    isNumber(row[col.key]) &&
                    (row[col.key].includes("-") || row[col.key].includes("/"))
                      ? row[col.key]?.toString()?.length > 6
                        ? toArabic(moment(row[col.key]).format("iYYYY/iM/iD"))
                        : toArabic(moment(row[col.key]).format("iYYYY/iM/iD"))
                      : row[col.key] &&
                        !(
                          row[col.key].includes("-") ||
                          row[col.key].includes("/")
                        )
                      ? getDateFromConcatNumbers(row[col.key])
                      : t("common:notFoundValue");
                  //use convertEngNumbersToArabic method in utilsFunc to show it in arabic numbers
                  row[col.key] = hijriDate;
                } else
                  row[col.key] =
                    typeof row[col.key] === "string"
                      ? // if string
                        row[col.key].trim()
                        ? showDataSplittedBySlash(row[col.key])
                        : t("common:notFoundValue")
                      : //if not string
                      row[col.key]
                      ? showDataSplittedBySlash(row[col.key])
                      : t("common:notFoundValue");
              });
              return row;
            }) || [];

          setDepTableData({
            data: tblDataWithNotFoundValues,
            layerData: dependencyData.name,
          });
          setDepTblColumns(tblCols);
          showLoading(false);
        })
        .catch((err) => {
          console.log(err);
          showLoading(false);
          if (err?.response?.status === 401) {
            //logut
            notificationMessage(t("common:sessionFinished"), 5);
            localStorage.removeItem("user");
            localStorage.removeItem("token");
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else notificationMessage(t("common:retrievError"), 5);
        });
    } else if (dependencyData?.depName === "LandStatistics") {
      setPlanDataModal({
        PLAN_SPATIAL_ID: record?.PLAN_SPATIAL_ID,
        PLAN_NO: record?.PLAN_NO
          ? convertNumbersToEnglish(record?.PLAN_NO)
          : record?.PLAN_NO,
        MUNICIPALITY_NAME: record?.MUNICIPALITY_NAME_Code,
      });
    } else {
      let whereClause =
        dependencyData.filterDataType === "esriFieldTypeString"
          ? `${dependencyData.filter}='${filteredValue}'`
          : `${dependencyData.filter}=${filteredValue}`;
      let layerID = getLayerId(props.map.__mapInfo, dependencyData.name);
      let layerData = Object.entries(props.mainData.layers)
        ?.filter((l) => l[0] === dependencyData.name)
        ?.map((l) => {
          return {
            layerName: l[0],
            layerMetadata: l[1],
          };
        });
      if (layerData.length) layerData = layerData[0];
      // let layerData = layersNames.find(
      //   (l) => l.layerName === dependencyData.name
      // );
      filterWhereClauseRef.current = {
        ...filterWhereClauseRef.current,
        dep: {
          default: whereClause,
          filtered: "",
        },
      };
      getLayerDataForTable({
        layerID,
        layerData,
        isNewTbl: true,
        // callBackFunc,
        where: whereClause,
        // orderByFields:layerData,
        colsIsNeeded: true,
        isDependData: layerData,
      });
    }
  };

  const handleBackClick = () => {
    clearGraphics(["ZoomGraphicLayer", "highlightGraphicLayer"], props.map);
    if (tableData.length) {
      let features = tableData.filter((d) => {
        if (d.geometry?.rings || d.geometry?.paths || d.geometry?.x) return d;
        else return undefined;
      });
      if (isToggled) {
        highlightFeature(features, props.map, {
          layerName: "highlightGraphicLayer",
          isZoom: true,
          zoomDuration: 1000,
        });
      }
    }
    setDepTableData({
      data: [],
      layerData: "",
    });
    setDepTblColumns();
    setCountNumber({ ...countNumber, dep: 0 });
    countNumberRef.current = {
      ...countNumberRef.current,
      dep: 0,
    };
    setTotalArea({ ...totalArea, dep: null });
    totalAreaRef.current = {
      ...totalAreaRef.current,
      dep: null,
    };
    setCurrentPageNum({ ...currentPageNum, dep: 1 });
    filterWhereClauseRef.current = {
      ...filterWhereClauseRef.current,
      dep: {
        default: "",
        filtered: "",
      },
    };
  };

  const CustomtableRow = ({
    dataIndex,
    title,
    record,
    index,
    children,
    ...restProps
  }) => {
    let tblDataNumber = depTblColumns
      ? depTableData.data.length
      : tableData.length;
    return (
      <>
        <tr
          onMouseMove={(e) => drawLineWithZoom(record, e)} // mouse enter row
          onMouseLeave={clearFeatures}
          {...restProps}
        >
          {children}
        </tr>

        {tblDataNumber != 0 && index + 1 == tblDataNumber ? (
          countNumber.current !== tblDataNumber &&
          tblColumns.length &&
          !depTblColumns ? (
            props.searchTableDisplay !== "searchTableHidden" ? (
              <tr>
                <PaginationComp
                  currentPageNum={currentPageNum}
                  isCurrent={true}
                  setCurrentPageNum={setCurrentPageNum}
                />
              </tr>
            ) : null
          ) : depTblColumns ? (
            countNumber.dep !== tblDataNumber ? (
              <tr>
                <PaginationComp
                  currentPageNum={currentPageNum}
                  isCurrent={false}
                  searchTableDisplay={props.searchTableDisplay}
                  setCurrentPageNum={setCurrentPageNum}
                />
              </tr>
            ) : null
          ) : null
        ) : null}
      </>
    );
  };

  ///////////////////////// zoom and draw lines from tbl row to feature on map
  function drawLineWithZoom(feature, event) {
    if (feature) {
      if (
        feature.geometry &&
        (feature.geometry?.rings?.length ||
          feature.geometry?.x ||
          feature.geometry?.paths)
      ) {
        highlightFeature(feature, props.map, {
          layerName: "SelectGraphicLayer",
          isAnimatedLocation: true,
          highlighColor: [0, 255, 255, 0.5],
          strokeColor: "black",
          fillColor: [0, 255, 255, 0.5],
          noclear: true,
        });

        // drawLine({
        //   feature: feature,
        //   map: props.map,
        //   event: event,
        //   //condition here depend on css style
        //   hideFromHeight:
        //     props.searchTableDisplay == "searchTableShown"
        //       ? window.innerHeight * 0.6
        //       : 200,
        // });
      }
    }
  }

  const clearFeatures = () => {
    props.map.findLayerById("SelectGraphicLayer").removeAll();
    clearCanvasLine();
  };
  const zoomToFeature = (feature) => {
    console.log("feature clicked", feature);
    if (
      feature.geometry?.rings?.length ||
      feature.geometry?.x ||
      feature.geometry?.paths
    ) {
      zoomToFeatureDefault(feature, props.map);
      if (feature.geometry.type == "polygon") {
        console.log("feature to highlight are", feature);
        highlightAreaWithDimming([feature], props.map);
      }
    }
  };

  ///////////////////////////////////////////////////////////////

  //reset pagination
  const resetPagination = () => {
    setCurrentPageNum({
      current: 1,
      dep: 1,
    });
  };

  const filterModalFields = (requiredLayer) => {
    let mediaFields = requiredLayer.layerMetadata?.outFields_Db.filter(
      (field) => field.is_media
    );
    let mediaFieldsNames = mediaFields.map((mediaField) => mediaField.enname);
    let filteredFields = tblColumns.filter(
      (tblColumn) => !mediaFieldsNames.includes(tblColumn.dataIndex)
    );
    return filteredFields.filter((x) => x.hasOwnProperty("filtered"));
  };
  return (
    <div
      className={props.searchTableDisplay}
      id="searchMeta"
      style={{
        // right: props.openDrawer ? "260px" : "135px",
        // left: props.openDrawer ? "260px" : "135px",
        right: "135px",
        left: "135px",
      }}
    >
      {openGalleryModal && (
        <GalleryModal
          gallery_data={galleryData}
          openGalleryModal={openGalleryModal}
          setOpenGalleryModal={setOpenGalleryModal}
        />
      )}

      <Container fluid className="openMetaHelp">
        {planDataModal && (
          <PlanDataModal
            map={props.map}
            open={planDataModal}
            planData={planDataModal}
            closeModal={() => setPlanDataModal()}
            landbaseParcelData={props.mainData.layers["Landbase_Parcel"]}
          />
        )}
        {showTextFullTxt && (
          <Modal
            visible={true}
            onOk={() => setShowTextFullTxt()}
            onCancel={() => setShowTextFullTxt()}
            //  width={"75%"}
            closable={true}
            cancelText={t("cancel")}
          >
            <div
              className={`showMoreModal ${props?.languageState}`}
              style={{ margin: "1rem", textAlign: "center" }}
            >
              {showTextFullTxt}
            </div>
          </Modal>
        )}
        <Row className="metaRow">
          <Col span={sideDisplay ? 5 : 0}>
            <div className="metaSide">
              {/* <Input
                placeholder={t("common:layerFilter")}
                allowClear
                value={searchText}
                onChange={handleChangeInput}
              /> */}
              <Form
                className="GeneralForm"
                layout="vertical"
                name="validate_other"
              >
                <Form.Item name="searchText" className="outerSearchInput">
                  <Input
                    placeholder={t("filter")}
                    allowClear
                    size="large"
                    prefix={<IoSearch color="#fff" className="nearbyIcon" />}
                    value={searchText}
                    onChange={handleSearchInput}
                  />
                </Form.Item>
              </Form>
              <div className="metaSideScroll">
                {searchText !== ""
                  ? displayedLinks.map((layData) => (
                      // <Tooltip title={text.name} placement="left">
                      <div
                        id={layData.layerName}
                        onClick={(e) => openSideLinkData(e, layData)}
                        key={layData.layerName}
                        className={
                          activeLayer.value === layData.layerName
                            ? "activeMetaSide metaSideDiv"
                            : " metaSideDiv"
                        }
                      >
                        {props?.languageState === "ar"
                          ? layData.layerMetadata.arabicName
                          : layData.layerMetadata.englishName}
                      </div>
                      // </Tooltip>
                    ))
                  : layersNames
                      // ?.filter((la) => la.layerMetadata.isSearchable)
                      .map((layer) => (
                        // <Tooltip title={text.name} placement="left">
                        <div
                          id={layer.layerName}
                          key={layer.layerName}
                          onClick={(e) => {
                            openSideLinkData(e, layer);
                          }}
                          className={
                            activeLayer.value === layer.layerName
                              ? "activeMetaSide metaSideDiv"
                              : " metaSideDiv"
                          }
                        >
                          {props?.languageState === "ar"
                            ? layer.layerMetadata.arabicName
                            : layer.layerMetadata.englishName}
                        </div>
                        // </Tooltip>
                      ))}
              </div>
            </div>
          </Col>{" "}
          {props.searchTableDisplay === "searchTableHidden" ? (
            <MdKeyboardDoubleArrowRight
              onClick={props.openSearchTable}
              className="searchTableArrow"
              color="#000"
              style={{ transform: "rotate(-90deg)" }}
            ></MdKeyboardDoubleArrowRight>
          ) : (
            <MdKeyboardDoubleArrowRight
              onClick={props.closeSearchTable}
              className="searchTableArrow"
              color="#000"
              style={{ transform: "rotate(90deg)" }}
            ></MdKeyboardDoubleArrowRight>
          )}
          {(depTblColumns || tblColumns).length ? (
            <Col span={sideDisplay ? 19 : 24}>
              <div
                className="tableHeaderIconsDiv"
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                {showMetaStat ? (
                  <MetaDataStatistics
                    map={props.map}
                    openMetaStat={openMetaStat}
                    showMetaStat={showMetaStat}
                    exportPDFStatistics={exportPDFStatistics}
                  />
                ) : null}
                <p className="resultsNumber">
                  {sideDisplay ? (
                    <Button onClick={closeSide} className="metaheaderBtn">
                      <MdKeyboardDoubleArrowRight
                        icon={faChevronCircleRight}
                        style={{
                          marginRight: "5px",
                          cursor: "pointer",
                        }}
                      />
                    </Button>
                  ) : (
                    <Button onClick={openSide} className="metaheaderBtn">
                      <MdKeyboardDoubleArrowLeft
                        icon={faChevronCircleLeft}
                        style={{
                          fontSize: "18px",
                          marginRight: "5px",
                          cursor: "pointer",
                        }}
                      />
                    </Button>
                  )}
                  <span style={{ fontWeight: "bold" }} className="px-2">
                    {t("map:mapTools.resultNum")}
                  </span>
                  {depTblColumns ? (
                    <span>{countNumber.dep}</span>
                  ) : (
                    <span>{countNumber.current}</span>
                  )}

                  {currentTotalArea > 0 && (
                    <>
                      <span style={{ fontWeight: "bold" }} className="px-2">
                        {t("map:mapTools.area")}
                      </span>
                      <span>{currentTotalArea}</span>
                    </>
                  )}

                  {(depTblColumns ? totalArea.dep : totalArea.current) ? (
                    <>
                      <span
                        style={{ fontWeight: "bold" }}
                        className="metaHeaderSpacePadding px-2"
                      >
                        {t("map:mapTools.area")}{" "}
                      </span>
                      <span>
                        {(depTblColumns
                          ? totalArea.dep
                          : totalArea.current
                        ).toLocaleString()}{" "}
                        {t("map:km2")}
                      </span>
                    </>
                  ) : null}
                </p>

                <div className="tableFiltersButtons">
                  {/* <Button className="tableHeaderBtn "> <Tooltip title={t("map:kroky")} placement="top">

                <img className="splitKrokyMetaSVG"
                    src={kroky}
                    style={{
                      cursor: "pointer",
                    }}
                  />
              </Tooltip></Button>
              <Button className="tableHeaderBtn ">
              <Tooltip title={t("map:split")} placement="top">

                  <img className="splitKrokyMetaSVG"
                    src={splitIcon}
                    style={{
                      cursor: "pointer",
                    }}
                  />
              </Tooltip></Button>
              <Button className="tableHeaderBtn ">
              <Tooltip title={t("map:updateContract")} placement="top">
              <img className="splitKrokyMetaSVG"
                    src={updateContract}
                    style={{paddingTop:"7px",
                      cursor: "pointer",
                    }}
                  />
              </Tooltip></Button> */}

                  <Tooltip title={t("hideOtherData", { ns: "map" })}>
                    <Switch
                      checked={isToggled}
                      onChange={onChangeSwitchButton}
                    />
                  </Tooltip>

                  {(depTblColumns &&
                    countNumber.dep &&
                    depTableData.layerData?.layerName === "Landbase_Parcel" &&
                    depTableData?.layerMetadata?.dependencies?.find(
                      (d) => d.name === externalBtnsForTblData.statisticsAttrTbl
                    )) ||
                    (!depTblColumns &&
                      countNumber.current &&
                      activeLayer?.layerData?.layerMetadata?.dependencies?.find(
                        (d) =>
                          d.name === externalBtnsForTblData.statisticsAttrTbl
                      ) && (
                        <Button
                          className="tableHeaderBtn "
                          onClick={openMetaStat}
                        >
                          <Tooltip placement="top" title={t("common:stat")}>
                            <span>
                              <FontAwesomeIcon icon={faChartPie} />
                            </span>
                          </Tooltip>
                        </Button>
                      ))}

                  {depTblColumns && depTableData
                    ? depTableData?.layerMetadata?.dependencies?.find(
                        (d) =>
                          d.name === externalBtnsForTblData.filterAttrTblBtn
                      )
                    : activeLayer?.layerData?.layerMetadata?.dependencies?.find(
                        (d) =>
                          d.name === externalBtnsForTblData.filterAttrTblBtn
                      ) && (
                        <>
                          <Button
                            disabled={[
                              "LGR_ROYAL",
                              "PARCEL_PRIVACY",
                              "SALES_LANDS",
                            ].includes(depTableData?.layerData)}
                            className="tableHeaderBtn "
                            onClick={openFilterModal}
                          >
                            <Tooltip placement="top" title={t("common:filter")}>
                              <span>
                                <FontAwesomeIcon icon={faFilter} />
                              </span>
                            </Tooltip>
                          </Button>
                          <Button
                            disabled={[
                              "LGR_ROYAL",
                              "PARCEL_PRIVACY",
                              "SALES_LANDS",
                            ].includes(depTableData?.layerData)}
                            className="tableHeaderBtn"
                            onClick={resetFilters}
                          >
                            <Tooltip
                              placement="top"
                              title={t("map:cancelFilter")}
                            >
                              <span>
                                <RestartAltIcon />
                              </span>
                            </Tooltip>
                          </Button>

                          <FilteRModal
                            resetPagination={resetPagination}
                            showLoading={showLoading}
                            languageState={props.languageState}
                            fields={filterModalFields(activeLayer.layerData)}
                            isDepend={
                              depTblColumns ? depTableData?.layerData : false
                            }
                            setTblColumns={
                              depTblColumns ? setDepTblColumns : setTblColumns
                            }
                            showFilterModal={showFilterModal}
                            openFilterModal={openFilterModal}
                            reqLayer={
                              depTblColumns
                                ? depTableData.layerData
                                : activeLayer.layerData
                            }
                            map={props.map}
                            getLayerDataForTable={getLayerDataForTable}
                            setColFilterWhere={setColFilterWhere}
                            filterWhereClauseRef={filterWhereClauseRef}
                          />
                        </>
                      )}
                  {/**Export files */}
                  {(depTblColumns &&
                    depTableData?.layerMetadata?.dependencies?.find(
                      (d) => d.name === externalBtnsForTblData.exportAttrTbl
                    )) ||
                  (tblColumns.length &&
                    activeLayer?.layerData?.layerMetadata?.dependencies?.find(
                      (d) => d.name === externalBtnsForTblData.exportAttrTbl
                    )) ? (
                    <ExportFilesComp
                      map={props.map}
                      isDepend={depTblColumns ? true : false}
                      columns={
                        depTblColumns
                          ? depTblColumns
                              ?.filter((c) => c.title)
                              ?.map((c) => c.dataIndex)
                          : tblColumns
                              ?.filter((c) => c.title)
                              ?.map((c) => c.dataIndex)
                      }
                      filterWhereClause={filterWhereClauseRef.current}
                      colFilterWhere={colFilterWhere}
                      dataSet={depTblColumns ? depTableData.data : tableData}
                      labels={
                        depTblColumns
                          ? depTblColumns
                              ?.filter(
                                (c) =>
                                  c.title &&
                                  !["OBJECTID"].includes(c.dataIndex) &&
                                  !c.dataIndex
                                    ?.toString()
                                    .includes("_SPATIAL_ID")
                              )
                              ?.map((c) => c.title)
                          : tblColumns
                              ?.filter(
                                (c) =>
                                  c.title &&
                                  !["OBJECTID"].includes(c.dataIndex) &&
                                  !c.dataIndex
                                    ?.toString()
                                    .includes("_SPATIAL_ID")
                              )
                              ?.map((c) => c.title)
                      }
                      activeLayer={
                        depTblColumns
                          ? depTableData
                          : layersNames.length
                          ? activeLayer
                          : ""
                      }
                    />
                  ) : null}
                  {/****** */}
                  {(depTblColumns
                    ? depTableData
                    : activeLayer.layerData
                  )?.layerMetadata?.dependencies?.find(
                    (d) => d.name === externalBtnsForTblData.zoomBtn
                  ) && (
                    <Button
                      className="tableHeaderBtn"
                      onClick={() =>
                        getLayerExtext(
                          depTblColumns
                            ? depTableData.layerData
                            : activeLayer.layerData
                        )
                      }
                    >
                      <Tooltip
                        placement="top"
                        title={t("map:mapTools.zoomToAll")}
                      >
                        <span>
                          <FontAwesomeIcon icon={faExpandArrowsAlt} />
                        </span>
                      </Tooltip>
                    </Button>
                  )}
                  {depTblColumns ? (
                    <Button
                      className="tableHeaderBtn"
                      onClick={handleBackClick}
                    >
                      <Tooltip placement="top" title={t("map:mapTools.prev")}>
                        <span>
                          <ArrowBackIcon />
                        </span>
                      </Tooltip>
                    </Button>
                  ) : null}

                  <Button
                    className="tableHeaderBtn"
                    onClick={() => navigate("/")}
                  >
                    <Tooltip placement="top" title={t("close")}>
                      <span>
                        <IoCloseSharp size={20} />
                      </span>
                    </Tooltip>
                  </Button>
                </div>
              </div>

              <Table
                components={{
                  body: {
                    row: CustomtableRow,
                  },
                }}
                className="metaTableIcons"
                bordered
                locale={{
                  filterTitle: "Filter menu",
                  filterConfirm: "OK",
                  filterReset: "Reset",
                  filterEmptyText: "No filters",
                  filterCheckall: "Select all items",
                  filterSearchPlaceholder: "Search in filters",
                  emptyText: (
                    <h2 style={{ textAlign: "center" }}>
                      {t("common:noResults")}{" "}
                    </h2>
                  ),
                  selectAll: "Select current page",
                  selectInvert: "Invert current page",
                  selectNone: "Clear all data",
                  selectionAll: "Select all data",
                  sortTitle: "Sort",
                  expand: "Expand row",
                  collapse: "Collapse row",
                  triggerDesc: t("map:clickDescSort"),
                  triggerAsc: t("map:clickAscSort"),
                  cancelSort: t("map:cancelSort"),
                }}
                // columns={tblColumns}
                dataSource={depTblColumns ? depTableData.data : tableData}
                pagination={false}
                onChange={onTableChange}
                scroll={{ x: 200, y: 300 }}
                onRow={(record, index) => {
                  return {
                    onMouseMove: (e) => drawLineWithZoom(record, e), // mouse enter row
                    onMouseLeave: clearFeatures, // mouse leave row,

                    index,
                    onMouseDown: (e) => zoomToFeature(record),
                  };
                }}
                columns={(depTblColumns || tblColumns).filter(
                  (c) =>
                    !["OBJECTID", "id"].includes(c.dataIndex) &&
                    !c.dataIndex?.toString().includes("_SPATIAL_ID") &&
                    !checkIfHiddenField(
                      props.mainData.layers[activeLayer.value],
                      c.dataIndex
                    )
                )}
              >
                {/* {setColumnsComps(tblColumns)} */}
              </Table>
            </Col>
          ) : (
            <div className="no-chosen-layer">
              <div
                style={{
                  position: "absolute",
                  top: "10px",
                  insetInlineEnd: "10px",
                }}
              >
                <Button
                  className="tableHeaderBtn"
                  onClick={() => navigate("/")}
                  style={{ color: "#fff" }}
                >
                  <Tooltip placement="top" title={t("close")}>
                    <span>
                      <IoCloseSharp size={20} />
                    </span>
                  </Tooltip>
                </Button>
              </div>

              <strong>{t("common:noLayer")}</strong>
              <br />
              <h4>{t("common:chooseLayerToresult")}</h4>
            </div>
          )}
        </Row>
      </Container>
    </div>
  );
}
