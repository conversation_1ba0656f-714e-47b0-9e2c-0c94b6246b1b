import React from "react";
import { Route, Redirect } from "react-router-dom";
import { modulesIDs } from "./constants";


const PrivateRouter = ({ component: Component, id, ...rest }) => {
    const PERMISSIONS_ENUM = {
        "yes":'yes', "no":"no", "unknown":'unknown'
    }
    const [hasPermission, setHasPermission] = React.useState(()=>{
        let user = JSON.parse(localStorage.getItem('user'));
        let isUserHasPermission = user?.groups?.find(
            gr=>gr?.groups_permissions?.find(gp=>{
                if(id==='dashboard')return[modulesIDs.incidentsModule].includes(gp?.module_id)
                else return undefined
            })
          );
          if(isUserHasPermission) return true;
          else return false;
    });
  return (
    <Route
      {...rest}
      render={(props) =>
        hasPermission ? <Component {...rest} {...props }/> :
        
        <Redirect push to="/" />
      }
    />
  );
};

export default PrivateRouter
