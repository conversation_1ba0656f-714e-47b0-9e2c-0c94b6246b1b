import React from "react";
import { Modal } from "antd";
// import ImageCarousel from "../../../components/ImageCarousel/ImageCarousel";
// import ImageViewer from "../../../components/ImageViewer/ImageViewer";
import { useTranslation } from "react-i18next";

function ArchiveModal(props) {
    const {t} = useTranslation('common')

  const handleOk = async () => {};

  return <>
   <Modal
      className="archiveGalleryModal"
      visible={props.isOpen}
      onOk={handleOk}
      onCancel={props.closeModal}
      width={"75%"}
      closable={true}
    //   title={'الصور المؤرشفة'}
    //    confirmLoading
        cancelText={t('cancel')}
        //okText={'حسناً'}
    >
        
        {/* <ImageCarousel {...props} /> */}
        {props.children}
        </Modal></>;
}

export default ArchiveModal;
