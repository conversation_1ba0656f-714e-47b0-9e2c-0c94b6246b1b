import React from "react";
import { useEffect } from "react";
import { useState } from "react";
import ReactApexChart from "react-apexcharts";

function RadialGauge(props) {
  const [series, setSeries] = useState([]);
  const [labels, setLabels] = useState([]);
  const [options, setOptions] = useState({});

  useEffect(() => {
    let { preparedChartData, title, shownDataTypes } = props;
if(preparedChartData?.length){
    setOptions({
      chart: {
        // width: 250,
        type: "radialBar",
      },

      colors: ["#20E647"],
      plotOptions: {
        radialBar: {
          offsetY: 0,
          startAngle: 0,
          endAngle: 270,
          hollow: {
            margin: 5,
            size: "30%",
            background: "transparent",
            image: undefined,
          },
          dataLabels: {
            name: {
              show: true,
            },
            value: {
              show: true,
            },
          },
        },
      },

      legend: { show: false },
      labels: preparedChartData.map((i) => i.label),
    });
    setLabels(preparedChartData.map((i) => i.label));
    setSeries(preparedChartData.map((i) => i.count));
  }else{
    if(series?.length) setSeries([]);
    if(options) setOptions({});
    if(labels) setLabels([]);
  }
    return () => {
      setSeries();
      setOptions();
      setLabels();
    };
  }, [props.preparedChartData]);
  useEffect(()=>{
    if(props.sideTblTitle===props.title && series){
      props.onClickTitle({
        data: series,
        title: props.title,
        labels,
      })
    }
    
  },[series]);

  return (
    <div className="ape-chart">
      <div className="col text-center">

      <h6
        onClick={() =>
          {if((options && series) && (Object.keys(options).length &&series.length)) props.onClickTitle({
            data: series,
            title: props.title,
            labels,
          })
              //todo: show a toast there is no data to show in else

        }
        }
        style={{
          fontFamily: "NeoSansArabic",
          textAlign: "center",
          fontWeight: "bold",
          cursor: "pointer",
        }}
      >
        {props.title}
      </h6>
      {/* <img src={props.mapLogoSrc} className="map-pointer" alt="map logo" onClick={handleHeatMap} /> */}
          </div>

          {(options && series) && (!Object.keys(options).length || !series.length)?
      <h4 className="text-center"> لا يوجد بيانات للعرض</h4>
      :(options && series) && (Object.keys(options).length &&series.length)? <ReactApexChart
        options={options}
        series={series}
        type="radialBar"
        // width={props.width||250}
        // height={props.height||250}
      />:null}
    </div>
  );
}

export default RadialGauge;
