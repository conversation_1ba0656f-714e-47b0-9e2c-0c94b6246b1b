import { useEffect, useState } from "react";
import eventBus from "../../helper/EventBus";
import {
  faCaretDown,
  faCaretLeft,
  faCaretRight,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { getLayerId, showLoading } from "../../helper/common_func";
import { Checkbox } from "antd";

export default function Legend(props) {
  const [legendList, setLegendList] = useState([]);
  const [isLegnedHide, setIsLegnedHide] = useState(false);

  useEffect(() => {
    let result = {};
    let toggleResult = {};
    legendList.forEach((item) => {
      toggleResult[item.layerName] = item.toggle;
      if (item.list) {
        item.list.forEach((subItem) => {
          result[item.layerName + "_" + subItem.label] = subItem.isChecked;
        });
      }
      result[item.layerName + "_" + item.label] = item.isChecked;
    });
    window.__legend_checked_list = result;
    window.__legend_checked_toggle_list = toggleResult;
  }, [legendList]);

  useEffect(() => {
    eventBus.on("updateLegendList", (data) => {
      if (data.hideLegend) {
        setIsLegnedHide(data.hideLegend);
      } else {
        setIsLegnedHide(false);
        setLegendList(
          data.message.map((item, index) => ({
            ...item,
            toggle:
              window.__legend_checked_toggle_list[item.layerName] == undefined
                ? index == 0
                : window.__legend_checked_toggle_list[item.layerName],
            isChecked:
              window.__legend_checked_list[item.layerName + "_" + item.label] ==
                undefined ||
              (item.layerName == "PROTECTED_AREA_BOUNDARY" &&
                data.isProptected_Area_changed)
                ? true
                : window.__legend_checked_list[
                    item.layerName + "_" + item.label
                  ],
            list: item.list
              ? item.list.map((subItem) => ({
                  ...subItem,
                  isChecked:
                    window.__legend_checked_list[
                      item.layerName + "_" + subItem.label
                    ] == undefined ||
                    (item.layerName == "PROTECTED_AREA_BOUNDARY" &&
                      data.isProptected_Area_changed)
                      ? true
                      : window.__legend_checked_list[
                          item.layerName + "_" + subItem.label
                        ],
                }))
              : undefined,
          }))
        );
      }
    });
  }, []);

  useEffect(() => {
    return () => {
      eventBus.remove("updateLegendList");
    };
  }, []);

  const changeToggle = (item) => {
    item.toggle = !item.toggle;
    setLegendList([...legendList]);
  };

  const removeHighlight = (item) => {
    let layerId = getLayerId(props.map.__mapInfo, item.layerName);
    let flLayer = props.map.findLayerById("fl_" + layerId);

    if (flLayer?.__layerView._highlightIds.size > 0)
      flLayer.__layerView._highlightIds.clear();
  };

  const highlightLengendFeatures = (item) => {
    let layerId = getLayerId(props.map.__mapInfo, item.layerName);
    let flLayer = props.map.findLayerById("fl_" + layerId);

    if (flLayer) {
      let query = flLayer.createQuery();
      query.where = item.where || "1=1";

      /*if (item.layerName != "MARINE_SPECIE") {
      flLayer.__layerView.effect = {
        filter: { where: item.where },
        includedEffect: "drop-shadow(3px, 3px, 3px, black)",
        excludedEffect: "grayscale(100%) opacity(20%)"
      }
    }
    flLayer.visible = true;*/

      let hideLayers = props.map.layers.items.filter(
        (x) => x.id.indexOf("fl_") > -1
      );
      hideLayers.forEach((layer) => {
        if (layer.__layerView._highlightIds.size > 0)
          layer.__layerView._highlightIds.clear();
      });

      props.map.view.highlightOptions = {
        color: [180, 83, 51] || [0, 255, 255, 1],
        haloOpacity: 1,
        fillOpacity: 1,
        haloColor: [0, 0, 0, 1],
      };

      flLayer.__layerView.queryFeatures(query).then((result) => {
        flLayer.__layerView.highlight(result.features);
      });
    }
  };

  const handleCheckboxChange = (parentIndex, subIndex) => {
    const updatedList = [...legendList];
    updatedList[parentIndex].list[subIndex].isChecked =
      !updatedList[parentIndex].list[subIndex].isChecked;
    setLegendList(updatedList);

    let layerId = getLayerId(
      props.map.__mapInfo,
      updatedList[parentIndex].list[subIndex].layerName
    );
    let flLayer = props.map.findLayerById("fl_" + layerId);

    let showTypesWhere = updatedList[parentIndex].list
      .filter((i) => i.where && i.isChecked)
      .map((x) => x.where)
      .join(" or ");

    flLayer.definitionExpression =
      showTypesWhere == "" ? "1 != 1" : showTypesWhere;
  };

  const handleParentCheckboxChange = (parentIndex) => {
    const updatedList = [...legendList];
    updatedList[parentIndex].isChecked = !updatedList[parentIndex].isChecked;

    let layerId = getLayerId(
      props.map.__mapInfo,
      updatedList[parentIndex].layerName
    );
    let flLayer = props.map.findLayerById("fl_" + layerId);

    updatedList[parentIndex].list?.forEach((item) => {
      item.isChecked = updatedList[parentIndex].isChecked;
    });

    setLegendList(updatedList);

    let showTypesWhere = updatedList[parentIndex].list
      .filter((i) => i.where && i.isChecked)
      .map((x) => x.where)
      .join(" or ");

    flLayer.definitionExpression =
      updatedList[parentIndex].isChecked && showTypesWhere != ""
        ? showTypesWhere
        : "1!=1";
  };

  return (
    legendList.length &&
    !isLegnedHide && (
      <div className="legend">
        {legendList.map((item, parentIdx) => {
          return item.list ? (
            <div>
              <div
                className="legendlst"
                style={{
                  display: "flex",
                  gap: "5px",
                  alignItems: "center",
                  marginBlock: "10px",
                  cursor: "pointer",
                }}
              >
                <div
                  onClick={() => changeToggle(item)}
                  style={{ cursor: "pointer" }}
                >
                  {item.toggle ? (
                    <FontAwesomeIcon icon={faCaretDown} />
                  ) : (
                    <FontAwesomeIcon
                      icon={
                        localStorage.getItem("lang") === "ar"
                          ? faCaretLeft
                          : faCaretRight
                      }
                    />
                  )}
                </div>
                <Checkbox
                  key={"ch" + parentIdx}
                  onChange={() => handleParentCheckboxChange(parentIdx)}
                  checked={item.isChecked}
                  style={{ float: "inline-start" }}
                ></Checkbox>
                <img src={item.icon} style={{ width: "20px" }} alt="" />
                <div
                  style={{ textWrap: "wrap", textAlign: "start" }}
                  onClick={() => changeToggle(item)}
                >
                  {item.label}
                </div>
              </div>
              {item.toggle && (
                <div className="legendchildlst">
                  {item.list.map((subItem, subIdx) => {
                    return (
                      <div
                        style={{
                          display: "flex",
                          gap: "5px",
                          alignItems: "center",
                          marginBlock: "10px",
                          cursor: "pointer",
                        }}
                        onMouseEnter={() =>
                          highlightLengendFeatures({
                            layerName: subItem.layerName,
                            where: subItem.where,
                          })
                        }
                        onMouseLeave={() =>
                          removeHighlight({ layerName: subItem.layerName })
                        }
                      >
                        <Checkbox
                          key={"ch" + parentIdx + "_" + subIdx}
                          onChange={() =>
                            handleCheckboxChange(parentIdx, subIdx)
                          }
                          checked={subItem.isChecked}
                          style={{ float: "inline-start" }}
                        ></Checkbox>

                        {subItem.icon ? (
                          <img
                            src={subItem.icon}
                            style={{ width: "20px" }}
                            alt=""
                          />
                        ) : (
                          <img
                            src={"data:image/jpeg;base64," + subItem.image}
                            alt=""
                          />
                        )}
                        <div style={{ textWrap: "wrap", textAlign: "start" }}>
                          {subItem.label}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          ) : (
            <div
              style={{
                display: "flex",
                gap: "5px",
                alignItems: "center",
                marginBlock: "10px",
                cursor: "pointer",
              }}
              onMouseEnter={() => highlightLengendFeatures(item)}
              onMouseLeave={() => removeHighlight(item)}
            >
              {item.icon ? (
                <img src={item.icon} style={{ width: "20px" }} alt="" />
              ) : (
                <img src={"data:image/jpeg;base64," + item.image} alt="" />
              )}
              <div style={{ textWrap: "wrap", textAlign: "right" }}>
                {item.label}
              </div>
            </div>
          );
        })}
      </div>
    )
  );
}
