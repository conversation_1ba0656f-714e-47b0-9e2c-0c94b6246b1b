import { DownCircleFilled } from "@ant-design/icons";
import { Select } from "antd";
import React from "react";
import { useEffect } from "react";
import { RiArrowDropDownFill } from "react-icons/ri";

function SelectComp(props) {
  useEffect(()=>{
    // console.log(props.listName, props.list);
  },[])
  return (
    <>
      <Select
        virtual={false}
        // suffixIcon={<DownCircleFilled />}
        suffixIcon={<RiArrowDropDownFill size={30} />}
        showSearch
        disabled={props.isIncident}
        allowClear={props.allowClear}
        className="dont-show"
        onChange={(name, e) => props.onChange(name, e)}
        value={props.value}
        placeholder={props.placeholder}
        getPopupContainer={(trigger) => trigger.parentNode}
        optionFilterProp="value"
        filterOption={(input, option) => {
          return (
            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          );
        }}
      >
        {props.list.map((s, index) => {
          if (props.listName === "layers")
            return (
              <Select.Option key={index + "ll"} value={s.name} id={s.id}>
                {props.languageStatus === "ar" ? s.arabicName : s.englishName}
              </Select.Option>
            );
          else {
            //  console.log({list:props.list, type:s.type, name:s.name});
            return (
              <Select.Option value={s.type || s.value} key={index + "bound"}>
                {s.name || s.label}
              </Select.Option>
            );
          }
        })}
      </Select>
    </>
  );
}

export default SelectComp;
