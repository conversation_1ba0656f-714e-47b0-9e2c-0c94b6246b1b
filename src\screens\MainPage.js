import React, { useEffect, useState } from "react";
import { FullScreen, useFullScreenHandle } from "react-full-screen";
import { useTranslation } from "react-i18next";
import Fade from "react-reveal";
import { useLocation } from "react-router-dom";

import LoginFormModal from "../components/LoginFormModal";
import ImportFileTable from "../tables/ImportFileTable";
import SearchByMetaData from "../tables/SearchByMetaData";
import Box from "@mui/material/Box";
import Legend from "./legend/Legend";
import LoadingComponent from "./LoadingComponent";
import MainPageHelp from "../containers/helpTooltip/MainPageHelp";
import SideMenu from "../containers/SideMenu";
import { showLoading } from "../helper/common_func";
import LandIcons from "../mapComponents/landsData/LandIcons";
import MapComponent from "../mapComponents/Map";
import MapToolsAndServicesIcons from "../mapComponents/MapToolsAndServicesIcons";
import OuterSearchForm from "../mapComponents/OuterSearchForm";
import InteractiveMapTopBar from "../sidemenu-Components/InteractiveMap/InteractiveMapTopBar";
import usePersistentState from "../helper/usePersistentState";
import DeviceLegend from "../sidemenu-Components/DeviceLegend";

let displayedOuterSearch = [];
export default function MainPage(props) {
  const { i18n } = useTranslation();
  const location = useLocation();
  const [showHelp, setHelpShow] = useState(true);
  const [popupInfo, setPopupInfo] = useState();
  const resultDetailsData = React.useRef({
    detailsData: null,
  });
  const [outerSearchResult, setOuterSearchResult] = usePersistentState(
    "outerSearchResult",
    [],
    "MainPage"
  );

  const setFilteredResult = (result) => {
    setOuterSearchResult(result);
  };

  /*Outer Search Functions*/
  const [outerResultMenuShown, setOuterResultMenu] = usePersistentState(
    "outerResultMenuShown",
    true,
    "MainPage"
  );
  const [outerResultDetailsShown, setOuterResultDetails] = usePersistentState(
    "outerResultDetailsShown",
    false,
    "MainPage"
  );

  const outerOpenResultMenu = (e) => {
    setOuterResultMenu(true);
    setOuterResultDetails(false);
  };
  const closeResultMenu = () => {
    setOuterResultMenu(false);
    setOuterResultDetails(false);
  };
  const outerOpenResultdetails = (e) => {
    setOuterResultMenu(false);
    setOuterResultDetails(true);
    setHelpName("cardDetailsHelp");
  };

  const [importTableDisplay, setImportDisplayTable] =
    useState("closeImportTable");
  const openImportTable = (e) => {
    setImportDisplayTable("importTableShown");
    // setOpenDrawer(false);
  };
  const closeImportTable = (e) => {
    setOpenDrawer(false);
    setImportDisplayTable("importTableHidden");
  };

  const [searchTableDisplay, setSearchTableDisplay] =
    useState("closeSearchTable");
  const openSearchTable = (e) => {
    setSearchTableDisplay("searchTableShown");
    setHelpName("openMetaHelp");
  };
  const closeSearchTable = (e) => {
    setOpenDrawer(false);
    setSearchTableDisplay("searchTableHidden");
  };
  // const showResultCardsHelp = () => {
  //   setHelpName("cardsResultHelp");
  // };
  const showCardDetailsHelp = () => {
    setHelpName("cardDetailsHelp");
  };
  const handle = useFullScreenHandle();
  const [openDrawer, setOpenDrawer] = useState(true);
  const [openMapToolsDrawer, setOpenMapToolsDrawer] = useState(true);

  const [closeBoth, setCloseBoth] = useState(true);

  const [mapObj, setMapLoaded] = useState(null);

  const handleDrawerOpen = () => {
    setOpenDrawer(true);
  };
  //help
  const [helpName, setHelpName] = useState("main");

  const handleDrawerCloseGeneral = () => {
    setOpenDrawer(false);
  };

  const handleMapToolsDrawerClose = () => {
    setOpenMapToolsDrawer(false);
  };

  const handleDrawerCloseButton = () => {
    setOpenDrawer(false);
    setHelpName("openSideMenu");
  };

  const getOuterSearchData = (e) => {
    displayedOuterSearch = e;
  };

  const onMapLoaded = async (map) => {
    //for padding map on zoom
    setMapLoaded(map);
    showLoading(false);
  };

  useEffect(() => {
    console.log(location);
    showLoading(true);
    if (
      location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
      "metaDataSearch"
    )
      openSearchTable();
  }, []);

  // const [openDesDrawer, setOpenDesDrawer] = useState(false);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const { t } = useTranslation("map");
  const clearGeneralSearchData = () => {
    if (resultDetailsData?.current?.detailsData)
      resultDetailsData.current.detailsData = null;
    setOuterSearchResult([]);
    setOuterResultMenu(false);
    setOuterResultDetails(false);
  };

  const { pathname } = useLocation();

  return (
    <div className="mainPage">
      <InteractiveMapTopBar openDrawer={openDrawer} map={mapObj} />

      {popupInfo?.mapPoint &&
      popupInfo?.currentLayer &&
      ["Street_Naming", "Landbase_Parcel"].includes(popupInfo?.currentLayer) ? (
        <LandIcons
          popupInfo={popupInfo}
          map={mapObj}
          setPopupInfo={setPopupInfo}
          currentLayer={popupInfo?.currentLayer}
        />
      ) : null}
      {showHelp && (
        <>
          {!localStorage.getItem("showHelp") && (
            <MainPageHelp helpName={"main"} setHelpShow={setHelpShow} />
          )}
          {!localStorage.getItem("showOpenSideHelp") &&
            helpName === "openSideMenu" && (
              <MainPageHelp helpName={"openSideMenu"} />
            )}
          {!localStorage.getItem("showMetaHelp") &&
            helpName === "openMetaHelp" && (
              <MainPageHelp helpName={"openMetaHelp"} />
            )}
          {/* {!localStorage.getItem("showCardsResultHelp") &&
            helpName === "cardsResultHelp" && (
              <MainPageHelp helpName={"cardsResultHelp"} />
            )} */}
          {!localStorage.getItem("showCardDetailsHelp") &&
            helpName === "cardDetailsHelp" && (
              <MainPageHelp helpName={"cardDetailsHelp"} />
            )}
        </>
      )}
      <LoadingComponent />
      <canvas id="myCanvas" className="canvas-line-zoom" />
      <FullScreen handle={handle} style={{ height: "100%" }}>
        {Object.keys(props.mainData || {}).length && (
          <MapComponent mapload={onMapLoaded} mainData={props.mainData} />
        )}

        <div id="scaleBarDiv" className="scaleBarStyle"></div>
        <div
          id="copmassDiv"
          className="copmassStyle"
          style={
            i18n.language === "ar"
              ? {
                  right: openDrawer
                    ? pathname === "/"
                      ? "220px"
                      : "370px"
                    : "100px",
                }
              : {
                  left: openDrawer
                    ? pathname === "/"
                      ? "220px"
                      : "370px"
                    : "100px",
                }
          }
        ></div>
        <label id="infoXY" className="xyStyle"></label>

        {/* <div className="copy-right">
          {t(
            "Copyright reserved to the National Center for Wildlife Development 2024"
          )}
        </div> */}

        {mapObj && <Legend map={mapObj} />}

        <DeviceLegend />

        {mapObj && (
          <Box sx={{ display: "flex" }}>
            <SideMenu
              toggleCurrentLang={props.toggleCurrentLang}
              languageState={props.languageState}
              mainData={props.mainData}
              map={mapObj}
              resultDetailsData={resultDetailsData}
              setOuterSearchResult={setOuterSearchResult}
              // showResultCardsHelp={showResultCardsHelp}
              outerSearchResult={outerSearchResult}
              showCardDetailsHelp={showCardDetailsHelp}
              displayedOuterSearch={displayedOuterSearch}
              closeImportTable={closeImportTable}
              openImportTable={openImportTable}
              closeSearchTable={closeSearchTable}
              openSearchTable={openSearchTable}
              openDrawer={openDrawer}
              setOpenDrawer={setOpenDrawer}
              handleDrawerCloseButton={handleDrawerCloseButton}
              handleDrawerCloseGeneral={handleDrawerCloseGeneral}
              handleMapToolsDrawerClose={handleMapToolsDrawerClose}
              handleDrawerOpen={handleDrawerOpen}
              outerOpenResultdetails={outerOpenResultdetails}
              outerOpenResultMenu={outerOpenResultMenu}
              closeResultMenu={closeResultMenu}
              outerResultMenuShown={outerResultMenuShown}
              outerResultDetailsShown={outerResultDetailsShown}
              // setOpenDesDrawer={setOpenDesDrawer}
              closeBoth={closeBoth}
              setCloseBoth={setCloseBoth}
              setIsModalOpen={setIsModalOpen}
              setHelpShow={setHelpShow}
            />
            <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
              {/* <div
                className="SideMenuOpenArrow"
                style={{
                  right: i18n.language === "ar" ? "75px" : "",
                  left: i18n.language === "en" ? "80px" : "",
                }}
              >
                <IconButton
                  className="openSideHelp"
                  color="inherit"
                  aria-label="open drawer"
                  onClick={handleDrawerOpen}
                  edge="start"
                  // sx={{
                  //   ...(openDrawer && { display: "none" }),
                  // }}
                >
                  <MdKeyboardDoubleArrowRight />
                </IconButton>
              </div> */}

              {/* <Drawer
                placement={"bottom"}
                onClose={() => setOpenDesDrawer(false)}
                open={openDesDrawer}
                closeIcon={false}
              >
                <DescriptionSearch setOpenDesDrawer={setOpenDesDrawer} />
              </Drawer> */}

              {mapObj && (
                <OuterSearchForm
                  mainData={props.mainData}
                  openDrawer={openDrawer}
                  map={mapObj}
                  setFilteredResult={setFilteredResult}
                  getOuterSearchData={getOuterSearchData}
                  handleDrawerOpen={handleDrawerOpen}
                  setHelpName={setHelpName}
                  outerSearchResult={outerSearchResult}
                  outerOpenResultMenu={outerOpenResultMenu}
                  closeResultMenu={closeResultMenu}
                  outerOpenResultdetails={outerOpenResultdetails}
                  isModalOpen={isModalOpen}
                />
              )}

              {/* <div
              className="languageIcon"
              style={{ right: openDrawer ? "495px" : "295px" }}
            >
              <IconButton color="inherit" onClick={changeLang} value={language}>
                {language}
              </IconButton>
            </div> */}
              {mapObj && (
                <Fade bottom delay={1000}>
                  <div
                    className="mapTools"
                    style={{
                      left:
                        i18n.language === "en"
                          ? openDrawer
                            ? location.pathname.substring(
                                location.pathname.lastIndexOf("/") + 1
                              ) === "generalSearch" ||
                              location.pathname.substring(
                                location.pathname.lastIndexOf("/") + 1
                              ) === "search" ||
                              location.pathname.substring(
                                location.pathname.lastIndexOf("/") + 1
                              ) === "coordinateSearch" ||
                              location.pathname.substring(
                                location.pathname.lastIndexOf("/") + 1
                              ) === "marsed"
                              ? "420px"
                              : location.pathname.substring(
                                  location.pathname.lastIndexOf("/") + 1
                                ) == "" ||
                                location.pathname.substring(
                                  location.pathname.lastIndexOf("/")
                                ) == process.env.PUBLIC_URL
                              ? "170px"
                              : "345px"
                            : "50px"
                          : "unset",
                    }}
                  >
                    {/* <MapTools
                      languageState={props.languageState}
                      map={mapObj}
                      mainData={props.mainData}
                      openDrawer={openDrawer}
                    /> */}
                  </div>
                </Fade>
              )}

              <MapToolsAndServicesIcons
                languageState={props.languageState}
                mainData={props.mainData}
                setPopupInfo={setPopupInfo}
                popupInfo={popupInfo}
                outerOpenResultMenu={outerOpenResultMenu}
                closeResultMenu={closeResultMenu}
                setFilteredResult={setFilteredResult}
                handleDrawerOpen={handleDrawerOpen}
                handle={handle}
                map={mapObj}
                outerResultMenuShown={setOuterResultMenu}
                setHelpShow={setHelpShow}
                clearGeneralSearchData={clearGeneralSearchData}
              />

              {/*<LoginFormModal
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
              />*/}

              {/* <MapToolsMenu
                languageState={props.languageState}
                mainData={props.mainData}
                setPopupInfo={setPopupInfo}
                popupInfo={popupInfo}
                outerOpenResultMenu={outerOpenResultMenu}
                closeResultMenu={closeResultMenu}
                setFilteredResult={setFilteredResult}
                handleDrawerOpen={handleDrawerOpen}
                handle={handle}
                map={mapObj}
                outerResultMenuShown={setOuterResultMenu}
                setHelpShow={setHelpShow}
                // openMapToolsDrawer={openMapToolsDrawer}
                // setOpenMapToolsDrawer={setOpenMapToolsDrawer}
                setIsModalOpen={setIsModalOpen}
                closeBoth={closeBoth}
                setCloseBoth={setCloseBoth}
              /> */}

              {location.pathname.substring(
                location.pathname.lastIndexOf("/") + 1
              ) === "import" ? (
                <ImportFileTable
                  mainData={props.mainData}
                  importTableDisplay={importTableDisplay}
                  openImportTable={openImportTable}
                  closeImportTable={closeImportTable}
                />
              ) : null}
              {location.pathname.substring(
                location.pathname.lastIndexOf("/") + 1
              ) === "metaDataSearch"
                ? mapObj && (
                    <SearchByMetaData
                      mainData={props.mainData}
                      openDrawer={openDrawer}
                      searchTableDisplay={searchTableDisplay}
                      closeSearchTable={closeSearchTable}
                      openSearchTable={openSearchTable}
                      languageState={props.languageState}
                      map={mapObj}
                    />
                  )
                : null}
            </Box>
          </Box>
        )}
      </FullScreen>
    </div>
  );
}
