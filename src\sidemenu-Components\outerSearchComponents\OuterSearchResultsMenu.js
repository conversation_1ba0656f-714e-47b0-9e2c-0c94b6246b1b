import React, { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Row, Col, Button, message, Switch } from "antd";
import { Tooltip } from "@mui/material";
import googleLocation from "../../assets/images/googleLocation.png";

import {
  faAngleDoubleDown,
  faSearchPlus,
  faExpandArrowsAlt,
} from "@fortawesome/free-solid-svg-icons";
import { toArabic } from "arabic-digits";
import {
  clearCanvasLine,
  clearGraphicLayer,
  getFeatureDomainName,
  highlightFeature,
  navigateToGoogle,
  queryTask,
  zoomToFeatureByObjectId,
  convertToArabic,
  removeHighlightedAreasWithDimming,
} from "../../helper/common_func";

// import { layersSetting } from "../../helper/layers";
import { useTranslation } from "react-i18next";

import result_num_img from "../../assets/images/dashboard/foundation_results.png";
import area_num_img from "../../assets/images/dashboard/foundation_results (1).png";

export default function OuterSearchResultsMenu(props) {
  const { t } = useTranslation("map", "print", "layers", "common");
  const [result, setShownData] = useState(null);

  useEffect(() => {
    removeHighlightedAreasWithDimming(props.map);
  });

  const navigateGeometry = (index) => {
    let featDataGeom = result.list[index]?.geometry;
    if (featDataGeom) {
      if (
        featDataGeom?.rings?.length ||
        featDataGeom?.paths?.length ||
        featDataGeom?.x
      )
        navigateToGoogle(
          featDataGeom.latitude || featDataGeom.centroid.latitude,
          featDataGeom.longitude || featDataGeom.centroid.longitude
        );
    } else {
      zoomToFeatureByObjectId(result[index], props.map, false, (feature) => {
        result[index].geometry = feature.geometry;
        if (feature?.rings?.length || feature?.paths?.length || feature?.x)
          navigateToGoogle(
            result[index].geometry.latitude ||
              result[index].geometry.centroid.latitude,
            result[index].geometry.longitude ||
              result[index].geometry.centroid.longitude
          );
      });
    }
  };

  if (
    result &&
    result.length &&
    !result.find(
      (x) =>
        !(
          x.geometry?.rings?.length ||
          x.geometry?.paths?.length ||
          x.geometry?.x
        )
    )
  ) {
    highlightFeature(result, props.map, {
      layerName: "generalSearchResultGraphicLayer",
      isZoom: true,
      zoomDuration: 1000,
      isDashStyle: true,
    });
  }

  useEffect(() => {
    if (props.outerSearchResult && props.outerSearchResult.length == 1) {
      props.outerOpenResultdetails(props.outerSearchResult[0]);
    }
    clearGraphicLayer("generalSearchResultGraphicLayer", props.map);
    let features = props.outerSearchResult?.list;
    if (features?.length) {
      //zoomToAll(features);
    }
    setShownData(props.outerSearchResult);
    onChangeSwitchButton(true, features);
    return () => {
      // clearGraphicLayer("ZoomGraphicLayer", props.map);
      // clearGraphicLayer("SelectGraphicLayer", props.map);
      setShownData(null);
    };
  }, []);

  const gotoFeature = (feature) => {
    /*if (feature.geometry) {
      highlightFeature(feature, props.map, {
        layerName: "SelectGraphicLayer",
      });
    }*/
  };

  const onMouseMoveOnFeature = (feature, e) => {
    if (
      feature.geometry?.rings?.length ||
      feature.geometry?.paths?.length ||
      feature.geometry?.x
    ) {
      if (!checkedSwitchButton) {
        props.map.findLayerById("generalSearchResultGraphicLayer").removeAll();
      }

      highlightFeature(feature, props.map, {
        layerName: "SelectGraphicLayer",
        isAnimatedLocation: true,
        highlighColor: [0, 255, 255, 0.5],
        strokeColor: "black",
        fillColor: [0, 255, 255, 0.5],
        noclear: true,
      });
      //  drawLine({ feature: feature, map: props.map, event: e });
    }
  };

  const clearHighlightFeatures = () => {
    props.map.findLayerById("SelectGraphicLayer").removeAll();
    clearCanvasLine();
  };

  const clearFeatures = () => {
    props.map.findLayerById("ZoomGraphicLayer").removeAll();
    props.map.findLayerById("SelectGraphicLayer").removeAll();
    props.map.findLayerById("generalSearchResultGraphicLayer").removeAll();
    clearCanvasLine();
  };

  const getDisplayField = (attributes) => {
    let layersSetting = props.mainData.layers;
    let displayField =
      attributes[layersSetting[attributes["layerName"]].displayField];
    if (layersSetting[attributes["layerName"]].name == "SPECIE") {
      displayField =
        attributes[layersSetting[attributes["layerName"]].displayField] == null
          ? attributes.AR_SPECIE
          : attributes[layersSetting[attributes["layerName"]].displayField];
    }
    return displayField;
  };

  const searchForMoreData = async (e) => {
    let queryObj = { ...result.queryObj };
    queryObj.start = result.list.length;

    queryTask({
      ...queryObj,
      callbackResult: ({ features }) => {
        if (features.length) {
          getFeatureDomainName(features, queryObj.layerdId).then((res) => {
            let mappingRes = res.map((f) => {
              return {
                layerName: queryObj.layerName,
                id: f.attributes["OBJECTID"],
                ...f.attributes,
                geometry: f.geometry,
              };
            });
            setShownData({
              ...result,
              queryObj: queryObj,
              list: result.list.concat(mappingRes),
            });

            zoomToAll(result.list.concat(mappingRes));
          });
        }
      },
      callbackError(error) {
        console.log({ error });
        message.open({
          type: "info",
          content: t("common:retrievError"),
        });
      },
    });
  };

  const openFeatureDetails = (attributes) => {
    //TODO: in case of royal or private lands fetch arc server to get land data and set its attributes if found
    props.outerOpenResultdetails(attributes);
    clearCanvasLine();
  };

  const zoomToAll = (feats, checked) => {
    clearGraphicLayer("generalSearchResultGraphicLayer", props.map);
    if (feats) {
      if (feats.length && feats.find((d) => d.geometry)) {
        let features = feats.filter((d) => {
          if (
            d.geometry?.rings?.length ||
            d.geometry?.paths?.length ||
            d.geometry?.x
          )
            return d;
          else return;
        });
        if (features?.length)
          highlightFeature(features, props.map, {
            layerName: "generalSearchResultGraphicLayer",
            isZoom: true,
            zoomDuration: 300,
            notExpandLevel: true,
          });
      }
    }
  };

  const [checkedSwitchButton, setCheckedSwitchButton] = useState(false);

  useEffect(() => {
    if (result && result.list) {
      //zoomToAll(result.list);
    }
  }, [result]);

  const onChangeSwitchButton = (checked, features) => {
    if (checked) {
      zoomToAll(features, checked);
    } else {
      clearFeatures();
    }
    setCheckedSwitchButton(checked);
  };

  return (
    <>
      {/*  */}
      <div class="resultTitle">
        {result && result.statisticsInfo && (
          <div style={{ textAlign: "right", fontSize: "13px", color: "#fff" }}>
            <img src={result_num_img} alt="" />
            <strong className="px-2">{t("map:mapTools.resultNum")} </strong>
            {toArabic(result.statisticsInfo.COUNT)}
          </div>
        )}

        {result && result.statisticsInfo && result.statisticsInfo.AREA && (
          <div style={{ textAlign: "left", fontSize: "13px" }}>
            <img src={area_num_img} alt="" />
            <strong className="px-2"> {t("map:mapTools.area")} </strong>
            {toArabic((result.statisticsInfo.AREA))}{" "}
            {t("map:km2")}
          </div>
        )}
        {result && result.statisticsInfo && (
          <div style={{ textAlign: "left" }}>
            <Button
              className="tableHeaderBtn outerSearchZoomAll"
              onClick={() => {
                zoomToAll(result.list);
              }}
            >
              <Tooltip placement="leftTop" title={t("map:mapTools.zoomToAll")}>
                <FontAwesomeIcon icon={faExpandArrowsAlt} />
              </Tooltip>
            </Button>
          </div>
        )}
        {result && (result.list || result).length > 0 && (
          <Tooltip title={t("hideOtherData")}>
            <Switch
              defaultChecked
              onChange={(checked) =>
                onChangeSwitchButton(
                  checked,
                  result.list ? result.list : result
                )
              }
            />
          </Tooltip>
        )}
      </div>
      {/*  */}

      <div
        className="generalSearchResult "
        style={{
          height:
            !result?.statisticsInfo &&
            !result?.statisticsInfo?.AREA &&
            "calc(100vh - 212px)",
        }}
      >
        {result && (result.list || result).length > 0 ? (
          (result.list || result).map((attributes, index) => (
            <div className="generalSearchCard" key={index}>
              <Row
                onMouseLeave={() => {
                  clearHighlightFeatures();
                  if (checkedSwitchButton) {
                    //zoomToAll(result.list);
                  }
                }}
                onMouseMove={(e) => onMouseMoveOnFeature(attributes, e)}
                onMouseEnter={gotoFeature.bind(this, attributes)}
                style={{ alignItems: "center" }}
              >
                <Col span={16} onClick={() => openFeatureDetails(attributes)}>
                  <h5>{convertToArabic(getDisplayField(attributes))}</h5>
                </Col>
                <Col span={8} style={{ margin: "auto", textAlign: "center" }}>
                  <Tooltip title={t("map:mapTools.zoomIn")} placement="top">
                    <button
                      className="tooltipButton"
                      onClick={() => {
                        //todo: call zoomToFeatureBySpatialID in case of royal and private land
                        zoomToFeatureByObjectId(
                          attributes,
                          props.map,
                          false,
                          (feature) => {
                            (result.list || result)[index].geometry =
                              feature.geometry;
                          }
                        );
                      }}
                    >
                      <FontAwesomeIcon
                        className="zoomIcon"
                        icon={faSearchPlus}
                        style={{
                          cursor: "pointer",
                        }}
                      />
                    </button>
                  </Tooltip>

                  <Tooltip
                    title={t("map:mapToolsServices.googleMaps2")}
                    placement="top"
                  >
                    <button
                      className="tooltipButton"
                      onClick={() => navigateGeometry(index)}
                    >
                      <img
                        style={{ width: "20px" }}
                        src={googleLocation}
                        alt="googleLocation"
                      />
                    </button>
                  </Tooltip>
                </Col>
              </Row>
            </div>
          ))
        ) : (
          <>
            <div
              style={{
                textAlign: "center",
                marginTop: "10px",
                color: "#fff",
                fontSize: "16px",
              }}
            >
              {t("common:NoDataWarning")}
            </div>
          </>
        )}

        {result &&
          result.list &&
          result.list.length >= window.paginationCount && (
            <div style={{ textAlign: "center" }}>
              <button
                onClick={searchForMoreData}
                className="seeMoreBtn px-3 py-2  mt-3"
                size="large"
                htmlType="submit"
              >
                <FontAwesomeIcon
                  className="closeIconsIcon"
                  icon={faAngleDoubleDown}
                  style={{ cursor: "pointer" }}
                />
              </button>
            </div>
          )}
      </div>
    </>
  );
}
