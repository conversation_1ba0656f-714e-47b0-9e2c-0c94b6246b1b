import React, { useEffect, useRef, useState } from "react";
import { Row, Col, Button } from "antd";
import measure1 from "../assets/images/measure1.png";
import measure2 from "../assets/images/measure2.png";
import measure3 from "../assets/images/measure3.png";
import esriGreen from "../assets/images/esriGreen.svg";
import esriCursor from "../assets/images/esriCursor.png";
import { Tooltip } from "@mui/material";
import { Table, Container } from "react-bootstrap";
import Measurement from "@arcgis/core/widgets/Measurement";
import { addPictureSymbol } from "../helper/common_func";
import { useTranslation } from "react-i18next";

export default function MeasureTool(props) {
  const [measureType, setMeasureType] = useState("CoordinateMeasure");
  const { t } = useTranslation("common", "map");

  const [mousePoint, setMousePoint] = useState(null);
  const [mouseGreenPoint, setMouseGreenPoint] = useState(null);

  const componentRef = useRef({});
  const { current: measurmentTool } = componentRef;

  useEffect(() => {
    measurmentTool.current = new Measurement({
      view: props.map.view,
      activeTool: "",
      container: document.getElementById("measurmentTool"),
    });

    props.map.view.on("pointer-move", (event) => {
      if (!measurmentTool.current.activeTool) {
        let point = props.map.view.toMap({ x: event.x, y: event.y });
        setMousePoint(point);
      }
    });

    var clickHandler = props.map.view.on("click", (event) => {
      if (!measurmentTool.current.activeTool) {
        setMouseGreenPoint(event.mapPoint);

        props.map.findLayerById("identifyGraphicLayer").removeAll();
        addPictureSymbol(
          event.mapPoint,
          esriGreen,
          "identifyGraphicLayer",
          props.map,
          16,
          26
        );
      }
      if (measurmentTool.current.activeTool == "area") {
        if (
          measurmentTool.current?.domNode?.children[0]?.children[0]?.children[1]
            ?.children[0]?.children[0]
        )
          measurmentTool.current.domNode.children[0].children[0].children[1].children[0].children[0].innerHTML =
            t("Area");
      }
      if (measurmentTool.current._widgets.get("distance")) {
        measurmentTool.current._widgets.get(
          "distance"
        ).messagesUnits.units.inches.abbr = "بوصة";
      }
    });

    CoordinateMeasure();
    return () => {
      clickHandler.remove();
      // measurmentTool.current.clear();
      // props.map.findLayerById("identifyGraphicLayer").removeAll();
    };
  }, []);

  const distanceMeasure = (e) => {
    props.map.findLayerById("identifyGraphicLayer").removeAll();
    setMeasureType("distanceMeasure");
    measurmentTool.current.activeTool = "distance";
  };
  const spaceMeasure = (e) => {
    props.map.findLayerById("identifyGraphicLayer").removeAll();
    setMeasureType("spaceMeasure");
    measurmentTool.current.activeTool = "area";
  };
  const CoordinateMeasure = (e) => {
    setMeasureType("CoordinateMeasure");
    measurmentTool.current.clear();
  };

  const getFlooredFixed = (v, d) => {
    return (Math.floor(v * Math.pow(10, d)) / Math.pow(10, d)).toFixed(d);
  };

  return (
    <div>
      <Container fluid className="coordinates mt-2 measurePage">
        <Row justify="start">
          <Col style={{ display: "flex", flexDirection: "row", gap: "8px" }}>
            <Tooltip placement="top" title={t("locationCoord")}>
              <Button
                className="CoordinateMeasureBtn"
                onClick={CoordinateMeasure}
                id={measureType === "CoordinateMeasure" ? "activeCooBtn" : ""}
              >
                <img src={measure3} alt="CoordinateMeasure" />
                <span>{t("locationCoord")}</span>
              </Button>
            </Tooltip>

            <Tooltip
              placement="top"
              title={t("areaCalc")}
              className="MuiTooltipStyle"
            >
              <Button
                className="spaceMeasureBtn"
                onClick={spaceMeasure}
                id={measureType === "spaceMeasure" ? "activeSpaceBtn" : ""}
              >
                <img src={measure1} alt="spaceMeasure" />
                <span>{t("areaCalc")}</span>
              </Button>
            </Tooltip>

            <Tooltip placement="top" title={t("measureDisLength")}>
              <Button
                className="distanceMeasureBtn"
                onClick={distanceMeasure}
                id={
                  measureType === "distanceMeasure" ? "activeDistanceBtn" : ""
                }
              >
                <img src={measure2} alt="CoordinateMeasure" />
                <span>{t("measureDisLength")}</span>
              </Button>
            </Tooltip>
          </Col>
        </Row>

        <div
          id="measurmentTool"
          style={
            measureType != "CoordinateMeasure"
              ? { display: "block", textAlign: "right" }
              : { display: "none", textAlign: "right" }
          }
        ></div>

        {measureType === "CoordinateMeasure" && (
          <div style={{ margin: "10px" }}>
            <Table className="table">
              <tbody>
                <tr>
                  <td></td>
                  <th style={{ textAlign: "center" }}>
                    <span>{t("longitude")}</span>
                  </th>
                  <th style={{ textAlign: "center" }}>
                    <span>{t("Latitude")}</span>
                  </th>
                </tr>
                <tr>
                  <td>
                    <img
                      src={esriCursor}
                      alt=""
                      style={{ filter: "brightness(0) invert(1)" }}
                    />
                  </td>
                  <td style={{ textAlign: "center" }}>
                    <span>
                      {mousePoint
                        ? getFlooredFixed(mousePoint.longitude, 6)
                        : "----"}
                    </span>
                  </td>
                  <td style={{ textAlign: "center" }}>
                    <span>
                      {mousePoint
                        ? getFlooredFixed(mousePoint.latitude, 6)
                        : "----"}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td>
                    <img
                      src={esriGreen}
                      style={{
                        width: "15px",
                        filter: "brightness(0) invert(1)",
                      }}
                      alt=""
                    />
                  </td>
                  <td style={{ textAlign: "center" }}>
                    <span>
                      {mouseGreenPoint
                        ? getFlooredFixed(mouseGreenPoint.longitude, 6)
                        : "----"}
                    </span>
                  </td>
                  <td style={{ textAlign: "center" }}>
                    <span>
                      {mouseGreenPoint
                        ? getFlooredFixed(mouseGreenPoint.latitude, 6)
                        : "----"}
                    </span>
                  </td>
                </tr>
              </tbody>
            </Table>
            {mouseGreenPoint && (
              <>
                <div
                  style={{
                    textAlign: "center",
                    padding: "5px",
                    borderRight: "2px solid #B45333",
                    borderRadius: "15px",
                    marginBlock: "10px",
                    color: "#fff",
                    background: "rgb(143 143 143 / 70%)",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      padding: "10px",
                      borderBottom: "2px dotted #fff",
                    }}
                  >
                    <div>{t("x")}</div>
                    <div>
                      {mouseGreenPoint
                        ? getFlooredFixed(mouseGreenPoint.x, 6)
                        : "----"}
                    </div>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      padding: "10px",
                    }}
                  >
                    <div>{t("y")}</div>
                    <div>
                      {mouseGreenPoint
                        ? getFlooredFixed(mouseGreenPoint.y, 6)
                        : "----"}
                    </div>
                  </div>
                </div>
              </>
            )}

            <label
              style={{
                whiteSpace: "normal",
                textAlign: "right",
                fontWeight: "bold",
              }}
            >
              {t("clickMapXY")}
            </label>
          </div>
        )}
      </Container>
    </div>
  );
}
