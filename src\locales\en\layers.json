{"munName": "Municipality name", "govMunName": "Governrat/Municipality name", "subMunName": "Sub-municipality name", "locationName": "Location name", "reportNum": "Report number", "districtName": "District name", "districtNum": "District number", "streetName": "Street name", "streetNum": "Street number", "streetClassification": "Street Classfication", "streetType": "Street Type", "streetWidth": "Street Width", "cityNum": "City number", "reportStatus": "Report status", "MAINCLASSIFICATIONID": "Main Classification", "SUBCLASSIFICATIONID": "Side Classification", "SUBSUBCLASSIFICATIONID": "Detailed Classification", "reportClassification": "Report classification", "orderNum": "Order number", "dateCreated": "Date created", "closeDate": "Close date", "latitude": "Latitude", "longitude": "Longitude", "lngCentroid": "Longitude of centroid", "latCentroid": "Latitude of centroid", "priorityzZomeName": "Priority Zone", "from": "From", "to": "To", "toDate": "Date To", "fromDate": "Date From", "cityName": "City name", "notes": "Notes", "opticalDistortionType": "Optical distortion type", "opticalDistortion": "Optical distortion", "reports": "Reports", "buildName": "Building name", "buildStatus": "Builing status", "buildType": "Building type", "ownerName": "Owner name", "floorsNum": "Floors number", "rentValue": "Rent value", "buildingMaterialType": "Building material type", "roadCondition": "Road status", "sector": "Sector", "sectorClassfication": "Sector classfication", "subSector": "Sub-sector", "indicatorName": "Indicator name", "indicatorClassfication": "Indicator classfication", "inventoryOfAmanaBuildings": "Inventory of amana buildings", "govName": "Government name", "inicatorValue": "Indicator value", "indicatorDetails": "Indicator details", "indicatorIdentify": "Indicator identifiction", "indicatorMeasureUnit": "Indicator measurment unit", "marsad": "Marsad", "bandNum": "Band number", "sectionNum": "Sector number", "sectionevl": "Sector evaluation", "CostOfMaintenance": "Cost of maintenance", "maintenancePriority": "Maintenance priority", "pavementConditionIndicator": "Pavement condition indicator", "damageTypeCode": "Damage type code", "suggestedRepairType": "Suggested repair type", "proposedPavementConditionIndicator,": "Proposed pavement condition indicator", "cameras": "Cameras", "cameraLocation": "Camera location", "entertainment": "Entertainment", "stationNum": "Station number", "path": "Path", "pathName": "Path name", "transportPaths": "Transport paths", "transportStations": "Transport stations", "stationArea": "Station area", "daysNum": "Days number", "lightPoleNum": "Light plot number", "address": "Address", "plateNum": "Plate number", "trafficCensus": "Traffic census", "investLands": "Investment lands", "projectName": " Project name", "contractNum": "Contract number", "contractName": "Contract name", "fiveYearAnnuaPlan": "Five years annual plan", "QuaysAndMedianIslands": "Quays and medianIs lands", "ownerShipType": "Ownership type", "street": "Street", "mobileNum": "Mobilenumber", "planNum": "Plan number", "landNum": "Land number", "storeName": "Store name", "storeLicenseNum": "Store license number", "storeLicenseDate": "Store license date", "landLicenses": "Land licenses", "licenseNum": "License number", "licenseYear": "License year", "landNumONPlan": "Land number on plan", "gasStationEastMainRoad": "Gas stations in the eastern region of the main roads", "stationName": "Station name", "amana": "<PERSON><PERSON>", "roadName": "ٌRoad name", "roadNum": "Road number", "stationClassification": "Station classification", "billNum": "Bill number", "billValue": "Bill values", "natureOfProgram": "Nature of the program", "categoryOfProgram": "Program category", "areaName": "Area name", "areaNum": "Area number", "sectorsNum": "Sectors number", "sectorNumer": "Sector number", "sectorAccounts": "Sector accounts", "maintenance": "Mintenance", "maintenance1": "Maintenance1", "S_strait": "Strait_S", "C_strait": "Strait_C", "strait1": "Strait_1", "specialTR": "خاص_TR", "special1": "Special__1", "special2": "Special__2", "sectorAR": "Sector AR", "installQUA": "Install__QUA", "protectionNum": "Protection number", "O_strait": "Strait_O", "scanDate": "Scan date", "roadClassification": "Road classification", "Perimeter": "Perimeter", "SectorIdNum": "Sector identification number", "digramNotes": "Diagram/ Notes", "roadPolyAsphalt": "Road polygons - asphalt", "devPriUrbElem": "Development priorities - urban elements at the neighborhood level", "storeLicense": "Store license", "SurveyingPlotsData": "Surveying plots data", "northCoord": "Northern coordinates", "eastCoord": "Easern coordinates", "95Petrolconsumption": "Petrol 95 consumption", "95PetrolPumps": "Petrol 95 puumps", "91Petrolconsumption": "Petrol 91 consumption", "91PetrolPumps": "Petrol 91 puumps", "dieselConsumption": "Diesel consumption", "dieselPumps": "Diesel pumps", "keroseneConsumption": "Kerosene consumption", "kerosenePumps": "Kerosene pumps", "constituency": "Constituency", "constituencyNum": "Constituency number", "Constituencies": "Constituencies", "buildingReq": "Building requirements", "MunBoundaries": "Municipality boundaries", "munType": "Municipality type", "amanaName": "Amana name", "District": "District", "DistrictBound": "District boundaries", "plansBound": "Plans boundaries", "planName": "Plan name", "titleDeedNum": "Title Deed No", "planArea": "Plan area-KM2", "planclassif": "Plan classfication", "planType": "Plan type", "planStatus": "Plan status", "partitionNum": "Partition number", "districtDivision": "District Partition", "categoryDivision": "Category/Zone Partition", "megawrahDivision": "Blocks Partition", "partitionArea": "Partition area", "partitionDesc": "Partition description", "partitionType": "Partition type", "blockNum": "Block number", "subdivisionType": "Subdivision Type", "subdivisionDesc": "Subdivision Description", "landType": "Land Type", "landsData": "Lands data", "archive": "Archive Data", "fileNameArchive": "Archive file name", "unplannedLandArchive": "Unplanned Parcels Archive", "GalleryArchive": "Gallery Archive", "buildLicenses": "Building licenses", "propertyGrantData": "Property grant data", "salesLandsData": "Sales lands sata", "landsAllotment": "Lands allotment", "krokySubmission": "Kroky Submissions", "farzSubmission": "Farz Submissions", "contractUpdateSubmission": "Contract Update Submissions", "zwa2edSubmission": "<PERSON><PERSON><PERSON> Submissions", "mshare3Submission": "mshare' khdamya Submissions", "partitionBound": "Partition boundaries", "UrbanScaleType": "Urban scale type", "UrbanScaleBound": "Urban scale boundaries", "subMunBound": "Sub-municipality boundaries", "bidNum": "Bid number", "latitudeEngCener": "The latitude coordinates of the engineering center", "longitudeEngCenter": "The longitude coordinates of the engineering center", "useCode": "Use code", "buildConditionDesc": "Building conditions description", "locationstaus": "Location status", "areaM2": "Area M2", "propInvctivity": "The proposed investment activity", "invSiteType": "Type of investment site", "suggStartDate": "Suggested start date", "suggFinishDate": "Suggested finish date", "mainSideName": "Main side name", "projectStatus": "Project status", "Administration": "Administration", "contractor": "Contractor", "Consultant": "Consultant", "projectsData": "Projects data", "transformer": "Transformer", "contractorName": "Contractor name", "ColumnNum": " Column number", "counterNum": "Counter number", "lightingPanels": "Lighting panels", "installDate": "Installation date", "StreetLightStatus": "street light status", "lampsNum": "Lamps number", "streetLightMaterial": "Street light material", "streetLightHright": "Street light height", "lampType": "Lamp type", "streetLightServType": "Street light service type", "lightPoles": "Light poles", "engAmanaName": "Amana name in english", "amanaBound": "Amana boundaries", "gasStationFromGoogleToMainRoad": "Gas stations added from Google Eastern Province to the main roads", "gasStationsOutsideMainRoad": "Gas stations outside the metropolis of the main roads", "gasstationsInsideMainRoads": "Gas stations inside the metropolis of the main roads", "superAuthName": "Supervising authority name", "contractorNum": "Contractor number", "orderDate": "Order date", "drillingLength": "Drilling length", "drillingTime": "Drilling time", "drillingliceStartDate": "Driling license start date", "eventStartDate": "Event start date", "eventFinishDate": "Event finish date", "sggestedPlace": "Suggested place", "events": "Events", "TheExecutingAgency": "The executing agency", "excavationLicense": "Excavation license", "mainUseOfLand": "Main use of land", "subUseOfLand": "Sub-use of land", "orderType": "Order type", "regeqNum": "Region Requirement Number", "reaFromInstru": "area from the instrument", "letterNum": "letter number", "speechHistory": "Date of the Speech", "HijriDate": "Hijri date", "indusUnitsNum": "The number of industrial units.", "houseUnitsNum": "The number of housing units", "servNum": "number of services", "IdNum": "Identification Number", "IdHistory": "identity date", "idSource": "identity source", "accuracyStatLevel": "The level of accuracy of the statement", "unitsNum": "Units number", "landicensNum": "Land license number", "licenseDate": "License date", "orderStatus": "Order status", "currentStatus": "Current status", "ApproxArea": "Approximate area m2", "DetailedUse": "Detailed use", "existBuildName": "The name of the existing building", "completeRate": "Completion rate%", "primaryHistory": "Primary date", "primaryHistryReform": "Primary date Reform", "initExecDate": "Initial Execution Date", "constTeamSuper": " Contractor Team Supervisor", "finalDate": "Final date", "finalDateRepar": "Final Date Repair", "execDeadline": "Execution deadline", "repairSiteDelDate": "Repair site delivery date", "SiteExecDate": "Site Execution Date of Delivery", "neccEquip": " Necessary Equipment", "laborNeedNum": "Number of Labor Needed", "critSiteDesc": "Critical Site Description", "sitesOfBridgesGeneralAdmin": "Sites of bridges and tunnels - General Administration of Supervision", "Region": "Region", "amanaSuperVName": "Amana supervisor name", "criticalManage": "Critical Sites - Disaster Management", "govBuildMaintenSite": "Government Building Maintenance Sites", "whiteLndsUncElec": "White Lands - Uncovered - Electricity", "elecresUse2015": " Electricity meters for residential use 2015 (electricity)↵", "elecresUse2016": "Electricity meters for residential use 2016 (electricity)", "buildUpLandArea": "built-up land-area(h)-water", "buildUpLandCover": "built-up land-covered-water", "buildUpLandNotCover": "Built-up land - not covered - with water", "planLandArea": "Planned land - area (ha) - water", "planLandCovered": "Planned Land - Covered - Water", "planLandUnCover": "Planned Lands - Uncovered - Water", "whiteLandArea": "White Lands - Area (ha) - Water", "whiteLandCover": "White Lands - Covered - Water", "whiteLandUnCover": "White Lands - Uncovered - Water", "servNameGirlTutorial": "Service Name - Girls Tutorial", "servLevelGirlEduc": "Service Level - Girls' Education", "classNumGirls": "Number of Classes - Girls' Education", "studentNumGirls": "Number of Female Students - Girls' Education", "buildOwnerShipGirls": "Building Ownership - Girls Education", "servNameTutorialBoys": "Service Name - Tutorial Boys", "servLevelEducBoys": "Service Level - Educational Boys", "classNumBoys": "Number of Classes - Boys' Education", "studentsNumBoys": "Number of Students - Boys' Education", "buildOwnerShipBoys": " Building Ownership - Boys Education", "floors": "Floors", "canaddFloor": "Can add floor", "description": "Description", "buildBlockCoeff": "Building block coefficient", "floorHeight": "Floor height", "buildRation": "Building ratio", "couponspace": "Coupon space", "minFacade": "Minimal_facade", "faceBounce": "Facing_bounce", "revAsspects": "Reversal_aspects", "backback": "Back Back", "streets": "Streets", "pointNumInLand": "The point number of the plot of land", "xCoord": "X-coordinates", "yCoord": "Y-coordinates", "longitudeCoord": "Longitude coordinates", "latitudeCoord": "Latitude coordinates", "tableCoordCorner": "Table of the coordinate points of the corners", "boundaryNum": "Boundary number", "ribstartPoint": "Rib starting point number", "ribfinishPoint": "Rib end point number", "boundaryLength": "Boundary length-M", "boundaryDesc": "Boundary description", "boundaryDirection": "Boundary Direction", "landSideTable": "Land side table", "liceYearIssuance": "Year of issuance of the license", "liceDateIssuance": "Date of issuance of the license", "accuracyStat": "Accuracy of the statement", "blockUse": "Block use", "blockArea": "Block area", "blocksBound": "Blocks boundaries", "servName": "Service name", "servDesc": "Service description", "servType": "Service type", "servsubUse": "Sub-use of service", "serviceData": "Services data", "cardNum": "Card number", "citizenName": "Citizen name", "reqNum": "Request number", "NotLettNum": "Notary letter number", "decisionNum": "Decision number", "decisionDate": "Decision date", "activityName": "Activity name", "zoomIn": "Zoom in", "create_date": "Submission Date", "export_date": "Submission Date", "request_no": "Submission Number", "planNoLandsStatistics": "Plan Num. Data", "planNo": "Plan Num.", "landNo": "Land Num.", "orgName": "The beneficiary", "licPurpose": "purpose of allocation ", "areaByM2": "Allocated land area (m2)", "typeLic": "Allocation type", "decisionNoLic": "Allocation decision num.", "dateMilady": "Allocation date", "dateHijri": "Allocation date (Hijri)", "buildingDesc": "Building Description", "cornerNo": "Corner Num.", "startBoundary": "Boundary Start Point", "endBoundary": "Boundary End Point", "akarReportBtn": "Ownership Report", "googleMapsLink": "Google Maps Link", "northBoundary": "Northern Boundary", "southBoundary": "Souther Bo<PERSON>", "eastBoundary": "Eastern Boundary", "westBoundary": "Western Boundary", "ArReserveCategory": "Reserve Category", "ArncwCategory": "NCW Category", "ArKingdom": "Kingdom", "ArPhylum": "phylum", "ArClass": "Class", "ArOrder": "Order", "ArFamily": "Family", "ArGenus": "Genus", "ArSpecie": "Species", "ArReserveName": "Reserve Name", "ArRegion": "Region", "ArCategory": "Category", "RESERVESTATUS": "Resrve Status", "Category": "Category", "PROTECTED_AREA_STATUS": "Protected Area Status", "EN_PROTECTED_AREA_STATUS": "Protected Area Status", "PROTECTED_AREA_NAME": "Protected Area Name", "MARINE_BIODIVERSITY": "Marine Biodiversity", "MARINE_SPECIE": "Marine Specie", "HABITAT": "Habitat", "MANGROVE": "Mangrove", "CORAL_REEF": "Coral Reef", "SEA_GRASS": "Sea Grass", "TERRESTRIAL_BIODIVERSITY": "Terrestrial Biodiversity", "TERRESTRIAL_SPECIE": "Terrestrial Specie", "PROTECTED_AREA_CENTROID": "Protected Area Centroid", "PROTECTED_AREA_BOUNDARY": "Protected Area Boundary", "REGION": "Region", "ECOLOGY": "Ecology", "WET_LAND": "Wet Land", "ECO_REGION": "Eco Region", "ECO_SYSTEM": "Eco System", "ECO_HOTSPOT": "Eco Hotspot", "ISLAND": "Island", "PATHYMETRIC_MAPPING": "Pathymetric Mapping", "NATIONAL_BOUNDARY": "National Boundary", "INTERNATIONAL_BOUNDARY": "International Boundary", "Topography 500": "Topography 500", "Topography 250": "Topography 250", "NCW": "NCW", "VisitDate": "Visit Date", "VisitDateFrom": "From Date", "VisitDateTo": "To", "ECOLOGY_CATEGORY": "Ecology Category", "NCW_CATEGORY": "NCW Category", "KINGDOM": "Kingdom", "PHYLUM": "phylum", "CLASS": "Class", "ORDER": "Order", "FAMILY": "Family", "GENUS": "Genus", "SPECIE": "<PERSON><PERSON><PERSON>", "SITE_ID": "Location number", "SIGHTING_DATE": "Sighting Date", "PROTECTED_AREA_CATEGORY": "Protected Area Category", "CREATED_DATE": "Created Date"}