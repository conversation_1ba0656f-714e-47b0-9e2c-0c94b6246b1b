{"mapTools": {"zoomIn": "Zoom In", "zoomOut": "Zoom Out", "fullMap": "Full Extent Map", "move": "Move", "removeAll": "Clear All", "prev": "Previous", "next": "Next", "zoomToAll": "Zoom To All", "area": "Area km2", "resultNum": "Results Number:", "fullScreen": "Full screen", "CloseTools": "Hide tools", "OpenTools": "Show tools", "more": "more", "space": "Space"}, "mapToolsServices": {"logout": "Logout", "login": "<PERSON><PERSON>", "home": "Home", "help": "Help", "compare": "Compare", "compareLayers": "Layers Swipe", "inquiry": "Identify", "nearMe": "Near Me", "generalSiteMap": "General Site Map", "myLocation": "My Location", "select first layer": "Select the left layer", "select second layer": "Select the right layer", "selectSeconedLayerNote": "Select the left layer", "selectFirstLayerNote": "Select the right layer", "mapKey": "Map Legend", "googleMaps": "Google Maps View", "smallMap": "Overview Map", "layersMenu": "Layers menu", "traffic": "Traffic", "Basemap": "Basemap", "gas": "Gas", "water": "Water", "hospitals": "Hospitals", "pharmacies": "Pharmacies", "catering": "Catering", "maintenance": "Maintenance", "moreServ": "Less Services", "lessServ": "More Services", "slow": "Slow", "fast": "Fast", "showImages": "Show media", "barChart": "Bar Chart", "print": "Print", "files": "Files"}, "results": "Results", "km2": "Km2", "layerName": "Layer name", "element": "Element", "cancelFilter": "Cancel filter", "zoomMap": "Zoom map", "clickDescSort": "Click for descending sort ", "clickAscSort": "Click for ascending sort", "cancelSort": "Click to cancel sort", "showMore": "Show More", "showLess": "Show Less", "hideOtherData": "Hide other data", "regions": "Area reserves", "Ecosystems": "Ecosystems", "important_areas": "Important sites and wetlands", "wetlands": "wetlands", "WET_LAND": "wetlands", "ECO_HOTSPOT": "Important site", "important_site": "Important site", "Copyright reserved to the National Center for Wildlife Development 2024": "Copyright reserved to the National Center for Wildlife Development 2024", "attrTblFilter": {"equal": "Equal", "lessOrEqual": "Less then or equal", "lessthan": "Less then", "not": "Not equal", "morethan": "More than", "isNull": "Is Empty", "notNull": "Not Empty", "includes": "Includes", "dateAfter": "Date is after", "dateBefore": "Date is before"}}