import React, { useState, useEffect } from "react";
// import { useNavigate } from "react-router-dom";
import moment from "moment-hijri";
import { useTranslation } from "react-i18next";
import isDeepEqual from "fast-deep-equal/react";

//import Components
import Loader from "../../containers/Loader";
import MapComponent from "../../mapComponents/Map";
import DashHeader from "./DashboardHeader/index";
import DataTable from "./DashboardTbls/DataTable";
import CustomLineChart from "./dashboardCharts/customCharts/CustomLineChart";
import SideTbls from "./DashboardTbls/SideTbls";
import DashboardCountAreaPart from "./DashboardCountAreaPart";
import LeftSideChartsContainer from "./dashboardCharts/LeftChartsComp/LeftSideChartsContainer";
import { Resizable, ResizableBox } from "react-resizable";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlusCircle, faExpandArrowsAlt } from "@fortawesome/free-solid-svg-icons";
//helper funcs
import { getLayerId } from "../../helper/common_func";

import {
  getCountPerTimeContext,
  getDefaultStatistics,
  getDomainValuesForDashboard,
  getTimePeriod,
} from "./helpers/helperFunc";
import { notificationMessage } from "../../helper/utilsFunc";
import { modulesIDs } from "../../helper/constants";
import { Modal } from "antd";

function DashboardPage(props) {
  const { i18n, t } = useTranslation("dashboard", "common", "print");
  const dateDataRef = React.useRef();
  const [isModalOpen, setIsModalOpen] = useState(null);

  // const navigate = useNavigate();
  const [hasAccessToPage, setHasAccessToPage] = React.useState(false);
  const [activeHeatMapFactor, setActiveHeatMapFactor] = useState("");
  // const [routeName, setNavRouteName] = useState("plans");
  const [leftChartsExist, setLeftChartsExist] = useState(false);
  const [loading, setLoading] = useState(true);
  const [map, setMap] = useState();
  const [layersNames, setLayersNames] = useState([]);

  const [mapTablesShow, setMapTablesShow] = useState(false);
  /*Print table */

  /* */
  /**
   * queryData => contains (selectedLayer [refactored],
   * selectedBoundaryType [refactored], selectedTimeContext)
   */
  const [queryData, setQueryData] = useState({
    selectedLayer: props.isIncident ? "incidents940" : "Landbase_Parcel",
    selectedBoundaryType: {
      value: undefined,
      boundariesArr: [],
      selectedBoundary: null,
      preNeededBound: {
        value: "MUNICIPALITY_NAME",
        preNeededBoundariesArr: [],
        selectedPreNeededBoundary: null,
      },
    },
    selectedTimeContext: {
      type: undefined,
      dateData: [],
    },
  });
  /**@description
   * count: related to count section (display count Totol number by default or pie chart with count of selected boundaryType)
   * countPerPeriod: contains {type:String, data:Array, timePeriod:Array, default:Boolean} &
   * related to timeContext line chart at the middle section below map
   * geoType: to check if there is area of not if geoType === polygon
   */
  const [chartsData, setChartsData] = useState({
    count: undefined,
    areaOrLength: {
      value: undefined,
      type: undefined,
    },
    countPerPeriod: undefined,
    // restChartData: [],    //initial total value
    geoType: undefined, // to check if there is area of not if geoType === polygon
  });
  //side tbl data
  const [sideTblData, setSideTblData] = useState({
    title: "",
    data: [],
  });
  if (
    !isDeepEqual(dateDataRef.current, queryData?.selectedTimeContext?.dateData)
  ) {
    dateDataRef.current = queryData?.selectedTimeContext?.dateData;
  }
  //useMemo selectedTimeContext
  const selectedParams = React.useMemo(() => {
    return {
      type: queryData.selectedTimeContext.type,
      dateData: queryData.selectedTimeContext.dateData,
    };
  }, [queryData.selectedTimeContext.type, dateDataRef.current]);
  // if user is not logged navigate to /
  // if logged: intialize layersNames array and get the
  useEffect(() => {
    if (!props.mainData.logged)
      window.open(window.hostURL + "/home/<USER>", "_self");
    else {
      if (map) {
        let isUserHasPermission = props.mainData?.mainFunctions?.find((gr) =>
          gr?.groups_permissions?.find(
            (gp) => gp?.module_id === modulesIDs.incidentsModule
          )
        );

        if (!isUserHasPermission) {
          window.open(window.hostURL + "/home/<USER>", "_self");
        } else setHasAccessToPage(true);
        window.__moment__ = moment;
        //intialize layersNames
        let mapAllLayers = map.__mapInfo.info.$layers.layers;
        // let mapAllTbls = props.map.__mapInfo.info.$layers.tables;
        let layersSetting = props.mainData.layers;
        let layersNames = Object.entries(layersSetting)
          ?.filter((l) => {
            let mapLayerNames = mapAllLayers.map((lay) => lay.name);
            if (
              mapLayerNames.includes(l[1].name) &&
              !l[1].isHiddenOnDashboard
            )
              return l;
            else return undefined;
          })
          ?.map((l) => {
            let mapLayer = mapAllLayers.find((lay) => lay.name === l[0]);
            return {
              englishName: l[0],
              arabicName: l[1].arabicName,
              geoType: mapLayer.geometryType,
              statistics: l[1]?.statistics,
              dashboardCharts: l[1]?.dashboardCharts,
              fields: l[1]?.fields,
              restrictionWhereClause: l[1]?.restrictionWhereClause || undefined,
            };
          });
        let params = {
          changeType: layersNames,
          getTotalData: true,
          boundAdminType: null,
          callBack: handlerToSetQueryDChartsD,
          changedVal: null,
        };
        //set is there left charts for this layer or not
        let isLedtChartsExist = layersNames.find(
          (l) => l.name === queryData.selectedLayer
        )?.dashboardCharts;
        if (isLedtChartsExist?.length) setLeftChartsExist(true);
        /////////////
        setLayersNames(layersNames);
        getDefaultData(params);
      }
      // else {
      //   window.open(window.hostURL + "/home/<USER>", '_self');
      // }
    }
    return () => null;
  }, [map, props.mainData]);

  //in case of change time context
  useEffect(() => {
    if (selectedParams.type && selectedParams.dateData.length) {
      getCountDataPerPeriod((reqData, timePeriod) => {
        if (reqData) {
          let { data } = reqData;
          setChartsData({
            ...chartsData,
            countPerPeriod: {
              type: queryData.selectedTimeContext.type,
              data,
              timePeriod,
              default: false,
            },
          });
        } else {
          // handleClearCalender();
          setChartsData({
            ...chartsData,
            countPerPeriod: {
              type: queryData.selectedTimeContext.type,
              data: [],
              timePeriod,
              default: false,
            },
          });
          notificationMessage(t("dashboard:noDataForEnteredTimeContext"));
        }
        setLoading(false);
      });
    } else return;
  }, [selectedParams]);

  useEffect(() => {
    /**
     * in case of no selected date context 
     */
    if (!selectedParams.dateData.length && map) {
      let { selectedBoundaryType } = queryData;
      let currentLayer = layersNames.find(
        (lay) => lay.name === queryData.selectedLayer
      );
      let layerID = getLayerId(map.__mapInfo, currentLayer.name);
      /**
       * in case of select specific boundary value 
       */
      if (
        selectedBoundaryType.selectedBoundary &&
        selectedBoundaryType.boundariesArr.length
      ) {
        //get data of sub boundary
        let boundAdminType = selectedBoundaryType.value;
        getCountDataSubBound(
          layerID,
          selectedBoundaryType.selectedBoundary,
          boundAdminType,
          selectedBoundaryType,
          true
        );
      }
      /**
       * in case of clear selected boundary 
       */
       else if (
        !selectedBoundaryType.selectedBoundary &&
        selectedBoundaryType.boundariesArr.length
      ) {
        let params = {
          changeType: "selectedBoundary",
          getTotalData:
            chartsData.countPerPeriod?.default ||
            !selectedBoundaryType.selectedBoundary
              ? true
              : false,
          boundAdminType: selectedBoundaryType.value,
          callBack: handlerToSetQueryDChartsD,
          changedVal: null,
        };
        if(selectedBoundaryType.value==='DISTRICT_NAME' && selectedBoundaryType.preNeededBound.preNeededBoundariesArr?.length&&selectedBoundaryType.preNeededBound.selectedPreNeededBoundary)
        params.wClause = `${selectedBoundaryType.preNeededBound.value}=${selectedBoundaryType.preNeededBound.selectedPreNeededBoundary}` 
        getDefaultData(params);
      } 
      /**
       * in case of clear date value
       */
      else {
        let params = {
          changeType: layersNames,
          getTotalData: true,
          boundAdminType: null,
          callBack: handlerToSetQueryDChartsD,
          changedVal: null,
          preserveTypeContext:['monthly', 'daily'].includes(queryData?.selectedTimeContext?.type)
        };
        getDefaultData(params);
      }
    } else return;
  }, [queryData.selectedBoundaryType.selectedBoundary, dateDataRef.current]);

  //in case of change adminBound to apply heat map

  const getCountDataSubBound = async (
    layerID,
    subBoundary,
    boundAdminType,
    selectedBoundaryType,
    isDateCleared
  ) => {
    setLoading(true);
    try {
      let whereStatement = "";
      switch (boundAdminType) {
        case "PLAN_NO":
          whereStatement = subBoundary
            ? `${boundAdminType} = '${subBoundary}'`
            : "";
          break;
        case "DISTRICT_NAME":
          whereStatement = subBoundary
            ? `${boundAdminType} = '${subBoundary}'`
            : "";
          if (
            selectedBoundaryType?.preNeededBound?.value &&
            selectedBoundaryType?.preNeededBound?.selectedPreNeededBoundary
          )
            whereStatement = whereStatement
              ? whereStatement +` AND ${selectedBoundaryType?.preNeededBound?.value} = ${selectedBoundaryType?.preNeededBound?.selectedPreNeededBoundary}`
              : whereStatement;
          break;

        default:
          whereStatement = subBoundary
            ? `${boundAdminType} = '${subBoundary}'`
            : "";
          break;
      }

      let currentLayer = layersNames.find(
        (lay) => lay.name === queryData.selectedLayer
      );

      //getStatisticsForFeatLayer
      let data = await getCountPerTimeContext(
        {
          layerID,
          restrictionWhereClause: currentLayer?.restrictionWhereClause,
        },

        (queryData.selectedTimeContext.type && !isDateCleared)
          ? queryData.selectedTimeContext.type
          : "yearly",
        null,
        whereStatement
      );
      if (data) {
        setChartsData({
          ...chartsData,
          countPerPeriod: {
            ...chartsData.countPerPeriod,
            data: data.data,
            default: true,
          },
        });
        setLoading(false);
      }
    } catch (err) {
      console.log(err);
      notificationMessage(t("common:retrievError"));
      setLoading(false);
    }
  };

  const changeMapTablesShow = (data) => {
    if (data.title === mapTablesShow) {
      setSideTblData();
      setMapTablesShow(false);
    } else {
      setSideTblData(data);
      setMapTablesShow(data.title);
    }
  };
  const closeSideTblHandler = () => {
    setSideTblData();
    setMapTablesShow(false);
  };
  const onMapLoaded = (map) => {
    setMap(map);
  };

  const getDefaultData = async ({
    changeType,
    getTotalData,
    boundAdminType,
    callBack,
    changedVal,
    munDomains,
    wClause,
    preserveTypeContext
  }) => {
    let layersNs = typeof changeType === "object" ? changeType : layersNames;
    let currentLayer = layersNs.find((lay) => {
      let layerName =
        changedVal && changeType === "layer"
          ? changedVal
          : queryData.selectedLayer;
      return lay?.name === layerName;
    });
    if (currentLayer) {
      let { englishName, statistics, geoType, restrictionWhereClause } =
        currentLayer;
      let layerID = getLayerId(map.__mapInfo, englishName);
      let whereClause = restrictionWhereClause;
      if (whereClause && wClause) {
        if (wClause) whereClause = whereClause + " AND " + wClause;
      } else if (wClause) {
        whereClause = wClause;
      }
      let layerObj = {
        layerID,
        statistics,
        boundAdminType,
        getTotalData,
        restrictionWhereClause: whereClause,
      };
      try {
        setLoading(true);
        //count + area (parts)
        let statData =
          changeType === "calendar"
            ? {
                count: chartsData.count,
                areaOrLength: chartsData.areaOrLength,
              }
            : await getDefaultStatistics(layerObj); //count, area or only count based on layer's statistics
        // cout per time (middle part belopw map)
        let yearlyData;
        if (getTotalData)
          yearlyData = await getCountPerTimeContext(
            { layerID, restrictionWhereClause: whereClause },
            "yearly",
            undefined
          );
        callBack(
          {
            statData,
            yearlyData,
          },
          getTotalData,
          geoType,
          changeType,
          changedVal,
          boundAdminType,
          munDomains,
          preserveTypeContext
        );
      } catch (err) {
        notificationMessage(t("common:retrievError"));
        setLoading(false);
      }
    } else {
      notificationMessage(t("common:retrievError"));
      setLoading(false);
    }
  };
  /**
   * (handlerToSetQueryDChartsD, getChartsDataBasedOnStatData, getQueryDataBasedOnStatData)
   * Helpers functions related to getDefaultData
   */
  const handlerToSetQueryDChartsD = (
    data,
    getTotalData,
    geoType,
    changeType,
    changedVal,
    boundAdminType,
    munDomains,
    preserveTypeContext
  ) => {
    let { statData, yearlyData } = data;
    let queryD = getQueryDataBasedOnStatData(
      statData,
      boundAdminType,
      getTotalData,
      changeType,
      preserveTypeContext
    );
    switch (changeType) {
      case "layer":
        queryD.selectedLayer = changedVal;
        break;
      case "boundAdmin":
        queryD.selectedBoundaryType.value = changedVal;
        break;
      case "subBound":
        queryD.selectedBoundaryType.selectedBoundary = changedVal;
        break;
      case "preSubBound":
        if (munDomains) {
          queryD.selectedBoundaryType.selectedBoundary = undefined;
          queryD.selectedBoundaryType.boundariesArr = [];
          queryD.selectedBoundaryType.value = "DISTRICT_NAME";
          queryD.selectedBoundaryType.preNeededBound.preNeededBoundariesArr =
            munDomains;
          queryD.selectedBoundaryType.preNeededBound.selectedPreNeededBoundary =
            undefined;
        } else {
          queryD.selectedBoundaryType.preNeededBound.selectedPreNeededBoundary =
            changedVal;
          if (!changedVal) queryD.selectedBoundaryType.boundariesArr = [];
        }
        break;
      default:
        break;
    }
    setQueryData({ ...queryD });
    if (yearlyData) {
      let chartD = getChartsDataBasedOnStatData(yearlyData);
      setChartsData({
        ...chartD,
        ...statData,
        geoType,
      });
    } else {
      setChartsData({
        ...chartsData,
        ...statData,
        geoType,
      });
    }
    setLoading(false);
  };
  const getChartsDataBasedOnStatData = (yearlyData) => {
    let { data } = yearlyData;

    return {
      ...chartsData,
      countPerPeriod: {
        type: "yearly",
        data,
        timePeriod: undefined,
        default: true,
      },
    };
  };
  const getQueryDataBasedOnStatData = (
    statData,
    boundAdminType,
    getTotalData,
    changeType,
    preserveTypeContext
  ) => {
    let queryDataClone = { ...queryData };
    //if there is a count data array
    if (typeof statData.count === "object" && boundAdminType) {
      queryDataClone.selectedBoundaryType = {
        ...queryDataClone.selectedBoundaryType,
        selectedBoundary:
          queryData.selectedBoundaryType.selectedBoundary !== null
            ? undefined
            : null,
        boundariesArr: statData.count
          .filter((item) => item[boundAdminType])
          .map((item) => {
            return {
              name: item[boundAdminType],
              value:
                boundAdminType === "PLAN_NO"
                  ? item[boundAdminType]
                  : item[boundAdminType + "_Code"],
            };
          }),
      };
      if (getTotalData)
        queryDataClone.selectedTimeContext = {
          type: queryDataClone.selectedTimeContext?.type || undefined,
          dateData: [],
        };
    } else {
      queryDataClone.selectedBoundaryType = {
        ...queryDataClone.selectedBoundaryType,
        selectedBoundary: undefined,
        boundariesArr: [],
        preNeededBound: {
          ...queryDataClone.selectedBoundaryType.preNeededBound,
          preNeededBoundariesArr: [],
          selectedPreNeededBoundary: undefined,
        },
      };
      if (changeType === "layer")
        queryDataClone.selectedBoundaryType.value = undefined;
      if (getTotalData && !preserveTypeContext)
        queryDataClone.selectedTimeContext = {
          type: undefined,
          dateData: [],
        };
        else if(getTotalData && preserveTypeContext){
          queryDataClone.selectedTimeContext = {
            ...queryDataClone.selectedTimeContext,
            dateData: [],
          };
        }
    }
    return queryDataClone;
  };
  /********************************************/
  const getCountDataPerPeriod = async (callBack) => {
    let currentLayer = layersNames.find(
      (lay) => lay.name === queryData.selectedLayer
    );
    let layerID = getLayerId(map.__mapInfo, currentLayer.name);
    let timePeriod = getTimePeriod(queryData);
    // console.log({ timePeriod });
    setLoading(true);
    try {
      let { selectedBoundaryType, selectedTimeContext } = queryData;
      let data = await getCountPerTimeContext(
        {
          layerID,
          restrictionWhereClause: currentLayer?.restrictionWhereClause,
        },
        selectedTimeContext.type,
        timePeriod,
        selectedBoundaryType.selectedBoundary
          ? selectedBoundaryType.value === "PLAN_NO"
            ? `${selectedBoundaryType.value} = '${selectedBoundaryType.selectedBoundary}' `
            : selectedBoundaryType.value === "DISTRICT_NAME"
            ? `${selectedBoundaryType.value} = ${selectedBoundaryType.selectedBoundary} AND ${selectedBoundaryType?.preNeededBound?.value} = ${selectedBoundaryType?.preNeededBound?.selectedPreNeededBoundary}`
            : `${selectedBoundaryType.value} = ${selectedBoundaryType.selectedBoundary}`
          : ""
      );
      callBack(data, timePeriod);
    } catch (err) {
      console.log(err);
      notificationMessage(t("common:retrievError"));
      setLoading(false);
    }
  };

  const handleClearCalender = (isDefault) => {
    let currentLayer = layersNames.find(
      (lay) => lay.name === queryData.selectedLayer
    );
    let querydataToSet = { ...queryData };
    if (isDefault) {
      querydataToSet.selectedTimeContext = {
        type: undefined,
        dateData: [],
      };
    } else if (!chartsData.countPerPeriod?.default) {
      let subBound = queryData.selectedBoundaryType.selectedBoundary;
      let layerID = getLayerId(map.__mapInfo, currentLayer.name);

      //get data of sub boundary
      let boundAdminType = queryData.selectedBoundaryType.value;
      if(boundAdminType && subBound)
      getCountDataSubBound(
        layerID,
        subBound,
        boundAdminType,
        queryData.selectedBoundaryType,
        true
      );

      //reset calendar
      querydataToSet.selectedTimeContext = {
        ...queryData.selectedTimeContext,
        dateData: [],
      };
    } else {
      let subBound = queryData?.selectedBoundaryType?.selectedBoundary || "";
      let layerID = getLayerId(map.__mapInfo, currentLayer.name);

      //get data of sub boundary
      let boundAdminType = queryData?.selectedBoundaryType?.value || "";
      if(subBound && boundAdminType)
      getCountDataSubBound(
        layerID,
        subBound,
        boundAdminType,
        queryData?.selectedBoundaryType,
        true
      );
    }
    setQueryData(querydataToSet);
  };
  const handleAdminBoundChange = async (
    adminBoundary,
    preNeededBoundWhereClause
  ) => {
    let params = {
      changeType: "boundAdmin",
      getTotalData: false,
      boundAdminType: null,
      callBack: handlerToSetQueryDChartsD,
      changedVal: adminBoundary,
    };
    if (!adminBoundary) {
      if (activeHeatMapFactor) setActiveHeatMapFactor("");

      if (!chartsData.count) return;
      else if (typeof chartsData.count === "object") {
        //reset sub boundary array to hide from UI
        if (queryData.selectedTimeContext.dateData.length) {
          params.changeType = "boundAdmin";
          if (preNeededBoundWhereClause) {
            params.wClause =
              typeof preNeededBoundWhereClause === "string"
                ? preNeededBoundWhereClause
                : "";
            params.boundAdminType = queryData.selectedBoundaryType.value;
            params.changeType = "preSubBound";
          }
          getDefaultData(params);
        } else {
          params.changeType = "boundAdmin";
          params.getTotalData = chartsData.countPerPeriod?.default
            ? false
            : true;
          if (preNeededBoundWhereClause) {
            params.wClause =
              typeof preNeededBoundWhereClause === "string"
                ? preNeededBoundWhereClause
                : "";
            params.boundAdminType = queryData.selectedBoundaryType.value;
            params.changeType = "preSubBound";
          }
          getDefaultData(params);
        }
      } else {
        params.changeType = "boundAdmin";
        params.getTotalData = chartsData.countPerPeriod?.default ? false : true;
        getDefaultData(params);
      }
    } else {
      if (adminBoundary && preNeededBoundWhereClause) {
        setActiveHeatMapFactor("count"); //count is default for drawing heat map
        // params.boundAdminType = adminBoundary;
        if (preNeededBoundWhereClause) {
          params.wClause =
            typeof preNeededBoundWhereClause === "string"
              ? preNeededBoundWhereClause
              : "";
          params.boundAdminType = queryData.selectedBoundaryType.value;
          params.changeType = "preSubBound";
        }
        getDefaultData(params);
      } else if (adminBoundary === "DISTRICT_NAME") {
        if (activeHeatMapFactor) setActiveHeatMapFactor("");

        let currentLayer = layersNames.find(
          (lay) => lay.name === queryData.selectedLayer
        );
        let layerID = getLayerId(map.__mapInfo, currentLayer.name);
        setLoading(true);

        let munDomains = await getDomainValuesForDashboard({
          layerID,
          boundAdminType: queryData.selectedBoundaryType.preNeededBound.value,
        });
        setLoading(false);

        let params = {
          changeType: "preSubBound",
          getTotalData: false,
          boundAdminType: null,
          callBack: handlerToSetQueryDChartsD,
          changedVal: queryData.selectedLayer,
          munDomains: munDomains?.data || [],
        };

        getDefaultData(params);
      } else {
        setActiveHeatMapFactor("count"); //count is default for drawing heat map
        params.boundAdminType = adminBoundary;
        getDefaultData(params);
      }
    }
  };
  const handleLayerChange = (layerName) => {
    if (mapTablesShow) {
      setSideTblData();
      setMapTablesShow(false);
    }
    //set is there left charts for this layer or not
    let isLedtChartsExist = layersNames?.find(
      (l) => l.name === layerName
    )?.dashboardCharts;
    if (isLedtChartsExist?.length) setLeftChartsExist(true);
    else if (leftChartsExist) setLeftChartsExist(false);
    //it should reset all dropdown lists and
    //just display the default data (count, area, line bar with count per years),
    //reset select of max 6, min-6 and all in count, area pie charts to be max-6
    //reset select of max-6
    let getTotalData = true;
    let params = {
      changeType: "layer",
      getTotalData,
      boundAdminType: null,
      callBack: handlerToSetQueryDChartsD,
      changedVal: layerName,
    };

    getDefaultData(params);
  };
  const handleChangeSubBoundary = async (subBound) => {
    let { selectedBoundaryType } = queryData;
    let currentLayer = layersNames.find(
      (lay) => lay.name === queryData.selectedLayer
    );
    let layerID = getLayerId(map.__mapInfo, currentLayer.name);
    if (subBound && selectedBoundaryType.boundariesArr.length) {
      //get data of sub boundary
      let boundAdminType = selectedBoundaryType.value;
      await getCountDataSubBound(
        layerID,
        subBound,
        boundAdminType,
        selectedBoundaryType
      );
    } else if (
      subBound === undefined &&
      selectedBoundaryType.boundariesArr.length
    ) {
      let params = {
        changeType: "subBound",
        getTotalData: chartsData.countPerPeriod?.default ? true : false,
        boundAdminType: selectedBoundaryType.value,
        callBack: handlerToSetQueryDChartsD,
        changedVal: subBound,
      };
      getDefaultData(params);
    }
  };
  const [dropNavOpened, setDropNavOpened] = useState("headerHidden");
  const openNavDrop = () => {
    setDropNavOpened("headerShown");
  };
  const closeNavDrop = () => {
    setDropNavOpened("headerHidden");
  };
  return (
    <div className="dashboardPage">
      {loading && <Loader />}

      <DashHeader
        dropNavOpened={dropNavOpened}
        openNavDrop={openNavDrop}
        closeNavDrop={closeNavDrop}
        isIncident={props.isIncident}
        defaultKey="eastern"
        dash
        layersNames={layersNames}
        languageStatus={i18n.language}
        queryData={queryData}
        setQueryData={setQueryData}
        handleClearCalender={handleClearCalender}
        handleLayerChange={handleLayerChange}
        handleAdminBoundChange={handleAdminBoundChange}
        handleChangeSubBoundary={handleChangeSubBoundary}
        map={map}
        dataPerPeriod={chartsData.countPerPeriod}
      />
      <div
        className="dashboard-page-layout no-print"
        id={dropNavOpened == "headerHidden" ? "layotHiddenDrops" : ""}>
        {/**on right: (map+2tbls) 74vw [50vw, 24vw], on left: charts 26vw[13vw+13vw] */}
        {mapTablesShow && sideTblData.title ? (
          <SideTbls
            data={sideTblData}
            closeSideTblHandler={closeSideTblHandler}
          />
        ) : null}
        {/**Map Part */}
        {/* <ResizableBox
          width={200}
          height={200}
          minConstraints={[100, 100]}
          maxConstraints={[300, 300]}> */}
        <div
          className="map-wrapper"
          style={{
            width: !leftChartsExist ? "100vw" : mapTablesShow ? "50vw" : "74vw",
          }}>
          {/**Map Container */}
          <div
            className="dashMap"
            // id={mapTablesShow ? "dashMapHeight" : "dashMapHeightDefault"}
          >
            <MapComponent
              mapload={onMapLoaded}
              mainData={props.mainData}
              currentLayer={queryData.selectedLayer}
              isDashboard={true}
            />
          </div>
          {/**End Map Container */}
          {/**Charts and Sum containers below map */}
          <div className="charts-below-map">
            {!chartsData.count ? (
              <Loader />
            ) : (
              <DashboardCountAreaPart
                type="count"
                chartsData={chartsData}
                queryData={queryData}
                title={t("dashboard:totalCount")}
                map={map}
                activeHeatMapFactor={activeHeatMapFactor}
                setActiveHeatMapFactor={setActiveHeatMapFactor}
                hasArea={
                  chartsData.areaOrLength.type &&
                  typeof chartsData.areaOrLength.value
                }
              />
            )}
             
          
              <div className="mapSquare">
                <FontAwesomeIcon
                  style={{
                    float: "right",
                    marginRight: "10px",
                    marginTop: "15px",
                  }}
                  onClick={() => setIsModalOpen(1)}
                  className="faplusChart"
                  // icon={faPlusCircle}
                  icon={faExpandArrowsAlt}
                />
                <Modal
                  cancelText="إغلاق"
                  className="dashModalZoom"
                  visible={isModalOpen == 1 ? true : false}
                  onCancel={() => setIsModalOpen(null)}
                  footer={null}
                  width={"50%"}>
                   <>
                  {" "}
                  {queryData.selectedBoundaryType.selectedBoundary ? (
                    <h6 style={{textAlign:"center"}}>
                     
                      {t("dashboard:countPerTime")}
                      <br />
                      {
                        queryData.selectedBoundaryType.boundariesArr.find(
                          (i) =>
                            i.value ===
                            queryData.selectedBoundaryType.selectedBoundary
                        )?.name
                      }
                    </h6>
                  ) : (
                    <h6 style={{textAlign:"center"}}>{t("dashboard:countPerTime")}</h6>
                  )}
                </>
                  <CustomLineChart
                    title={
                      queryData?.selectedBoundaryType?.selectedBoundary
                        ? t("dashboard:countPerTime") +
                          " (" +
                          queryData?.selectedBoundaryType?.boundariesArr.find(
                            (i) =>
                              i.value ===
                              queryData?.selectedBoundaryType?.selectedBoundary
                          )?.name +
                          ")"
                        : t("dashboard:countPerTime")
                    }
                    chartData={chartsData?.countPerPeriod||{}}
                  />
                </Modal>
                {/* <h6>أعداد أقطار مخارج المزارعين </h6>  */}
             
                <>
                  {" "}
                  {queryData.selectedBoundaryType.selectedBoundary ? (
                    <h6>
                     
                      {t("dashboard:countPerTime")}
                      <br />
                      {
                        queryData.selectedBoundaryType.boundariesArr.find(
                          (i) =>
                            i.value ===
                            queryData.selectedBoundaryType.selectedBoundary
                        )?.name
                      }
                    </h6>
                  ) : (
                    <h6>{t("dashboard:countPerTime")}</h6>
                  )}
                </>
                <CustomLineChart chartData={chartsData?.countPerPeriod||{}} />
              </div>
            
            {chartsData.geoType === "esriGeometryPoint" ? null : chartsData
                .areaOrLength.type && typeof chartsData.areaOrLength.value ? (
              <DashboardCountAreaPart
                type="areaOrLength"
                chartsData={chartsData}
                queryData={queryData}
                title={t("dashboard:totalArea")}
                map={map}
                activeHeatMapFactor={activeHeatMapFactor}
                setActiveHeatMapFactor={setActiveHeatMapFactor}
                hasCount={chartsData.count}
              />
            ) : null}
          </div>
          {/**End Charts and Sum containers below map */}
        </div>
        {/* </ResizableBox> */}
        {/*************** */}
        {/**Left side Charts */}
        {layersNames.length && queryData?.selectedLayer && map ? (
          <LeftSideChartsContainer
            layersNames={layersNames}
            map={map}
            selectedLayer={queryData.selectedLayer}
            changeMapTablesShow={changeMapTablesShow}
            mapTablesShow={mapTablesShow}
            queryData={queryData}
          />
        ) : null}
        {/**End Left side Charts */}
      </div>
      {/**Details Table on the bottom of the page */}
      <div className="routesData">
        <DataTable
          // routeName={routeName}
          boundAdmin={queryData.selectedBoundaryType.preNeededBound}
          isIncident={props.isIncident}
          chartsData={chartsData}
          title={queryData.selectedBoundaryType.value}
          hasArea={chartsData.areaOrLength.value}
        />
      </div>
      {/**End Details Table on the bottom of the page */}
    </div>
  );
}

export default DashboardPage;
