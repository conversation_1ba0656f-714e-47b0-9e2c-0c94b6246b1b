window.__config = {
  stage: {
    ApiUrl: "https://geoservices2.syadtech.com/wildlifestgapi/",
    hostURL: "https://geoservices2.syadtech.com",
    filesURL: "https://geoservices2.syadtech.com/wildlifefiles/",
    mapServerUrl: "https://geoservices2.syadtech.com/server/rest/services",
    baseURI: "/wildlifeexplorer",
    basename: "wildlifeexplorer",
    mapUrl: {
      ar: "https://geoservices2.syadtech.com/server/rest/services/AppWildLifeExplorer/WildLifeExplorerViewAR/MapServer",
      en: "https://geoservices2.syadtech.com/server/rest/services/AppWildLifeExplorer/WildLifeExplorerViewEN/MapServer",
    },
    guestMapUrl: {
      ar: "https://geoservices2.syadtech.com/server/rest/services/AppWildLifeExplorer/WildLifeExplorerGuestViewAR/MapServer",
      en: "https://geoservices2.syadtech.com/server/rest/services/AppWildLifeExplorer/WildLifeExplorerGuestViewEN/MapServer",
    },
    dashboardMapUrl: {
      ar: "https://geoservices2.syadtech.com/server/rest/services/AppWildLifeExplorer/WildLifeExplorerViewAR/MapServer",
      en: "https://geoservices2.syadtech.com/server/rest/services/AppWildLifeExplorer/WildLifeExplorerViewEN/MapServer",
    },
    printUrl:
      "https://geoservices2.syadtech.com/server/rest/services/Utilities/PrintingTools/GPServer/Export%20Web%20Map%20Task",

    mapPadding: 450,
    paginationCount: 20,
    is3dZoomEnabled: true,
    is3dHighlightOnly: false,
    initialCameraPosition: {
      position: {
        spatialReference: { latestWkid: 3857, wkid: 102100 },
        x: 4591700.624253098,
        y: 1685282.2062162554,
        z: 650262.1994772451,
      },
      heading: 0,
      tilt: 44.999999999999964,
    },
    fullExtent: {
      xmin: 3056086.5707930382,
      ymin: 2131045.6742685484,
      xmax: 6813119.385065023,
      ymax: 3831005.1833304157,
      spatialReference: {
        wkid: 102100,
      },
    },
    dashboardExtent: {
      xmin: 4785834.7959298305,
      ymin: 2933732.235282638,
      xmax: 5946638.743807967,
      ymax: 3658511.948195124,
      spatialReference: {
        wkid: 102100,
      },
    },
    //GP urls
    exportFeaturesGPUrl:
      "https://geoservices2.syadtech.com/server/rest/services/GPWildlife/ExportFeatures/GPServer/ExportFeatures",
    cadToJsonGPUrl:
      "https://geoservices2.syadtech.com/server/rest/services/GPWildlife/CADToJSON/GPServer/CADToJSON",
    shapeFileToJSONGPUrl:
      "https://geoservices2.syadtech.com/server/rest/services/GPWildlife/ShapeFileToJSON/GPServer/ShapeFileToJSON",
    kmlToJSONGPUrl:
      "https://geoservices2.syadtech.com/server/rest/services/GPWildlife/KMLToJSON/GPServer/KMLToJSON",
    //API url
    webApiUrl: "https://geoservices2.syadtech.com/wildlifestgapi/",
    workflowUrl: "https://geoservices2.syadtech.com/gisv2/#/",
    archiveAPIUrl: "https://geoservices2.syadtech.com/GISAPIV2/Archive/",
    archiveFilesUrl: "https://geoservices2.syadtech.com/eastern-archive/",
    momraMapServiceUrl:
      "https://baladybasemap.momra.gov.sa/arcgis/rest/services/Eastern_Area/business_Damam/MapServer",
    incidentMapServiceUrl:
      "https://geoservices2.syadtech.com/arcgisnew/rest/services/940DASHBOARD/MapServer",
    archiveGalleryPrefixUrl:
      "https://archive.eamana.gov.sa/TransactFileUpload/",
    showFeaturesMarker: false,
    showScaleBar: true,
    showXY: false,
    cameraLiveUrl:
      "https://gis-maps.ncw.gov.sa/NCW_CAMS_STRAEAM/NCW_LCAM.aspx?CameraID=",
    blendLayerUrl:
      "https://geoservices2.syadtech.com/server/rest/services/Hosted/BaseMapVT/VectorTileServer",

      
    topography_500k:
      "https://geoservices2.syadtech.com/server/rest/services/Hosted/TopographicMap500K/MapServer",
    ncwBaseMap:
      "https://geoservices2.syadtech.com/server/rest/services/Hosted/BaseMapContourVT/VectorTileServer",
    topography_200k:
      "https://geoservices2.syadtech.com/server/rest/services/Hosted/BaseMapWaterMark/VectorTileServer",
    geolgyMap:
      "https://geoservices2.syadtech.com/server/rest/services/Hosted/Geology/VectorTileServer",


    portalUrl: "http://localhost:3001/wildlife-portal",
    customPrintUrl:
      "https://geoservices2.syadtech.com/server/rest/services/custom_layout_exporting/GPServer/Export%20Web%20Map",
    reqTimeout: 120,
    mobileAppUrl:
      "https://geoservices2.syadtech.com/server/rest/services/AppRassed/RassedViewAR/MapServer",
    mobileAppEditUrl:
      "https://geoservices2.syadtech.com/server/rest/services/AppRassed/RassedEdit/FeatureServer",
    
  },
};
