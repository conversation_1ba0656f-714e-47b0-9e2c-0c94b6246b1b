import React from "react";
import { Carousel } from 'antd'
function ImageCarousel(props) {
    const contentStyle = {
        margin: 0,
        height: '60vh',
        color: '#fff',
        lineHeight: '160px',
        textAlign: 'center',
        background: '#364d79',
      };
      const onChange = (currentSlide) => {
        console.log(currentSlide);
      };
  return <div>
     <Carousel autoplay afterChange={onChange}>
    

        {props?.galleryData?.length ? props?.galleryData?.map((item, idx) => 
        
        // <h3 key={"gArch"+idx} style={contentStyle}>
        <div key={"gArch"+idx} style={contentStyle} >
        <img src={window.archiveGalleryPrefixUrl + item.Path.replaceAll("\\","/")} alt="archive-gallery" />
      </div>
      // </h3>
      
      ) : ''
      }
    
    </Carousel>
  </div>;
}

export default ImageCarousel;
