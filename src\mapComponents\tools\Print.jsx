import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";
import { Fade } from "react-reveal";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import PrintComponent from "../../sidemenu-Components/PrintComponent";
import Draggable from "react-draggable";
import { useTranslation } from "react-i18next";

export default function Print(props) {
  const { i18n } = useTranslation();

  const [x, setX] = React.useState(0);
  const [y, setY] = React.useState(0);

  const handleDrag = (e, position) => {
    setX(position.x);
    setY(position.y);
  };

  return (
    // <Fade left collapse>
    <Draggable
      position={{ x, y }}
      onDrag={handleDrag}
      bounds={{
        left:
          i18n.language === "ar"
            ? -(window.innerWidth - 400)
            : -window.innerWidth,
        top: -300,
        right: i18n.language === "ar" ? window.innerWidth - 400 : 0, // Subtract component width
        bottom: window.innerHeight - 650, // Subtract component height
      }}
    >
      <div
        className="toolsMenu inquiryTool layersMenu leftToolMenu"
        style={{
          overflow: "auto",
          height: "fit-content",
          maxHeight: "500px",
          position: "relative",
          top: "95px",
          minWidth: "400px",
        }}
      >
        <Fade left>
          <span
            style={{
              width: "100%",
              float: "left",
              textAlign: "left",
              marginLeft: "5px",
              marginTop: "-5px",
            }}
          >
            <FontAwesomeIcon
              icon={faTimes}
              style={{
                marginTop: "5px",
                marginRight: "5px",
                cursor: "pointer",
              }}
              onClick={(e) => {
                e.stopPropagation();
                props.closeToolsData();
              }}
            />
          </span>

          <div style={{ overflow: "auto" }}>
            <PrintComponent map={props.map} />
          </div>
        </Fade>
      </div>
    </Draggable>
    // </Fade>
  );
}
